#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试改进后的功能：
1. 环境变量配置
2. 长文本分割处理
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1/document"

def test_env_config():
    """
    测试环境变量配置是否正常工作
    """
    print("🔧 测试环境变量配置...")
    
    # 测试简单的API调用
    request_data = {
        'prompt': '请简单回答：什么是资产负债表？',
        'template_name': '一般企业 (2).xls',
        'selected_files': ['测试.pdf']
    }
    
    try:
        response = requests.post(f'{BASE_URL}/call-qwen-api', json=request_data, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print('✅ 环境变量配置正常，API调用成功')
            print(f'回答长度: {len(data.get("ai_response", ""))} 字符')
            return True
        else:
            print(f'❌ API调用失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {str(e)}')
        return False

def test_long_text_splitting():
    """
    测试长文本分割处理
    """
    print("\n📄 测试长文本分割处理...")
    
    # 创建一个超长的提示词（模拟真实场景）
    long_content = """
    这是一个非常详细的财务报表分析请求。
    
    """ + "这是重复的内容用于增加长度。" * 1000 + """
    
    请根据以上信息生成完整的资产负债表。
    
    具体要求：
    1. 包含所有主要科目
    2. 数据要合理
    3. 格式要标准
    4. 计算要准确
    
    """ + "更多详细要求和说明内容。" * 500 + """
    
    请开始生成报表。
    """
    
    print(f"生成的长文本长度: {len(long_content)} 字符")
    
    request_data = {
        'prompt': long_content,
        'template_name': '一般企业 (2).xls',
        'selected_files': ['长文本测试.pdf']
    }
    
    try:
        print("正在测试长文本处理，请稍候...")
        start_time = time.time()
        
        response = requests.post(f'{BASE_URL}/call-qwen-api', json=request_data, timeout=180)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('ai_response', '')
            
            print(f'✅ 长文本处理成功')
            print(f'处理时间: {processing_time:.2f} 秒')
            print(f'输入长度: {len(long_content)} 字符')
            print(f'输出长度: {len(ai_response)} 字符')
            
            # 检查是否包含分割处理的标识
            if '分割处理汇总' in ai_response:
                print('✅ 检测到分割处理标识，长文本分割功能正常')
            else:
                print('ℹ️ 未检测到分割处理标识，可能文本长度未超过阈值')
            
            # 显示响应预览
            print(f'\n📄 AI回答预览:\n{ai_response[:500]}...')
            
            return True
        else:
            print(f'❌ 长文本处理失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 长文本测试异常: {str(e)}')
        return False

def test_normal_length_text():
    """
    测试正常长度文本（确保不会被错误分割）
    """
    print("\n📝 测试正常长度文本...")
    
    normal_content = """
    请根据以下信息生成资产负债表：
    
    流动资产：
    - 货币资金：1,000,000元
    - 应收账款：500,000元
    - 存货：300,000元
    
    非流动资产：
    - 固定资产：2,000,000元
    - 无形资产：200,000元
    
    流动负债：
    - 应付账款：400,000元
    - 短期借款：300,000元
    
    非流动负债：
    - 长期借款：800,000元
    
    所有者权益：
    - 实收资本：2,000,000元
    - 未分配利润：500,000元
    
    请生成标准格式的资产负债表。
    """
    
    print(f"正常文本长度: {len(normal_content)} 字符")
    
    request_data = {
        'prompt': normal_content,
        'template_name': '一般企业 (2).xls',
        'selected_files': ['正常测试.pdf']
    }
    
    try:
        response = requests.post(f'{BASE_URL}/call-qwen-api', json=request_data, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('ai_response', '')
            
            print(f'✅ 正常文本处理成功')
            print(f'输出长度: {len(ai_response)} 字符')
            
            # 检查是否错误地进行了分割
            if '分割处理汇总' in ai_response:
                print('⚠️ 警告：正常长度文本被错误分割了')
                return False
            else:
                print('✅ 正常长度文本未被分割，处理正确')
            
            print(f'\n📄 AI回答预览:\n{ai_response[:300]}...')
            return True
        else:
            print(f'❌ 正常文本处理失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 正常文本测试异常: {str(e)}')
        return False

if __name__ == "__main__":
    print("改进功能测试脚本")
    print("=" * 60)
    
    # 测试环境变量配置
    env_success = test_env_config()
    
    # 测试正常长度文本
    normal_success = test_normal_length_text()
    
    # 测试长文本分割
    long_success = test_long_text_splitting()
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"环境变量配置: {'✅ 成功' if env_success else '❌ 失败'}")
    print(f"正常文本处理: {'✅ 成功' if normal_success else '❌ 失败'}")
    print(f"长文本分割处理: {'✅ 成功' if long_success else '❌ 失败'}")
    
    if env_success and normal_success and long_success:
        print("\n🎉 所有改进功能测试通过！")
    else:
        print("\n⚠️ 部分功能测试失败，请检查相关配置")
    
    # 保存测试结果
    test_result = {
        "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "env_config": env_success,
        "normal_text": normal_success,
        "long_text_split": long_success,
        "overall_status": "SUCCESS" if all([env_success, normal_success, long_success]) else "PARTIAL_FAILURE"
    }
    
    with open("improved_features_test_result.json", "w", encoding="utf-8") as f:
        json.dump(test_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 测试结果已保存到 improved_features_test_result.json")
