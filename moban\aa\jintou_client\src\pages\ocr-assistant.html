<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财报OCR助手 - 金投大脑</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/due-diligence.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome - 使用CDN资源代替本地资源 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Marked.js 用于Markdown渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <!-- Prism.js用于代码高亮 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <!-- 添加PDF.js库 - 使用本地资源 -->
    <script src="../libs/pdf.js/pdf.min.js"></script>
    <style>
        /* 主体布局 */
        .main-container {
            display: flex;
            height: calc(100vh - 80px);
            overflow: hidden;
        }
        
        /* 左侧功能区 */
        .sidebar {
            width: 280px;
            background-color: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        /* 右侧内容区 */
        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: #fff;
        }
        
        /* 步骤导航 */
        .steps-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .step-item {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .step-item:hover {
            background-color: #e9ecef;
        }
        
        .step-item.active {
            background-color: #007bff;
            color: white;
        }
        
        .step-item.completed {
            background-color: #f8f9fa;
            color: #28a745;
        }
        
        .step-item.completed::after {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .step-item .step-icon {
            margin-right: 10px;
            width: 24px;
            text-align: center;
        }
        
        .step-item .step-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
            background-color: #6c757d;
            color: white;
            margin-right: 10px;
        }
        
        .step-item.active .step-number {
            background-color: white;
            color: #007bff;
        }
        
        /* 内容区块样式 */
        .content-block {
            display: none;
            animation: fadeIn 0.3s ease;
        }
        
        .content-block.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 文件上传区域 */
        .file-upload-area {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload-area:hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        .file-upload-area.highlight {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        .file-list {
            margin-top: 15px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 8px;
            background-color: white;
        }
        
        .file-icon {
            font-size: 24px;
            margin-right: 15px;
            color: #007bff;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: 500;
        }
        
        .file-size {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        /* 图片网格显示 */
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .image-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .image-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .image-container {
            text-align: center;
        }
        
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 3px;
        }
        
        /* 分析结果区域 */
        .analysis-container {
            margin-top: 20px;
        }
        
        .analysis-title {
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        /* 加载指示器 */
        .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            color: #666;
        }
        
        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            margin-right: 10px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 分析结果消息样式 */
        .message {
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e9ecef;
        }
        
        .message-header {
            padding: 12px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
        }
        
        .message-content {
            padding: 15px;
            background-color: #fff;
        }
        
        /* 报表信息表单区域 */
        .report-info-container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
            padding: 0 10px;
            margin-bottom: 15px;
        }
        
        /* 按钮样式 */
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
        
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        
        /* 表格管理区域 */
        .table-management {
            margin-top: 20px;
        }
        
        .table-list {
            list-style: none;
            padding: 0;
        }
        
        .table-item {
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-item-info {
            flex: 1;
        }
        
        .table-item-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .table-item-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .table-item-actions {
            display: flex;
            gap: 5px;
        }
        
        /* 多页图片结果样式 */
        .image-result {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .image-result h6 {
            padding: 10px 15px;
            margin: 0;
            background-color: #f8f9fa;
            font-weight: 500;
        }
        
        .image-content {
            padding: 15px;
            background-color: #fff;
        }
        
        .loading-indicator-inline {
            padding: 10px;
            color: #666;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 10px;
            animation: pulse 1.5s infinite ease-in-out;
        }
        
        @keyframes pulse {
            0% { background-color: #f8f9fa; }
            50% { background-color: #e9ecef; }
            100% { background-color: #f8f9fa; }
        }
        
        /* 表格样式优化 */
        .result-text table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .result-text table th,
        .result-text table td {
            padding: 0.5rem;
            border: 1px solid #dee2e6;
        }
        
        .result-text table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
        
        .result-text table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        /* 流式显示动画 */
        .result-text {
            animation: fadeIn 0.3s ease;
        }
        
        /* 进度显示样式 */
        .progress {
            height: 8px;
            margin-top: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-bar {
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.25em 0.5em;
            border-radius: 10px;
        }
        
        /* 响应式调整 */
        @media (max-width: 992px) {
            .main-container {
                flex-direction: column;
                height: auto;
            }
            
            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
            
            .steps-nav {
                display: flex;
                overflow-x: auto;
            }
            
            .step-item {
                white-space: nowrap;
                border-bottom: none;
                border-right: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <div class="header">
            <div class="logo">
                <img src="../../assets/logo.png" alt="金投大脑">
                <h1>财报OCR助手</h1>
            </div>
            <div class="user-info">
                <span id="username">用户</span>
                <button id="logoutBtn" class="btn btn-outline">退出登录</button>
            </div>
        </div>

        <div class="main-container">
            <!-- 左侧功能区 -->
            <div class="sidebar">
                <ul class="steps-nav">
                    <li class="step-item active" data-step="1">
                        <span class="step-number">1</span>
                        <span>上传文件</span>
                    </li>
                    <li class="step-item" data-step="2">
                        <span class="step-number">2</span>
                        <span>生成原始表格</span>
                    </li>
                    <li class="step-item" data-step="3">
                        <span class="step-number">3</span>
                        <span>生成统一表格</span>
                    </li>
                    <li class="step-item" data-step="4">
                        <span class="step-number">4</span>
                        <span>保存表格</span>
                    </li>
                    <li class="step-item" data-step="5">
                        <span class="step-number">5</span>
                        <span>管理表格</span>
                    </li>
                </ul>
                
                <!-- 操作按钮区 -->
                <div class="p-3 border-top mt-auto">
                    <button id="clearAllBtn" class="btn btn-danger w-100">
                        <i class="fas fa-trash-alt"></i> 清除所有内容
                    </button>
                        </div>
                    </div>
            
            <!-- 右侧内容区 -->
            <div class="content-area">
                <!-- 步骤1: 上传文件 -->
                <div class="content-block active" id="step-content-1">
                    <h3 class="mb-3">上传财务报表</h3>
                    <p class="text-muted mb-4">请上传相关文件（支持PDF、JPG、JPEG、PNG格式）</p>
                    
                    <!-- 模型选择区域 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">模型选择</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="modelType" id="internalModelRadio" value="internal" checked>
                            <label class="form-check-label" for="internalModelRadio">
                                内部模型（Qwen2.5VL-32B）
                            </label>
                        </div>
                            <div class="form-check">
                            <input class="form-check-input" type="radio" name="modelType" id="externalModelRadio" value="external">
                            <label class="form-check-label" for="externalModelRadio">
                                外部模型（千问视觉）
                            </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 文件上传表单 -->
                    <form id="fileUploadForm">
                        <div class="file-upload-area">
                            <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png" style="display:none;">
                            <div class="upload-placeholder">
                                <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #6c757d;"></i>
                                <p class="mt-3 mb-1">点击或拖拽文件到此处</p>
                                <p class="text-muted small">支持PDF和图片格式</p>
                            </div>
                        </div>
                        <div id="fileList" class="file-list">
                            <!-- 上传的文件列表将在这里显示 -->
                    </div>
                    
                    <div class="mt-3">
                            <button type="button" id="processBtn" class="btn btn-primary" disabled>
                            <i class="fas fa-play"></i> 处理文件
                        </button>
                    </div>
                </form>
            </div>

                <!-- 步骤2: 生成原始表格 -->
                <div class="content-block" id="step-content-2">
                    <h3 class="mb-3">原始图片识别</h3>
                    <p class="text-muted mb-4">上传的图片将在此处显示，系统正在自动进行OCR识别</p>
                    
                    <!-- 文件处理区域 -->
                    <div id="fileProcessingArea">
                        <!-- 每个文件的处理结果将在这里显示 -->
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="mt-4">
                        <button id="exportExcelBtn" class="btn btn-success me-2" style="display: none;">
                            <i class="fas fa-file-excel"></i> 导出Excel
                        </button>
                        <button id="generateUnifiedTableBtn" class="btn btn-primary" style="display: none;">
                            <i class="fas fa-table"></i> 生成统一表格
                        </button>
                    </div>
                </div>
                
                <!-- 步骤3: 生成统一表格 -->
                <div class="content-block" id="step-content-3">
                    <h3 class="mb-3">生成统一表格</h3>
                    <p class="text-muted mb-4">将原始OCR识别结果转换为标准科目的统一表格</p>
                    
                    <!-- 统一表格区域 -->
                    <div id="unifiedTableContainer" class="analysis-container">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> 请先从原始表格生成统一表格
                    </div>
                    
                        <!-- 统一表格内容 -->
                        <div id="unifiedTableContent">
                            <!-- 统一表格将在这里显示 -->
                        </div>
                    </div>
                </div>
                
                <!-- 步骤4: 保存表格 -->
                <div class="content-block" id="step-content-4">
                    <h3 class="mb-3">保存报表信息</h3>
                    <p class="text-muted mb-4">填写报表相关信息并保存</p>
                
                <!-- 报表信息区域 -->
                    <div id="reportInfoContainer" class="report-info-container">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="companyNameInput">公司名称</label>
                            <input type="text" id="companyNameInput" class="form-control" placeholder="输入公司名称">
                        </div>
                        <div class="form-group">
                            <label for="reportYearInput">报表年份</label>
                            <input type="text" id="reportYearInput" class="form-control" placeholder="YYYY">
                        </div>
                        <div class="form-group">
                            <label for="reportMonthInput">报表月份</label>
                            <select id="reportMonthInput" class="form-control">
                                <option value="">选择月份</option>
                                <option value="01">1月</option>
                                <option value="02">2月</option>
                                <option value="03">3月</option>
                                <option value="04">4月</option>
                                <option value="05">5月</option>
                                <option value="06">6月</option>
                                <option value="07">7月</option>
                                <option value="08">8月</option>
                                <option value="09">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="reportNotesInput">备注</label>
                            <textarea id="reportNotesInput" class="form-control" rows="3" placeholder="添加备注信息"></textarea>
                        </div>
                        <div class="mt-3">
                            <button id="saveReportBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存报表数据
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤5: 管理表格 -->
                <div class="content-block" id="step-content-5">
                    <h3 class="mb-3">表格管理</h3>
                    <p class="text-muted mb-4">查看和管理已保存的表格数据</p>
                    
                    <!-- 表格管理区域 -->
                    <div class="table-management">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">已保存的报表</h5>
                            <button id="refreshTableListBtn" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        
                        <div id="tableListContainer">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 加载已保存的表格数据...
                            </div>
                            
                            <!-- 表格列表将在这里显示 -->
                            <ul class="table-list" id="savedTablesList" style="display: none;">
                                <!-- 示例项 -->
                                <li class="table-item">
                                    <div class="table-item-info">
                                        <div class="table-item-title">示例公司财务报表</div>
                                        <div class="table-item-meta">2023年12月 | 创建于 2023-12-15</div>
                                    </div>
                                    <div class="table-item-actions">
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-file-excel"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                    </button>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/ocr-unified-table.js"></script>
    <script src="../js/ocr-assistant.js"></script>
    <!-- 添加XLSX.js用于Excel导出 - 使用多个路径确保加载成功 -->
    <script>
        // 定义要尝试加载的脚本路径
        const scriptPaths = [
            '../libs/xlsx.full.min.js',                 // 相对路径 - 本地libs目录
            '../../node_modules/xlsx/dist/xlsx.full.min.js'  // 相对路径 - node_modules目录
        ];
        
        // 尝试加载脚本的函数
        function loadScript(index) {
            if (index >= scriptPaths.length) {
                // 所有脚本都尝试了但都失败了
                console.error('无法加载XLSX库');
                alert('Excel导出库加载失败，请刷新页面后重试');
                return;
            }
            
            const script = document.createElement('script');
            script.src = scriptPaths[index];
            script.onload = function() {
                console.log('XLSX库加载成功:', scriptPaths[index]);
            };
            script.onerror = function() {
                // 当前路径加载失败，尝试下一个
                console.warn('XLSX库加载失败:', scriptPaths[index]);
                loadScript(index + 1);
            };
            document.body.appendChild(script);
        }
        
        // 开始尝试加载脚本
        loadScript(0);
    </script>
</body>
</html> 