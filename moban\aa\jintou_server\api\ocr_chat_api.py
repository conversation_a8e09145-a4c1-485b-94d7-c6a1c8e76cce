#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 聊天API服务
提供AI智能问答功能的API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Optional
import time
import os
import json
import logging
import httpx
import traceback

# 导入安全工具
from jintou_server.security.jwt_handler import get_current_user

# 创建路由
router = APIRouter(
    prefix="/api/ocr",
    tags=["OCR聊天服务"],
    responses={404: {"description": "Not found"}},
)

# 配置日志
logger = logging.getLogger("jintou_brain.chat")

# API配置
API_URL = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
API_KEY = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
API_MODEL = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

# 数据模型
class Message(BaseModel):
    role: str = Field(..., description="消息角色，例如'user'或'assistant'")
    content: str = Field(..., description="消息内容")

class ChatRequest(BaseModel):
    messages: List[dict] = Field(..., description="对话历史消息列表")
    temperature: float = Field(0.7, description="温度参数，控制回答的随机性")
    max_tokens: Optional[int] = Field(4000, description="最大生成token数")

# 流式聊天API端点
@router.post("/chat/stream")
async def chat_stream(
    request: Request,
    current_user: dict = Depends(get_current_user),
):
    """
    流式智能问答API
    接收用户消息并以流式方式返回AI回复
    """
    try:
        # 从请求体中获取JSON数据
        body = await request.json()
        
        # 检查是否有message参数（旧版本API）或messages参数（新版本API）
        messages = body.get("messages", None)
        message = body.get("message", None)
        session_id = body.get("session_id", None)
        
        # 如果提供了message和session_id，但没有提供messages，则构造messages
        if message is not None and not messages:
            messages = [{"role": "user", "content": message}]
            logger.info(f"从message参数构造messages: {message[:50]}...")
        
        temperature = body.get("temperature", 0.7)
        max_tokens = body.get("max_tokens", 4000)
        
        # 验证消息列表
        if not messages:
            error_msg = "缺少必要参数：messages或message"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)
        
        logger.info(f"收到聊天请求，消息数量: {len(messages)}")
        
        # 返回流式响应
        return StreamingResponse(
            deepseek_stream_chat(messages, temperature, max_tokens),
            media_type="text/event-stream"
        )
            
    except Exception as e:
        error_msg = f"Stream chat error: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return StreamingResponse(
            error_stream(str(e)),
            media_type="text/event-stream"
        )

async def deepseek_stream_chat(messages, temperature=0.7, max_tokens=4000):
    """
    调用Deepseek API进行流式聊天
    """
    try:
        logger.info(f"开始调用Deepseek流式API，max_tokens={max_tokens}")
        
        # 强制限制max_tokens，确保不超过API限制
        if max_tokens > 20000:
            max_tokens = 20000
            logger.info(f"max_tokens过大，已调整为{max_tokens}")
        
        # 消息格式处理
        formatted_messages = []
        for msg in messages:
            # 确保每个消息都有role和content字段
            if isinstance(msg, dict) and "role" in msg and "content" in msg:
                # 统一role名称 (assistant/user/system)
                role = msg["role"]
                if role not in ["assistant", "user", "system"]:
                    role = "user"  # 默认为user
                
                formatted_messages.append({
                    "role": role,
                    "content": msg["content"]
                })
        
        # 检查是否有有效消息
        if not formatted_messages:
            error_msg = "没有有效的消息内容"
            logger.error(error_msg)
            yield f"data: {json.dumps({'error': error_msg})}\n\n"
            return
            
        logger.info(f"格式化后的消息数量: {len(formatted_messages)}")
        
        # 构建请求体
        request_body = {
            "model": API_MODEL,
            "messages": formatted_messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        logger.info(f"API请求参数: {json.dumps(request_body, ensure_ascii=False)[:200]}...")
        
        # 发送请求
        async with httpx.AsyncClient(timeout=300.0) as client:
            async with client.stream(
                "POST",
                API_URL,
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {API_KEY}"
                }
            ) as response:
                logger.info(f"API 响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    error_msg = await response.aread()
                    logger.error(f"API错误: {response.status_code} - {error_msg}")
                    yield f"data: {json.dumps({'error': f'API错误: {error_msg}'})}\n\n"
                    return
                
                # 流式处理响应
                buffer = ""
                assistant_message = {"role": "assistant", "content": ""}
                total_tokens = 0
                
                async for chunk in response.aiter_bytes():
                    chunk_text = chunk.decode("utf-8")
                    logger.debug(f"收到数据块: {chunk_text[:50]}...")
                    buffer += chunk_text
                    
                    # 处理可能的多行数据
                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)
                        if line.startswith("data: "):
                            data = line[6:]  # 去掉 "data: " 前缀
                            if data == "[DONE]":
                                logger.info(f"流式响应完成，总计tokens: {total_tokens}")
                                continue
                                
                            try:
                                json_data = json.loads(data)
                                if "choices" in json_data and len(json_data["choices"]) > 0:
                                    delta = json_data["choices"][0].get("delta", {})
                                    if "content" in delta and delta["content"]:
                                        content = delta["content"]
                                        assistant_message["content"] += content
                                        total_tokens += 1
                                        # 发送小块数据，改进流式体验
                                        yield f"data: {json.dumps({'content': content})}\n\n"
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析错误: {e}, 数据: {data[:100]}...")
                                continue
                
                # 确保最后的内容也被发送
                if buffer and buffer.startswith("data: "):
                    try:
                        data = buffer[6:]  # 去掉 "data: " 前缀
                        if data != "[DONE]":
                            json_data = json.loads(data)
                            if "choices" in json_data and len(json_data["choices"]) > 0:
                                delta = json_data["choices"][0].get("delta", {})
                                if "content" in delta and delta["content"]:
                                    content = delta["content"]
                                    yield f"data: {json.dumps({'content': content})}\n\n"
                    except Exception as e:
                        logger.warning(f"处理最后的缓冲区时出错: {e}")
                
                # 发送结束标记
                yield f"data: [DONE]\n\n"
    
    except Exception as e:
        error_msg = f"API error: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        yield f"data: {json.dumps({'error': str(e)})}\n\n"

async def error_stream(error_message):
    """
    生成错误流
    """
    yield f"data: {json.dumps({'error': error_message})}\n\n"

# 非流式聊天API端点
@router.post("/chat/completion")
async def chat_completion(request: Request, current_user: dict = Depends(get_current_user)):
    """
    非流式智能问答API
    接收用户消息并返回AI回复
    """
    try:
        # 从请求体中获取JSON数据
        body = await request.json()
        
        # 检查是否有message参数（旧版本API）或messages参数（新版本API）
        messages = body.get("messages", None)
        message = body.get("message", None)
        
        # 如果提供了message但没有提供messages，则构造messages
        if message is not None and not messages:
            messages = [{"role": "user", "content": message}]
            logger.info(f"从message参数构造messages: {message[:50]}...")
        
        temperature = body.get("temperature", 0.7)
        max_tokens = body.get("max_tokens", 4000)
        
        # 验证消息列表
        if not messages:
            error_msg = "缺少必要参数：messages或message"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)
        
        logger.info(f"收到非流式聊天请求，消息数量: {len(messages)}")
        
        # 消息格式处理
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, dict) and "role" in msg and "content" in msg:
                role = msg["role"]
                if role not in ["assistant", "user", "system"]:
                    role = "user"
                
                formatted_messages.append({
                    "role": role,
                    "content": msg["content"]
                })
        
        # 检查是否有有效消息
        if not formatted_messages:
            raise HTTPException(status_code=400, detail="没有有效的消息内容")
        
        # 构建请求体
        request_body = {
            "model": API_MODEL,
            "messages": formatted_messages,
            "stream": False,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        logger.info(f"API请求参数: {json.dumps(request_body, ensure_ascii=False)[:200]}...")
        
        # 发送请求
        async with httpx.AsyncClient(timeout=300.0) as client:
            response = await client.post(
                API_URL,
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {API_KEY}"
                }
            )
            
            if response.status_code != 200:
                error_msg = response.text
                logger.error(f"API错误: {response.status_code} - {error_msg}")
                raise HTTPException(status_code=500, detail=f"API错误: {error_msg}")
            
            # 解析响应
            response_data = response.json()
            logger.info(f"API响应: {json.dumps(response_data, ensure_ascii=False)[:200]}...")
            
            # 提取内容
            if "choices" in response_data and len(response_data["choices"]) > 0:
                content = response_data["choices"][0]["message"]["content"]
                return {
                    "id": f"chatcmpl-{int(time.time())}",
                    "created": int(time.time()),
                    "model": API_MODEL,
                    "content": content
                }
            else:
                raise HTTPException(status_code=500, detail="API返回格式错误")
    
    except Exception as e:
        error_msg = f"Chat completion error: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def check_status():
    """
    检查API状态
    """
    try:
        return {
            "status": "ok",
            "timestamp": time.time(),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"状态检查失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": time.time()
        } 