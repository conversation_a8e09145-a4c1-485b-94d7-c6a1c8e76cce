/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
"use strict";

var _annotation_storage = require("../../display/annotation_storage.js");
describe("AnnotationStorage", function () {
  describe("GetOrDefaultValue", function () {
    it("should get and set a new value in the annotation storage", function () {
      const annotationStorage = new _annotation_storage.AnnotationStorage();
      let value = annotationStorage.getValue("123A", {
        value: "hello world"
      }).value;
      expect(value).toEqual("hello world");
      annotationStorage.setValue("123A", {
        value: "hello world"
      });
      value = annotationStorage.getValue("123A", {
        value: "an other string"
      }).value;
      expect(value).toEqual("hello world");
    });
    it("should get set values and default ones in the annotation storage", function () {
      const annotationStorage = new _annotation_storage.AnnotationStorage();
      annotationStorage.setValue("123A", {
        value: "hello world",
        hello: "world"
      });
      const result = annotationStorage.getValue("123A", {
        value: "an other string",
        world: "hello"
      });
      expect(result).toEqual({
        value: "hello world",
        hello: "world",
        world: "hello"
      });
    });
  });
  describe("SetValue", function () {
    it("should set a new value in the annotation storage", function () {
      const annotationStorage = new _annotation_storage.AnnotationStorage();
      annotationStorage.setValue("123A", {
        value: "an other string"
      });
      const value = annotationStorage.getAll()["123A"].value;
      expect(value).toEqual("an other string");
    });
    it("should call onSetModified() if value is changed", function () {
      const annotationStorage = new _annotation_storage.AnnotationStorage();
      let called = false;
      const callback = function () {
        called = true;
      };
      annotationStorage.onSetModified = callback;
      annotationStorage.setValue("asdf", {
        value: "original"
      });
      expect(called).toBe(true);
      annotationStorage.setValue("asdf", {
        value: "modified"
      });
      expect(called).toBe(true);
      called = false;
      annotationStorage.setValue("asdf", {
        value: "modified"
      });
      expect(called).toBe(false);
    });
  });
  describe("ResetModified", function () {
    it("should call onResetModified() if set", function () {
      const annotationStorage = new _annotation_storage.AnnotationStorage();
      let called = false;
      const callback = function () {
        called = true;
      };
      annotationStorage.onResetModified = callback;
      annotationStorage.setValue("asdf", {
        value: "original"
      });
      annotationStorage.resetModified();
      expect(called).toBe(true);
      called = false;
      annotationStorage.setValue("asdf", {
        value: "original"
      });
      annotationStorage.resetModified();
      expect(called).toBe(false);
      annotationStorage.setValue("asdf", {
        value: "modified"
      });
      annotationStorage.resetModified();
      expect(called).toBe(true);
    });
  });
});