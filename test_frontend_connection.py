#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试前端和后端连接
"""

import requests
import json
import time

def test_backend_direct():
    """直接测试后端"""
    print("🔧 直接测试后端...")
    
    try:
        response = requests.get('http://localhost:8000/api/v1/document/report-templates', timeout=10)
        print(f"✅ 后端直接访问成功")
        print(f"状态码: {response.status_code}")
        data = response.json()
        if data.get('success'):
            templates = data.get('templates', [])
            print(f"模板数量: {len(templates)}")
            if templates:
                print(f"第一个模板: {templates[0].get('display_name')}")
        return True
    except Exception as e:
        print(f"❌ 后端直接访问失败: {e}")
        return False

def test_frontend_proxy():
    """通过前端代理测试"""
    print("\n🌐 通过前端代理测试...")
    
    try:
        # 通过前端代理访问
        response = requests.get('http://localhost:5173/api/v1/document/report-templates', timeout=10)
        print(f"✅ 前端代理访问成功")
        print(f"状态码: {response.status_code}")
        data = response.json()
        if data.get('success'):
            templates = data.get('templates', [])
            print(f"模板数量: {len(templates)}")
            if templates:
                print(f"第一个模板: {templates[0].get('display_name')}")
        return True
    except Exception as e:
        print(f"❌ 前端代理访问失败: {e}")
        return False

def test_generate_prompt():
    """测试生成提示词"""
    print("\n📝 测试生成提示词...")
    
    test_data = {
        "selected_files": ["测试文件.pdf"],
        "template_name": "一般企业 (2).xls",
        "analysis_results": [
            {
                "filename": "测试文件.pdf",
                "content": "这是测试内容",
                "parsed_result": {
                    "是否为报表": "是",
                    "报表类型": "资产负债表",
                    "报表主体": "测试公司",
                    "报表时期": "2023年12月31日"
                },
                "selected": True
            }
        ]
    }
    
    try:
        # 直接访问后端
        response = requests.post('http://localhost:8000/api/v1/document/generate-prompt', 
                               json=test_data, timeout=30)
        print(f"✅ 生成提示词成功")
        print(f"状态码: {response.status_code}")
        data = response.json()
        if data.get('success'):
            prompt = data.get('prompt', '')
            print(f"提示词长度: {len(prompt)} 字符")
            print(f"提示词预览: {prompt[:100]}...")
        return True
    except Exception as e:
        print(f"❌ 生成提示词失败: {e}")
        return False

def main():
    print("🚀 开始测试前端后端连接...")
    print("=" * 60)
    
    # 测试后端直接访问
    backend_ok = test_backend_direct()
    
    # 测试前端代理
    proxy_ok = test_frontend_proxy()
    
    # 测试生成提示词
    prompt_ok = test_generate_prompt()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"后端直接访问: {'✅ 成功' if backend_ok else '❌ 失败'}")
    print(f"前端代理访问: {'✅ 成功' if proxy_ok else '❌ 失败'}")
    print(f"生成提示词: {'✅ 成功' if prompt_ok else '❌ 失败'}")
    
    if backend_ok and proxy_ok and prompt_ok:
        print("\n🎉 所有测试通过！前端应该能正常工作")
    else:
        print("\n⚠️ 存在问题，需要进一步调试")

if __name__ == "__main__":
    main()
