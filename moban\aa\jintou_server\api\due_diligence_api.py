#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 尽调助手API
提供财务报表识别与分析功能
"""

from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Body, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Union
import os
import time
import json
import logging
import numpy as np
import cv2
import io
import base64
import re
import requests
import httpx
import pandas as pd
import uuid
from PIL import Image, ImageDraw
import fitz  # PyMuPDF库，用于PDF处理
import asyncio

# 导入环境变量
from os import environ

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("due_diligence_api")

# 创建路由
router = APIRouter(prefix="/api/due-diligence", tags=["尽调助手"])

# 创建临时文件目录
TEMP_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "temp")
os.makedirs(TEMP_DIR, exist_ok=True)

# OCR服务配置 - 与test_ocr_service.py保持一致
OCR_SERVICE_URL = "http://10.0.10.42:5005/invoke"  # base64接口

# 金投大脑API配置
# 使用提供的固定值，确保连接正常
if not environ.get("DEEPSEEK_API_URL"):
    environ["DEEPSEEK_API_URL"] = "http://**********:8000/v1/chat/completions"
if not environ.get("DEEPSEEK_API_KEY"):
    environ["DEEPSEEK_API_KEY"] = "szjf@2025"
if not environ.get("DEEPSEEK_MODEL"):
    environ["DEEPSEEK_MODEL"] = "/models/Qwen3-32B"

DEEPSEEK_API_URL = environ.get("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
DEEPSEEK_API_KEY = environ.get("DEEPSEEK_API_KEY", "szjf@2025")
DEEPSEEK_MODEL = environ.get("DEEPSEEK_MODEL", "/models/Qwen3-32B")

# 存储处理中的任务
processing_tasks = {}

# 检查API配置
def check_api_config():
    """检查API配置是否有效"""
    if not DEEPSEEK_API_URL or DEEPSEEK_API_URL == "http://localhost:8000/v1/chat/completions":
        logger.warning("⚠️ DEEPSEEK_API_URL 未配置或使用默认值，可能导致连接问题")
    
    if not DEEPSEEK_API_KEY or DEEPSEEK_API_KEY == "sk-no-key-available":
        logger.warning("⚠️ DEEPSEEK_API_KEY 未配置或使用默认值，可能导致认证问题")
    
    if not DEEPSEEK_MODEL or DEEPSEEK_MODEL == "deepseek-chat":
        logger.warning("⚠️ DEEPSEEK_MODEL 未配置或使用默认值，可能导致模型加载问题")
    
    # 检查网络连接
    try:
        import socket
        api_host = DEEPSEEK_API_URL.split("://")[1].split("/")[0].split(":")[0]
        socket.gethostbyname(api_host)
        logger.info(f"✅ 成功解析API主机: {api_host}")
    except Exception as e:
        logger.warning(f"⚠️ 无法解析API主机: {e}")
    
    logger.info(f"API配置: URL={DEEPSEEK_API_URL}, MODEL={DEEPSEEK_MODEL}")

# 在服务启动时检查配置
check_api_config()

# 响应模型
class OCRResponse(BaseModel):
    """OCR识别响应模型"""
    code: int = 0
    message: str = "识别成功"
    device: str = "GPU"
    duration: Optional[str] = None
    original: Optional[List[Dict[str, Any]]] = None
    result: Optional[str] = None
    image_base64: Optional[str] = None

def image_to_base64(image_bytes, image_type="jpeg"):
    """
    将图像字节数据转换为base64编码
    Args:
        image_bytes: 图像字节数据
        image_type: 图像类型
    Returns:
        base64编码的图像数据
    """
    try:
        # 编码为base64
        encoded_string = base64.b64decode(image_bytes) if isinstance(image_bytes, str) else image_bytes
        encoded_string = base64.b64encode(encoded_string).decode('utf-8')
        
        # 添加前缀
        return f"data:image/{image_type};base64,{encoded_string}"
    except Exception as e:
        logger.error(f"转换为base64时出错: {e}")
        return None

def pil_image_to_base64(image, format="JPEG"):
    """
    将PIL图像转换为base64编码
    Args:
        image: PIL.Image对象
        format: 图像格式，默认JPEG
    Returns:
        base64编码的图像数据
    """
    try:
        # 创建内存缓冲区
        buffer = io.BytesIO()
        
        # 保存图像到缓冲区
        image.save(buffer, format=format)
        
        # 获取字节数据
        img_bytes = buffer.getvalue()
        
        # 编码为base64
        encoded_string = base64.b64encode(img_bytes).decode('utf-8')
        
        # 添加前缀
        image_type = format.lower()
        if image_type == 'jpg':
            image_type = 'jpeg'
            
        return f"data:image/{image_type};base64,{encoded_string}"
    
    except Exception as e:
        logger.error(f"转换PIL图像为base64时出错: {e}")
        return None

def base64_to_pil_image(image_base64):
    """
    将Base64编码的图像数据转换为PIL图像
    Args:
        image_base64: Base64编码的图像数据
    Returns:
        PIL.Image对象
    """
    try:
        # 检查输入是否有效
        if not image_base64:
            logger.error("输入的Base64字符串为空")
            return None
        
        # 检查是否是PDF数据
        is_pdf = False
        if "data:application/pdf" in image_base64:
            logger.warning("检测到PDF格式，无法直接转换为图像")
            is_pdf = True
            return None
        
        # 移除数据URL前缀
        if "base64," in image_base64:
            # 提取MIME类型以便调试
            mime_type = image_base64.split(';')[0].split(':')[1] if ';' in image_base64 else "未知类型"
            logger.info(f"检测到MIME类型: {mime_type}")
            
            # 移除前缀
            image_data = image_base64.split('base64,')[1]
        else:
            logger.warning("未检测到标准Base64前缀，尝试直接解码")
            image_data = image_base64
        
        # 解码Base64
        try:
            image_bytes = base64.b64decode(image_data)
            logger.info(f"成功解码Base64数据，大小: {len(image_bytes)} 字节")
        except Exception as decode_error:
            logger.error(f"Base64解码失败: {decode_error}")
            return None
        
        # 创建内存缓冲区
        buffer = io.BytesIO(image_bytes)
        
        # 尝试不同的图像格式
        for image_format in ['JPEG', 'PNG', 'GIF', 'BMP', 'TIFF']:
            try:
                buffer.seek(0)  # 重置缓冲区位置
                image = Image.open(buffer)
                logger.info(f"成功以{image_format}格式打开图像，尺寸: {image.size}")
                return image
            except Exception as format_error:
                logger.warning(f"尝试{image_format}格式失败: {format_error}")
                continue
        
        # 如果所有格式都失败，尝试使用OpenCV
        try:
            buffer.seek(0)
            nparr = np.frombuffer(image_bytes, np.uint8)
            img_np = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if img_np is None:
                logger.error("OpenCV无法解码图像数据")
                return None
            
            # 转换BGR到RGB
            img_rgb = cv2.cvtColor(img_np, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(img_rgb)
            logger.info(f"使用OpenCV成功解码图像，尺寸: {image.size}")
            return image
        except Exception as cv_error:
            logger.error(f"OpenCV解码失败: {cv_error}")
            return None
        
    except Exception as e:
        logger.error(f"转换Base64为PIL图像时出错: {e}")
        return None

def call_ocr_service(image_base64):
    """
    调用OCR服务 - 完全按照test_ocr_service.py的方式
    Args:
        image_base64: Base64编码的图像数据
    Returns:
        OCR识别结果
    """
    # 准备请求数据
    payload = {
        "image_base64": image_base64
    }
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        # 发送请求
        start_time = time.time()
        logger.info("正在发送OCR请求...")
        response = requests.post(OCR_SERVICE_URL, headers=headers, json=payload)
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            logger.info(f"OCR请求成功! 耗时: {elapsed:.2f} 秒")
            # 解析JSON
            result = response.json()
            return result
        else:
            logger.error(f"OCR请求失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            raise HTTPException(
                status_code=503,
                detail=f"OCR服务请求失败: {response.text}"
            )
    except requests.RequestException as e:
        logger.error(f"请求OCR服务时出错: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"OCR服务连接失败: {str(e)}"
        )

def identify_company_names(ocr_result):
    """
    从OCR结果中识别企业名称
    Args:
        ocr_result: OCR识别结果
    Returns:
        企业名称列表，每个元素为(名称, 位置)元组
    """
    company_names = []
    
    # 检查OCR结果格式
    if not ocr_result or "original" not in ocr_result:
        logger.error("无效的OCR结果")
        return company_names
    
    # 获取文本块列表
    text_blocks = ocr_result["original"]
    
    # 企业名称的正则表达式模式
    company_patterns = [
        r'[\u4e00-\u9fa5]{2,}(?:公司|集团|企业|有限|责任|股份|合伙|商贸|科技|信息|咨询|服务|投资|金融|证券|保险|银行|资产|管理|控股)',
        r'[\u4e00-\u9fa5]+(?:有限公司|集团公司|股份有限公司)'
    ]
    
    # 排除的财务术语列表
    exclude_terms = [
        "流动资产", "非流动资产", "固定资产", "无形资产", "递延所得税资产", "其他资产", 
        "抵债资产", "可供出售金融资产", "持有至到期投资", "长期股权投资", "其他流动资产", 
        "其他非流动资产", "未到期责任", "提取未到期责任", "摊回未到期责任", "业务及管理",
        "归属于母公司"
    ]
    
    # 遍历每个文本块
    for block in text_blocks:
        # 获取文本内容
        text = block.get("text", "")
        
        # 跳过排除的财务术语
        if text in exclude_terms:
            continue
        
        # 获取位置信息 (box字段)
        box = block.get("box", [])
        
        # 检查是否匹配企业名称模式
        is_company_name = False
        matched_name = ""
        
        # 首先检查完整文本是否是企业名称
        for pattern in company_patterns:
            if re.fullmatch(pattern, text):
                is_company_name = True
                matched_name = text
                break
        
        # 如果整个文本不是企业名称，尝试在文本中查找企业名称
        if not is_company_name:
            for pattern in company_patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    company_name = match.group(0)
                    # 再次检查是否在排除列表中
                    if company_name not in exclude_terms:
                        is_company_name = True
                        matched_name = company_name
                        break
        
        # 如果识别到企业名称
        if is_company_name:
            logger.info(f"找到企业名称: {matched_name}")
            
            # 如果有box信息，转换为矩形坐标
            position = None
            if box and len(box) == 4:  # box格式为四个点的坐标
                # 提取四个点的坐标
                points = box
                
                # 计算矩形的左上角和右下角坐标
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                x0 = min(x_coords)
                y0 = min(y_coords)
                x1 = max(x_coords)
                y1 = max(y_coords)
                
                position = [x0, y0, x1, y1]
                logger.info(f"企业名称位置: {position}")
            
            # 保存企业名称和位置
            company_names.append((matched_name, position))
    
    return company_names

def mask_company_names(image, company_names, mask_color=(255, 0, 0)):
    """
    在图像上遮盖企业名称
    Args:
        image: PIL.Image对象
        company_names: 企业名称列表，每个元素为(名称, 位置)元组
        mask_color: 遮盖颜色，默认红色
    Returns:
        处理后的图像
    """
    # 创建一个可绘制的图像副本
    masked_image = image.copy()
    draw = ImageDraw.Draw(masked_image)
    
    # 遍历每个企业名称
    for name, position in company_names:
        if position:
            # 提取位置坐标
            x0, y0, x1, y1 = position
            
            # 增加矩形区域的大小，确保完全覆盖文字
            padding = 2
            x0 = max(0, x0 - padding)
            y0 = max(0, y0 - padding)
            x1 = min(image.width, x1 + padding)
            y1 = min(image.height, y1 + padding)
            
            # 绘制矩形遮盖，使用完全不透明的颜色
            draw.rectangle([x0, y0, x1, y1], fill=mask_color, outline=None)
            
            # 记录日志
            logger.info(f"已遮盖企业名称: {name}, 位置: {position}")
    
    return masked_image

def pdf_to_images(pdf_data):
    """
    将PDF数据转换为图片列表
    Args:
        pdf_data: PDF文件的字节数据
    Returns:
        图片列表，每个元素为PIL.Image对象
    """
    try:
        # 创建内存文件对象
        pdf_stream = io.BytesIO(pdf_data)
        
        # 打开PDF文件
        pdf_document = fitz.open(stream=pdf_stream, filetype="pdf")
        logger.info(f"成功打开PDF文件，共 {len(pdf_document)} 页")
        
        images = []
        # 遍历每一页
        for page_num in range(len(pdf_document)):
            page = pdf_document[page_num]
            
            # 将页面渲染为图像
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x缩放以提高清晰度
            
            # 转换为PIL图像
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            
            images.append(img)
            logger.info(f"成功转换第 {page_num + 1} 页，尺寸: {img.size}")
        
        pdf_document.close()
        return images
    
    except Exception as e:
        logger.error(f"PDF转图片失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None

def extract_pdf_data(image_base64):
    """
    从Base64编码的PDF数据中提取PDF二进制数据
    Args:
        image_base64: Base64编码的PDF数据
    Returns:
        PDF文件的二进制数据
    """
    try:
        # 移除数据URL前缀
        if "base64," in image_base64:
            pdf_data = image_base64.split('base64,')[1]
        else:
            pdf_data = image_base64
        
        # 解码Base64
        pdf_binary = base64.b64decode(pdf_data)
        return pdf_binary
    
    except Exception as e:
        logger.error(f"提取PDF数据失败: {e}")
        return None

def protect_privacy(image_base64, mask_color=(255, 0, 0)):
    """
    保护图像中的隐私信息（遮盖企业名称）
    Args:
        image_base64: Base64编码的图像数据
        mask_color: 遮盖颜色，默认红色
    Returns:
        处理后的图像的Base64编码，如果是PDF则返回一个包含所有页面的列表
    """
    try:
        # 检查是否是PDF文件
        is_pdf = False
        if "data:application/pdf" in image_base64:
            logger.info("检测到PDF文件，转换为图像后处理")
            is_pdf = True
            
            # 提取PDF数据
            pdf_data = extract_pdf_data(image_base64)
            if not pdf_data:
                logger.warning("提取PDF数据失败，返回原数据")
                return image_base64
            
            # 将PDF转换为图像
            images = pdf_to_images(pdf_data)
            if not images or len(images) == 0:
                logger.warning("PDF转图像失败，返回原数据")
                return image_base64
            
            # 处理所有页面
            logger.info(f"PDF共有 {len(images)} 页，开始处理所有页面")
            processed_images = []
            
            for page_idx, page_image in enumerate(images):
                logger.info(f"处理第 {page_idx + 1} 页，尺寸: {page_image.size}")
                
                # 将图像转换为Base64
                page_base64 = pil_image_to_base64(page_image)
                if not page_base64:
                    logger.warning(f"转换第 {page_idx + 1} 页为Base64失败，跳过此页")
                    continue
                
                # 调用OCR服务进行初步识别
                ocr_result = call_ocr_service(page_base64)
                if not ocr_result:
                    logger.warning(f"第 {page_idx + 1} 页OCR识别失败，使用原图")
                    processed_images.append(page_base64)
                    continue
                
                # 识别企业名称
                company_names = identify_company_names(ocr_result)
                if not company_names:
                    logger.info(f"第 {page_idx + 1} 页未识别到企业名称，使用原图")
                    processed_images.append(page_base64)
                    continue
                
                # 遮盖企业名称
                masked_image = mask_company_names(page_image, company_names, mask_color)
                
                # 将处理后的图像转换为Base64
                masked_image_base64 = pil_image_to_base64(masked_image)
                if not masked_image_base64:
                    logger.warning(f"第 {page_idx + 1} 页转换遮盖后图像为Base64失败，使用原图")
                    processed_images.append(page_base64)
                    continue
                
                # 添加到处理结果
                processed_images.append(masked_image_base64)
                logger.info(f"第 {page_idx + 1} 页处理完成")
            
            # 如果成功处理了至少一页
            if processed_images:
                logger.info(f"PDF处理完成，共处理 {len(processed_images)} 页")
                
                # 返回处理后的所有页面图像
                if len(processed_images) == 1:
                    # 如果只有一页，直接返回图像
                    return processed_images[0]
                else:
                    # 如果有多页，返回图像列表
                    return processed_images
            else:
                logger.warning("未能成功处理任何页面，返回原数据")
                return image_base64
        
        # 非PDF文件的处理逻辑保持不变
        # 调用OCR服务进行初步识别
        ocr_result = call_ocr_service(image_base64)
        if not ocr_result:
            logger.warning("OCR识别失败，返回原图")
            return image_base64  # 如果识别失败，返回原图
        
        # 识别企业名称
        company_names = identify_company_names(ocr_result)
        if not company_names:
            logger.info("未识别到企业名称，返回原图")
            return image_base64  # 如果没有识别到企业名称，返回原图
        
        # 将Base64转换为PIL图像
        image = base64_to_pil_image(image_base64)
        if not image:
            logger.warning("转换Base64为图像失败，返回原图")
            # 尝试提取MIME类型
            mime_type = "未知类型"
            if "data:" in image_base64 and ";base64," in image_base64:
                mime_type = image_base64.split(';')[0].split(':')[1]
            logger.warning(f"无法处理的图像类型: {mime_type}")
            return image_base64
        
        # 遮盖企业名称
        masked_image = mask_company_names(image, company_names, mask_color)
        
        # 将处理后的图像转换为Base64
        masked_image_base64 = pil_image_to_base64(masked_image)
        if not masked_image_base64:
            logger.warning("转换遮盖后图像为Base64失败，返回原图")
            return image_base64
        
        logger.info("隐私保护处理成功完成")
        return masked_image_base64
    except Exception as e:
        logger.error(f"隐私保护处理失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return image_base64  # 出错时返回原图

@router.get("/status")
async def get_api_status():
    """获取API状态"""
    try:
        # 尝试连接OCR服务
        response = requests.get(OCR_SERVICE_URL.replace("/invoke", "/status"), timeout=5)
        ocr_available = response.status_code == 200
    except:
        ocr_available = False
    
    return {
        "status": "running",
        "ocr_service_available": ocr_available,
        "ocr_service_url": OCR_SERVICE_URL,
        "version": "1.0.0"
    }

@router.post("/invoke-file", response_model=OCRResponse)
async def invoke_file(
    file: UploadFile = File(...),
    enable_privacy_protection: bool = Form(False),
    mask_color: str = Form("red")
):
    """
    通过上传文件进行OCR识别，返回详细的识别结果
    - **file**: 要识别的图片或PDF文件
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    start_time = time.time()
    
    # 验证文件格式
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ['.jpg', '.jpeg', '.png', '.pdf']:
        raise HTTPException(
            status_code=400,
            detail="只支持JPG、PNG和PDF格式"
        )
    
    try:
        # 读取文件内容
        content = await file.read()
        
        # 获取文件类型
        image_type = file_ext.replace('.', '')
        if image_type == 'jpg':
            image_type = 'jpeg'
        
        # 转换为base64
        image_base64 = image_to_base64(content, image_type)
        if not image_base64:
            raise HTTPException(
                status_code=500,
                detail="转换图像到base64失败"
            )
        
        # 解析遮盖颜色
        mask_color_map = {
            "red": (255, 0, 0),
            "black": (0, 0, 0),
            "blue": (0, 0, 255),
            "green": (0, 255, 0),
        }
        mask_color_rgb = mask_color_map.get(mask_color.lower(), (255, 0, 0))
        
        # 如果启用了隐私保护，先处理图像
        masked_image_base64 = None
        if enable_privacy_protection:
            logger.info("启用隐私保护，处理图像...")
            masked_image_base64 = protect_privacy(image_base64, mask_color_rgb)
            # 使用处理后的图像进行OCR
            result = call_ocr_service(masked_image_base64)
            # 在结果中添加处理后的图像
            result["image_base64"] = masked_image_base64
        else:
            # 直接调用OCR服务
            result = call_ocr_service(image_base64)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 如果OCR服务返回了duration，使用它
        duration = result.get("duration", f"{process_time:.3f}s")
        
        # 构建响应
        return OCRResponse(
            code=result.get("code", 0),
            message=result.get("message", "识别成功"),
            device=result.get("device", "GPU"),
            duration=duration,
            original=result.get("original", []),
            result=result.get("result", ""),
            image_base64=masked_image_base64 if enable_privacy_protection else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OCR识别失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"OCR识别失败: {str(e)}"
        )

class ImageBase64Request(BaseModel):
    """Base64编码图像请求模型"""
    image_base64: str
    enable_privacy_protection: bool = False
    mask_color: str = "red"

@router.post("/invoke", response_model=OCRResponse)
async def invoke(request: ImageBase64Request):
    """
    通过Base64编码的图像数据进行OCR识别，返回详细的识别结果
    - **image_base64**: Base64编码的图像数据
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    start_time = time.time()
    
    try:
        image_base64 = request.image_base64
        
        # 解析遮盖颜色
        mask_color_map = {
            "red": (255, 0, 0),
            "black": (0, 0, 0),
            "blue": (0, 0, 255),
            "green": (0, 255, 0),
        }
        mask_color_rgb = mask_color_map.get(request.mask_color.lower(), (255, 0, 0))
        
        # 如果启用了隐私保护，先处理图像
        masked_image_base64 = None
        if request.enable_privacy_protection:
            logger.info("启用隐私保护，处理图像...")
            masked_image_base64 = protect_privacy(image_base64, mask_color_rgb)
            # 使用处理后的图像进行OCR
            result = call_ocr_service(masked_image_base64)
            # 在结果中添加处理后的图像
            result["image_base64"] = masked_image_base64
        else:
            # 直接调用OCR服务
            result = call_ocr_service(image_base64)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 如果OCR服务返回了duration，使用它
        duration = result.get("duration", f"{process_time:.3f}s")
        
        # 构建响应
        return OCRResponse(
            code=result.get("code", 0),
            message=result.get("message", "识别成功"),
            device=result.get("device", "GPU"),
            duration=duration,
            original=result.get("original", []),
            result=result.get("result", ""),
            image_base64=masked_image_base64 if request.enable_privacy_protection else None
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OCR识别失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"OCR识别失败: {str(e)}"
        )

@router.post("/recognize", response_model=Dict[str, Any])
async def recognize(
    periods: List[str] = Form(...),
    files: List[UploadFile] = File(...),
    enable_privacy_protection: bool = Form(False),
    mask_color: str = Form("red")
):
    """
    识别多个期间的多个文件，返回结构化的识别结果
    - **periods**: 期间信息，JSON字符串列表，每个元素包含id、year、month字段
    - **files**: 要识别的图片或PDF文件列表
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    start_time = time.time()
    
    # 解析期间信息
    period_data = {}
    for period_str in periods:
        try:
            period = json.loads(period_str)
            period_id = period.get('id')
            if period_id:
                period_data[period_id] = {
                    'year': period.get('year'),
                    'month': period.get('month')
                }
        except Exception as e:
            logger.error(f"解析期间信息失败: {e}")
    
    # 检查期间信息
    if not period_data:
        raise HTTPException(
            status_code=400,
            detail="未提供有效的期间信息"
        )
    
    # 解析遮盖颜色
    mask_color_map = {
        "red": (255, 0, 0),
        "black": (0, 0, 0),
        "blue": (0, 0, 255),
        "green": (0, 255, 0),
    }
    mask_color_rgb = mask_color_map.get(mask_color.lower(), (255, 0, 0))
    
    # 处理文件
    result = {}
    file_count = 0
    
    logger.info(f"收到识别请求，期间数量: {len(period_data)}，文件数量: {len(files)}")
    
    for file in files:
        try:
            # 解析文件名，格式为: files_{period_id}_{filename}
            filename_parts = file.filename.split('_', 2)
            if len(filename_parts) < 3 or filename_parts[0] != 'files':
                logger.warning(f"文件名格式不正确: {file.filename}")
                continue
            
            period_id = filename_parts[1]
            original_filename = filename_parts[2]
            
            # 检查期间ID是否有效
            if period_id not in period_data:
                logger.warning(f"未找到期间ID: {period_id}")
                continue
            
            # 检查文件格式
            file_ext = os.path.splitext(original_filename)[1].lower()
            if file_ext not in ['.jpg', '.jpeg', '.png', '.pdf']:
                logger.warning(f"不支持的文件格式: {file_ext}")
                continue
            
            # 读取文件内容
            content = await file.read()
            
            # 获取文件类型
            image_type = file_ext.replace('.', '')
            if image_type == 'jpg':
                image_type = 'jpeg'
            
            # 转换为base64
            image_base64 = image_to_base64(content, image_type)
            if not image_base64:
                logger.error(f"转换文件到base64失败: {original_filename}")
                continue
            
            # 记录文件处理开始
            file_count += 1
            logger.info(f"启用隐私保护，处理文件: {file.filename}")
            
            # 初始化期间结果
            if period_id not in result:
                result[period_id] = []
            
            # 检查是否是PDF文件
            is_pdf = file_ext.lower() == '.pdf'
            
            # 如果启用了隐私保护，先处理图像
            if enable_privacy_protection:
                # 为PDF文件添加正确的MIME类型前缀
                if is_pdf and "data:application/pdf" not in image_base64:
                    # 移除任何可能的现有前缀
                    if "base64," in image_base64:
                        image_base64 = image_base64.split("base64,")[1]
                    # 添加PDF前缀
                    image_base64 = f"data:application/pdf;base64,{image_base64}"
                
                # 处理隐私保护
                masked_image_base64 = protect_privacy(image_base64, mask_color_rgb)
                
                # 检查是否返回的是数组（多页PDF的情况）
                if isinstance(masked_image_base64, list):
                    # 多页PDF，每页单独处理
                    logger.info(f"检测到多页PDF，共 {len(masked_image_base64)} 页")
                    
                    # 对每一页进行OCR识别，并合并结果
                    all_ocr_results = []
                    processed_images = []
                    
                    for page_idx, page_image_base64 in enumerate(masked_image_base64):
                        try:
                            logger.info(f"对第 {page_idx + 1} 页进行OCR识别")
                            page_ocr_result = call_ocr_service(page_image_base64)
                            if page_ocr_result:
                                # 添加页码信息
                                page_ocr_result["page_number"] = page_idx + 1
                                page_ocr_result["total_pages"] = len(masked_image_base64)
                                all_ocr_results.append(page_ocr_result)
                                processed_images.append(page_image_base64)
                            else:
                                logger.warning(f"第 {page_idx + 1} 页OCR识别失败")
                        except Exception as e:
                            logger.error(f"第 {page_idx + 1} 页OCR识别出错: {e}")
                    
                    # 合并OCR结果
                    if all_ocr_results:
                        # 使用第一页的基本结构
                        ocr_result = all_ocr_results[0].copy()
                        # 添加所有页面的结果
                        ocr_result["all_pages"] = all_ocr_results
                    else:
                        ocr_result = None
                        logger.warning("所有页面OCR识别均失败")
                    
                    # 标记隐私保护状态
                    file_result = {
                        "file_name": original_filename,
                        "file_type": "pdf",
                        "raw_response": ocr_result,
                        "privacy_protected": True,
                        "image_base64": masked_image_base64,  # 保存所有页面的图像
                        "is_pdf": True,
                        "is_multi_page": True,
                        "page_count": len(masked_image_base64)
                    }
                else:
                    # 单页图像，直接调用OCR
                    ocr_result = call_ocr_service(masked_image_base64)
                    
                    # 标记隐私保护状态
                    file_result = {
                        "file_name": original_filename,
                        "file_type": "pdf" if is_pdf else "image",
                        "raw_response": ocr_result,
                        "privacy_protected": True,
                        "image_base64": masked_image_base64,
                        "is_pdf": is_pdf
                    }
            else:
                # 直接调用OCR服务
                ocr_result = call_ocr_service(image_base64)
                
                # 构建结果
                file_result = {
                    "file_name": original_filename,
                    "file_type": "pdf" if is_pdf else "image",
                    "raw_response": ocr_result,
                    "privacy_protected": False
                }
            
            # 添加到结果
            result[period_id].append(file_result)
            
        except Exception as e:
            logger.error(f"处理文件时出错: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
    
    # 如果没有成功处理任何文件
    if file_count == 0:
        raise HTTPException(
            status_code=400,
            detail="未处理任何有效文件"
        )
    
    # 计算处理时间
    process_time = time.time() - start_time
    logger.info(f"处理完成，耗时: {process_time:.2f} 秒")
    
    # 返回结果
    return {
        "data": result,
        "privacy_protected": enable_privacy_protection,
        "process_time": f"{process_time:.2f}s",
        "file_count": file_count
    }

@router.post("/predict-by-file")
async def predict_by_file(
    file: UploadFile = File(...),
    enable_privacy_protection: bool = Form(False),
    mask_color: str = Form("red")
):
    """
    通过上传文件进行OCR识别，返回详细的识别结果
    - **file**: 要识别的图片或PDF文件
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    # 直接调用invoke-file端点
    return await invoke_file(file, enable_privacy_protection, mask_color)

@router.post("/predict-by-table")
async def predict_by_table(
    file: UploadFile = File(...),
    enable_privacy_protection: bool = Form(False),
    mask_color: str = Form("red")
):
    """
    识别表格结构，返回详细的识别结果
    - **file**: 包含表格的图片或PDF
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    # 直接调用invoke-file端点
    return await invoke_file(file, enable_privacy_protection, mask_color)

@router.post("/financial-reports")
async def recognize_financial_reports(
    periods: List[str] = Form(...),
    files: List[UploadFile] = File(...),
    enable_privacy_protection: bool = Form(False),
    mask_color: str = Form("red")
):
    """
    批量识别财务报表
    - **periods**: JSON格式的期间信息列表
    - **files**: 要识别的财务报表文件列表
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    # 直接调用recognize端点
    return await recognize(periods, files, enable_privacy_protection, mask_color)

@router.get("/supported_features")
async def get_supported_features():
    """
    获取支持的OCR功能列表
    """
    features = {
        "invoke": "Base64图像识别（详细结果）",
        "invoke-file": "文件识别（详细结果）",
        "predict-by-file": "文本识别",
        "predict-by-table": "表格识别",
        "financial-reports": "财务报表批量识别",
        "recognize": "财务报表识别（前端主要使用）",
        "privacy_protection": "隐私保护（遮盖企业名称）"
    }
    
    return {
        "resultcode": 200,
        "message": "Success",
        "features": features
    }

@router.get("/supported_languages")
async def get_supported_languages():
    """
    获取支持的语言列表
    """
    languages = {
        "ch": "中文",
        "en": "英文",
        "fr": "法文",
        "german": "德文",
        "korean": "韩文",
        "japan": "日文"
    }
    
    return {
        "resultcode": 200,
        "message": "Success",
        "languages": languages
    }

def generate_financial_prompt_template(raw_data, period_info=""):
    """
    生成财务报表提取的AI提示词模板
    Args:
        raw_data: 原始OCR识别数据
        period_info: 期间信息，例如"2022年12月"
    Returns:
        生成的提示词模板
    """
    # 获取原始数据字符串 - 限制数据量
    raw_data_string = ""
    
    # 判断数据类型（Excel还是OCR）
    is_excel = isinstance(raw_data, dict) and ("_sheet_info" in raw_data or 
                                              (not any(key in raw_data for key in ["original", "result", "title"])))
    
    # 如果是字典类型，提取关键信息
    if isinstance(raw_data, dict):
        # 检查是否是多工作表的Excel数据
        if "_sheet_info" in raw_data and "sheet_names" in raw_data["_sheet_info"]:
            # 多工作表Excel处理
            sheet_names = raw_data["_sheet_info"]["sheet_names"]
            raw_data_string += f"Excel文件包含 {len(sheet_names)} 个工作表: {', '.join(sheet_names)}\n\n"
            
            # 最多处理前5个工作表，每个工作表限制15000字符
            for i, sheet_name in enumerate(sheet_names[:5]):
                if sheet_name in raw_data:
                    sheet_data = raw_data[sheet_name]
                    raw_data_string += f"=== 工作表: {sheet_name} ===\n"
                    
                    # 转换为字符串，限制长度
                    sheet_str = str(sheet_data)
                    if len(sheet_str) > 15000:  # 每个工作表最多15000字符，增加了限制
                        sheet_str = sheet_str[:15000] + "...(数据已截断)"
                    raw_data_string += sheet_str + "\n\n"
            
            if len(sheet_names) > 5:
                raw_data_string += f"(还有 {len(sheet_names) - 5} 个工作表未显示)\n\n"
        else:
            # 添加原始OCR数据（包括位置信息）
            if "original" in raw_data and isinstance(raw_data["original"], list):
                original_content = ""
                for item in raw_data["original"]:
                    if "text" in item and "box" in item:
                        box_str = ", ".join([f"({p[0]}, {p[1]})" for p in item["box"]]) if item["box"] else "无坐标"
                        original_content += f"文本: {item['text']} | 坐标: {box_str}\n"
                
                # 限制原始OCR数据总长度
                if len(original_content) > 30000:  # 增加字符限制，避免超长问题
                    logger.info(f"OCR原始数据超长，从{len(original_content)}字符截断至30000字符")
                    original_content = original_content[:30000] + "...(原始数据已截断)"
                raw_data_string = original_content
    else:
        # 如果不是字典，转换为字符串并限制长度
        try:
            temp_str = str(raw_data)
            # 限制长度为30000字符，增加了限制
            if len(temp_str) > 30000:
                logger.info(f"原始数据超长，从{len(temp_str)}字符截断至30000字符")
                raw_data_string = temp_str[:30000] + "...(数据已截断)"
            else:
                raw_data_string = temp_str
        except:
            raw_data_string = "无法解析的数据格式"
    
    # 如果没有提供期间信息，尝试从数据中提取
    if not period_info:
        try:
            if isinstance(raw_data, dict):
                if raw_data.get("period"):
                    period_info = raw_data.get("period")
                elif raw_data.get("title") and isinstance(raw_data.get("title"), str):
                    # 尝试从标题中提取时间信息
                    date_matches = re.search(r'(\d{4})年(\d{1,2})月', raw_data.get("title"))
                    if date_matches:
                        period_info = f"{date_matches.group(1)}年{date_matches.group(2)}月"
        except Exception as e:
            logger.error(f"提取期间信息失败: {e}")
    
    # 根据数据类型选择不同的提示词开头
    if is_excel:
        prompt_start = f"""完全按照坐标提取所有的科目（不要修改名称）和金额数据，不要遗漏任何一个科目。"""
    else:
        prompt_start = f"""完全按照坐标提取所有的科目（不要修改名称）和金额数据，不要遗漏任何一个科目。"""
    
    # 创建提示词内容
    prompt = f"""{prompt_start}

【核心要求】：
1. 你是一个纯粹的数据提取工具，不是对话助手
2. 直接输出Markdown表格，绝对不输出任何其他内容
3. 不允许有任何思考过程、分析、解释、前言、后语
4. 不允许出现"我发现"、"根据数据"、"分析显示"等任何解释性文字
5. 不允许出现"我正在处理"、"我需要分析"等任何提示性文字
6. 不允许在表格前后添加任何文字说明
7. 如果你理解了这些要求，请直接开始提取数据，不要回复"我理解"或类似确认

【表格格式规范】：
1. 表格必须严格按照以下格式：
```
| 科目名称 | {period_info or 'xxxx年xx月'} |
| --- | --- |
| 货币资金 | 100,000.00 |
| 应收账款 | 50,000.00 |
```
2. 表头行必须只有两列：第一列是"科目名称"，第二列是期间
3. 表头下方必须有且只有一行分隔行：| --- | --- |
4. 每行数据必须有且只有两列：第一列是科目名称，第二列是对应金额
5. 每个单元格前后必须有一个空格，例如：| 货币资金 | 100,000.00 |
6. 表格的每一行必须以"|"开头和结尾

【数据提取规则】：
1. 只提取期末数据、年末数据或累计数据，忽略期初数据和中间月份数据
2. 如果表格有多个年份，优先提取最新年份的数据
3. 如果表格同时有"本期金额"和"上期金额"，只提取"本期金额"
4. 如果表格有"期末余额"和"期初余额"，只提取"期末余额"
5. 如果表格有"本年累计"和"本月"，只提取"本年累计"数据
6. 如果有多列数据，按照以下优先级提取：期末数 > 年末数 > 累计数 > 最新年份数据
7. 原来是什么科目名称就是什么科目名称，不要改变或简化
8. 必须提取所有可识别的科目，确保数据完整性
9. 严格按照原始数据坐标的顺序、科目名称来显示，不要做出任何修改或重新排序
10. 只提取有明确对应金额的科目，如果某个科目没有明确的金额值，应该标记为"--"
11. 不要自行推断或填充缺失的金额值
12. 科目和金额的匹配必须基于坐标位置的接近程度，确保正确匹配
13. 忽略"行次"列中的数字（如"1"、"2"、"3"等），这些不是金额
14. 如果金额中有空格，需要去除空格后再显示，且要把数据都显示全。

【原始数据】
{raw_data_string}"""

    # 检查提示词长度，如果超长则提前返回警告
    prompt_length = len(prompt)
    if prompt_length > 40000:
        logger.warning(f"生成的提示词超长: {prompt_length}字符，可能会被分块处理")
    
    return prompt

async def call_deepseek_api_with_prompt(prompt, temperature=0.7, max_tokens=32000):
    """
    调用Deepseek API进行非流式聊天
    Args:
        prompt: 提示词
        temperature: 温度参数
        max_tokens: 最大生成token数
    Returns:
        API响应的内容
    """
    # 检查提示词长度，如果超过临界值，自动启用分块处理
    if len(prompt) > 10000:  # 10000个字符约为3000个token
        logger.info(f"检测到长文本，长度: {len(prompt)}字符，使用分块处理模式")
        
        # 使用分块处理，收集所有页面的结果
        all_pages_content = []
        current_page = ""
        page_count = 0
        
        async for chunk in process_long_text_stream(prompt, temperature, max_tokens):
            if chunk.startswith("data: "):
                try:
                    data = chunk[6:]
                    if data == "[DONE]":
                        continue
                    
                    json_data = json.loads(data)
                    if "content" in json_data:
                        content = json_data["content"]
                        
                        # 检测是否是新页面标记
                        if "正在处理第" in content and "页" in content and "\n\n---\n\n" in content:
                            # 保存上一页内容并开始新页面
                            if current_page:
                                all_pages_content.append(current_page)
                                current_page = ""
                            page_count += 1
                        elif "已分为" in content and "页进行处理" in content:
                            # 这是初始页面信息，不添加到内容中
                            page_count = 1
                        else:
                            # 累积当前页面内容
                            current_page += content
                except:
                    pass
        
        # 添加最后一页
        if current_page:
            all_pages_content.append(current_page)
        
        # 如果有收集到内容，处理并返回
        if all_pages_content:
            # 只返回最后一页的内容作为最终结果
            # 如果需要所有页面内容，可以用 "\n\n---PAGE BREAK---\n\n".join(all_pages_content)
            logger.info(f"分块处理完成，共{len(all_pages_content)}页，最后一页内容长度: {len(all_pages_content[-1])}字符")
            return all_pages_content[-1]
        else:
            logger.warning("分块处理未返回内容，将尝试常规处理")
    
    # 最大重试次数
    max_retries = 3
    retry_count = 0
    retry_delay = 2  # 初始重试延迟（秒）
    
    while retry_count < max_retries:
        try:
            logger.info(f"开始调用Deepseek API (尝试 {retry_count + 1}/{max_retries})，max_tokens={max_tokens}")
            
            # 构建消息
            messages = [
                {"role": "system", "content": "你是一个财务数据分析专家，擅长从财务报表中提取所有数据。"},
                {"role": "user", "content": prompt}
            ]
            
            # 构建请求体
            request_body = {
                "model": DEEPSEEK_MODEL,
                "messages": messages,
                "stream": False,
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            # 发送请求，增加超时时间
            async with httpx.AsyncClient(timeout=600.0) as client:
                try:
                    logger.info(f"连接到 Deepseek API: {DEEPSEEK_API_URL}")
                    response = await client.post(
                        DEEPSEEK_API_URL,
                        json=request_body,
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                        }
                    )
                    
                    if response.status_code != 200:
                        logger.error(f"Deepseek API错误: {response.status_code} - {response.text}")
                        
                        # 检查是否是上下文长度超限错误
                        error_text = response.text.lower()
                        if "context length" in error_text or "maximum context length" in error_text:
                            logger.error("检测到上下文长度超限错误，尝试分块处理")
                            
                            # 如果是第一次遇到此错误，尝试分块处理并返回
                            if retry_count == 0:
                                logger.info("切换到分块处理模式")
                                # 构建一个临时接收器，收集所有分块处理的结果
                                response_content = ""
                                async for chunk in process_long_text_stream(prompt, temperature, max_tokens):
                                    if chunk.startswith("data: "):
                                        try:
                                            data = chunk[6:]
                                            if data == "[DONE]":
                                                continue
                                            json_data = json.loads(data)
                                            if "content" in json_data:
                                                response_content += json_data["content"]
                                        except:
                                            pass
                                
                                # 如果收集到内容，直接返回
                                if response_content:
                                    logger.info(f"分块处理完成，最终内容长度: {len(response_content)}字符")
                                    return response_content
                        
                        # 如果是服务器错误，尝试重试
                        if response.status_code >= 500:
                            retry_count += 1
                            if retry_count < max_retries:
                                wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                                logger.info(f"服务器错误，{wait_time}秒后重试...")
                                await asyncio.sleep(wait_time)
                                continue
                        return f"API错误: {response.text}"
                    
                    # 解析响应
                    result = response.json()
                    
                    # 提取回复内容
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0].get("message", {}).get("content", "")
                        return content
                    else:
                        logger.error(f"无法从API响应中提取内容: {result}")
                        return "无法从API响应中提取内容"
                        
                except httpx.ConnectError as e:
                    logger.error(f"连接到 Deepseek API 失败: {e}")
                    # 连接错误，尝试重试
                    retry_count += 1
                    if retry_count < max_retries:
                        wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                        logger.info(f"连接失败，{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        return f"连接到API服务器失败，请检查网络设置或稍后再试。错误: {str(e)}"
                
        except Exception as e:
            logger.error(f"调用Deepseek API时出错: {e}")
            # 其他错误，尝试重试
            retry_count += 1
            if retry_count < max_retries:
                wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                logger.info(f"发生错误，{wait_time}秒后重试: {str(e)}")
                await asyncio.sleep(wait_time)
                continue
            else:
                return f"调用API时出错: {str(e)}"
    
    # 如果所有重试都失败
    return "多次尝试后仍无法连接到API服务器，请检查网络设置或联系管理员"

async def deepseek_stream_chat_with_prompt(prompt, temperature=0.7, max_tokens=32000):
    """
    调用Deepseek API进行流式聊天，支持长文本处理
    Args:
        prompt: 提示词
        temperature: 温度参数
        max_tokens: 最大生成token数
    Yields:
        流式响应的内容
    """
    # 检查提示词长度，如果过长就进行分块处理
    is_long_text = len(prompt) > 10000  # 10000个字符约为3000个token
    if is_long_text:
        logger.info(f"检测到长文本，长度: {len(prompt)}字符，进行分块处理")
        async for chunk in process_long_text_stream(prompt, temperature, max_tokens):
            yield chunk
        return  # 使用不带值的return退出函数
    
    # 最大重试次数
    max_retries = 3
    retry_count = 0
    retry_delay = 2  # 初始重试延迟（秒）
    
    # 估算提示词的token数量（粗略估计，中文每个字约1.5个token，英文每个单词约1.3个token）
    def estimate_tokens(text):
        # 简单估算，实际token数可能与此不同
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        other_chars = len(text) - chinese_chars
        return int(chinese_chars * 1.5 + other_chars * 0.3)
    
    estimated_tokens = estimate_tokens(prompt)
    logger.info(f"提示词估计token数: {estimated_tokens}")
    
    # 如果估计的token数超过限制的80%，自动减少max_tokens
    if estimated_tokens > 32000 * 0.8:  # 32000是模型上下文长度上限
        # 调整max_tokens，确保总长度不超过上限
        adjusted_max_tokens = max(2000, 32000 - estimated_tokens - 1000)  # 预留1000个token的安全边界
        logger.warning(f"提示词过长，自动调整max_tokens从{max_tokens}到{adjusted_max_tokens}")
        max_tokens = adjusted_max_tokens
    
    while retry_count < max_retries:
        try:
            logger.info(f"开始调用Deepseek流式API (尝试 {retry_count + 1}/{max_retries})，max_tokens={max_tokens}")
            
            # 构建消息
            messages = [
                {"role": "system", "content": "你是一个财务数据分析专家，擅长从财务报表中提取所有数据。"},
                {"role": "user", "content": prompt}
            ]
            
            # 构建请求体
            request_body = {
                "model": DEEPSEEK_MODEL,
                "messages": messages,
                "stream": True,
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            # 发送请求，增加超时时间
            async with httpx.AsyncClient(timeout=600.0) as client:
                try:
                    logger.info(f"连接到 Deepseek API: {DEEPSEEK_API_URL}")
                    async with client.stream(
                        "POST",
                        DEEPSEEK_API_URL,
                        json=request_body,
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                        }
                    ) as response:
                        if response.status_code != 200:
                            error_msg = await response.aread()
                            logger.error(f"Deepseek API错误: {response.status_code} - {error_msg}")
                            
                            # 检查是否是上下文长度超限错误
                            if "context length" in str(error_msg).lower() or "maximum context length" in str(error_msg).lower():
                                logger.error("检测到上下文长度超限错误，尝试减少提示词长度")
                                
                                # 如果是第一次遇到此错误，尝试分块处理
                                if retry_count == 0:
                                    logger.info("切换到分块处理模式")
                                    async for chunk in process_long_text_stream(prompt, temperature, max_tokens):
                                        yield chunk
                                    return  # 使用不带值的return退出函数
                            
                            # 如果是服务器错误，尝试重试
                            if response.status_code >= 500:
                                retry_count += 1
                                if retry_count < max_retries:
                                    wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                                    logger.info(f"服务器错误，{wait_time}秒后重试...")
                                    await asyncio.sleep(wait_time)
                                    continue
                            yield "data: " + json.dumps({'error': f'API错误: {error_msg}'}) + "\n\n"
                            return
                        
                        # 流式处理响应
                        buffer = ""
                        assistant_message = {"role": "assistant", "content": ""}
                        total_tokens = 0
                        
                        async for chunk in response.aiter_bytes():
                            buffer += chunk.decode("utf-8")
                            
                            # 处理可能的多行数据
                            while "\n\n" in buffer:
                                line, buffer = buffer.split("\n\n", 1)
                                if line.startswith("data: "):
                                    data = line[6:]  # 去掉 "data: " 前缀
                                    if data == "[DONE]":
                                        logger.info(f"Deepseek流式响应完成，总计tokens: {total_tokens}")
                                        continue
                                        
                                    try:
                                        json_data = json.loads(data)
                                        if "choices" in json_data and len(json_data["choices"]) > 0:
                                            delta = json_data["choices"][0].get("delta", {})
                                            if "content" in delta and delta["content"]:
                                                content = delta["content"]
                                                assistant_message["content"] += content
                                                total_tokens += 1
                                                # 发送小块数据，改进流式体验
                                                yield "data: " + json.dumps({'content': content}) + "\n\n"
                                    except json.JSONDecodeError as e:
                                        logger.warning(f"JSON解析错误: {e}, 数据: {data[:100]}...")
                                        continue
                        
                        # 确保最后的内容也被发送
                        if buffer and buffer.startswith("data: "):
                            try:
                                data = buffer[6:]  # 去掉 "data: " 前缀
                                if data != "[DONE]":
                                    json_data = json.loads(data)
                                    if "choices" in json_data and len(json_data["choices"]) > 0:
                                        delta = json_data["choices"][0].get("delta", {})
                                        if "content" in delta and delta["content"]:
                                            content = delta["content"]
                                            yield "data: " + json.dumps({'content': content}) + "\n\n"
                            except Exception as e:
                                logger.warning(f"处理最后的缓冲区时出错: {e}")
                        
                        # 成功完成，发送结束标记
                        yield "data: [DONE]\n\n"
                        return  # 成功完成，退出函数
                        
                except httpx.ConnectError as e:
                    logger.error(f"连接到 Deepseek API 失败: {e}")
                    # 连接错误，尝试重试
                    retry_count += 1
                    if retry_count < max_retries:
                        wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                        logger.info(f"连接失败，{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        yield "data: " + json.dumps({'error': f'连接到API服务器失败，请检查网络设置或稍后再试。错误: {str(e)}'}) + "\n\n"
                        return
                
        except Exception as e:
            logger.error(f"Deepseek API error: {str(e)}")
            # 其他错误，尝试重试
            retry_count += 1
            if retry_count < max_retries:
                wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                logger.info(f"发生错误，{wait_time}秒后重试: {str(e)}")
                await asyncio.sleep(wait_time)
                continue
            else:
                yield "data: " + json.dumps({'error': f'调用API时发生错误: {str(e)}'}) + "\n\n"
                return
    
    # 如果所有重试都失败
    yield "data: " + json.dumps({'error': '多次尝试后仍无法连接到API服务器，请检查网络设置或联系管理员'}) + "\n\n"

async def process_long_text_stream(prompt, temperature=0.7, max_tokens=16000):
    """
    处理长文本的函数，将长文本分块后逐块处理，每个分块独立问询
    Args:
        prompt: 长文本提示词
        temperature: 温度参数
        max_tokens: 最大生成token数
    Yields:
        流式响应的内容
    """
    logger.info(f"开始处理长文本，总长度: {len(prompt)}字符")
    
    # 提取指令部分（前面的说明部分）和数据部分（要分析的OCR数据）
    # 假设提示词的结构是：指令部分 + OCR数据部分
    
    # 查找第一个原始数据的分隔标记
    instruction_marker_index = prompt.find("【原始数据】")
    if instruction_marker_index == -1:
        # 如果没有明确的分隔符，尝试其他分隔方式
        # 寻找连续的三个以上换行作为分隔点
        consecutive_newlines = re.search(r'\n{3,}', prompt)
        if consecutive_newlines:
            instruction_marker_index = consecutive_newlines.start()
        else:
            # 找不到明确分隔，尝试取前1000个字符作为指令
            instruction_marker_index = min(1000, len(prompt) // 4)
    
    # 分离指令和数据
    instruction_part = prompt[:instruction_marker_index]
    data_part = prompt[instruction_marker_index:]
    
    logger.info(f"指令部分长度: {len(instruction_part)}字符, 数据部分长度: {len(data_part)}字符")
    
    # 准备分块处理
    max_chunk_size = 6000  # 每个块最多6000个字符
    chunks = []
    
    # 按照大小分块数据部分
    for i in range(0, len(data_part), max_chunk_size):
        chunks.append(data_part[i:i + max_chunk_size])
    
    logger.info(f"数据分为{len(chunks)}个部分")
    
    # 设置系统消息（对每个请求都一样）
    system_message = "你是财务专家，擅长从财务报表中提取所有数据，保持原来科目的名称。严格按照要求输出，不要添加任何解释或思考过程。"
    
    # 告知用户总分块数
    page_info = f"数据较长，已分为{len(chunks)}页进行处理。正在处理第1页..."
    yield "data: " + json.dumps({'status': 'processing', 'message': page_info}) + "\n\n"
    
    # 处理每个块
    for i, chunk in enumerate(chunks):
        # 为每个部分添加提示，每个块都是独立的完整提示
        if len(chunks) > 1:
            # 多块情况下，告知这是第几页，并让每个块都能独立生成完整结果
            page_info = f"【注意：这是第{i+1}页，共{len(chunks)}页】\n\n"
            chunk_prompt = f"{instruction_part}\n\n{page_info}{chunk}"
            
            # 添加这段代码，将分页提示词也发送给前端
            yield "data: " + json.dumps({
                'status': 'page_prompt',
                'page_prompt': {
                    'page': i+1, 
                    'total_pages': len(chunks),
                    'prompt': chunk_prompt
                }
            }) + "\n\n"
        else:
            # 单块情况，使用原始提示
            chunk_prompt = f"{instruction_part}\n\n{chunk}"
        
        logger.info(f"处理第 {i+1}/{len(chunks)} 页，长度: {len(chunk_prompt)}字符")
        
        # 如果不是第一页，先通知用户正在处理新页面
        if i > 0:
            page_msg = f"正在处理第{i+1}/{len(chunks)}页..."
            yield "data: " + json.dumps({'status': 'processing', 'message': page_msg}) + "\n\n"
        
        # 构建消息
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": chunk_prompt}
        ]
        
        # 构建请求体
        request_body = {
            "model": DEEPSEEK_MODEL,
            "messages": messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        # 发送请求并处理响应
        max_retries = 3
        retry_count = 0
        retry_delay = 2
        current_page_content = ""
        success = False
        
        while retry_count < max_retries and not success:
            try:
                async with httpx.AsyncClient(timeout=600.0) as client:
                    async with client.stream(
                        "POST",
                        DEEPSEEK_API_URL,
                        json=request_body,
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                        }
                    ) as response:
                        if response.status_code != 200:
                            error_msg = await response.aread()
                            logger.error(f"处理第 {i+1} 页时出错: {response.status_code} - {error_msg}")
                            retry_count += 1
                            if retry_count < max_retries:
                                wait_time = retry_delay * (2 ** (retry_count - 1))
                                logger.info(f"等待 {wait_time} 秒后重试...")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                # 所有重试都失败，发送错误消息
                                error_content = f"[处理第 {i+1}/{len(chunks)} 页时出错: {error_msg}]"
                                yield "data: " + json.dumps({'status': 'error', 'message': error_content}) + "\n\n"
                                break
                        
                        # 流式处理响应
                        buffer = ""
                        
                        async for chunk_bytes in response.aiter_bytes():
                            buffer += chunk_bytes.decode("utf-8")
                            
                            # 处理可能的多行数据
                            while "\n\n" in buffer:
                                line, buffer = buffer.split("\n\n", 1)
                                if line.startswith("data: "):
                                    data = line[6:]  # 去掉 "data: " 前缀
                                    if data == "[DONE]":
                                        logger.info(f"第 {i+1}/{len(chunks)} 页处理完成")
                                        continue
                                    
                                    try:
                                        json_data = json.loads(data)
                                        if "choices" in json_data and len(json_data["choices"]) > 0:
                                            delta = json_data["choices"][0].get("delta", {})
                                            if "content" in delta and delta["content"]:
                                                content = delta["content"]
                                                current_page_content += content
                                                
                                                # 直接发送当前内容
                                                yield "data: " + json.dumps({'status': 'streaming', 'content': content}) + "\n\n"
                                    except json.JSONDecodeError:
                                        continue
                        
                        # 处理成功
                        success = True
                        
                        # 如果是多页的情况，添加页码信息
                        if len(chunks) > 1 and success and current_page_content:
                            page_footer = f"[第{i+1}页/{len(chunks)}页 完成]"
                            yield "data: " + json.dumps({'status': 'page_complete', 'page': i+1, 'total_pages': len(chunks), 'message': page_footer}) + "\n\n"
                        
            except Exception as e:
                logger.error(f"处理第 {i+1} 页时发生异常: {str(e)}")
                retry_count += 1
                if retry_count < max_retries:
                    wait_time = retry_delay * (2 ** (retry_count - 1))
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    # 所有重试都失败，发送错误消息
                    error_content = f"[处理第 {i+1}/{len(chunks)} 页时出错: {str(e)}]"
                    yield "data: " + json.dumps({'status': 'error', 'message': error_content}) + "\n\n"
    
    # 如果有多个分块，添加总结信息
    if len(chunks) > 1:
        summary_msg = f"[全部{len(chunks)}页数据处理完成]"
        yield "data: " + json.dumps({'status': 'all_complete', 'message': summary_msg}) + "\n\n"
    
    # 完成所有处理，发送结束标记
    yield "data: [DONE]\n\n"

def process_excel_data(file_content):
    """
    处理Excel文件数据
    Args:
        file_content: Excel文件内容
    Returns:
        处理后的数据
    """
    temp_file = None
    excel_file = None
    
    try:
        # 创建临时文件
        temp_file = os.path.join(TEMP_DIR, f"temp_{uuid.uuid4()}.xlsx")
        with open(temp_file, "wb") as f:
            f.write(file_content)
        
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(temp_file)
        sheet_names = excel_file.sheet_names
        
        # 如果只有一个工作表，保持原来的行为
        if len(sheet_names) == 1:
            df = pd.read_excel(excel_file, sheet_name=sheet_names[0])
            data = df.to_dict(orient="records")
        else:
            # 读取所有工作表
            data = {}
            for sheet_name in sheet_names:
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                data[sheet_name] = df.to_dict(orient="records")
            # 添加工作表信息
            data["_sheet_info"] = {
                "sheet_count": len(sheet_names),
                "sheet_names": sheet_names
            }
        
        return data
    except Exception as e:
        logger.error(f"处理Excel文件时出错: {e}")
        return {"error": f"处理Excel文件时出错: {str(e)}"}
    finally:
        # 确保关闭Excel文件
        if excel_file is not None:
            try:
                excel_file.close()
            except Exception as e:
                logger.warning(f"关闭Excel文件时出错: {e}")
        
        # 确保删除临时文件
        if temp_file is not None and os.path.exists(temp_file):
            try:
                # 尝试多次删除文件，有时文件可能被锁定
                for _ in range(3):
                    try:
                        os.unlink(temp_file)
                        break
                    except PermissionError:
                        logger.warning(f"文件可能被锁定，等待后重试删除: {temp_file}")
                        time.sleep(0.5)  # 等待500毫秒后重试
                    except Exception as e:
                        logger.warning(f"删除临时文件时出错: {e}")
                        break
            except Exception as e:
                logger.warning(f"清理临时文件时出错: {e}")

def parse_markdown_table(markdown_text):
    """
    解析Markdown表格，转换为结构化数据
    Args:
        markdown_text: Markdown格式的表格文本
    Returns:
        解析后的表格数据
    """
    try:
        # 分割行
        lines = markdown_text.strip().split('\n')
        
        # 至少需要3行（表头、分隔行、数据行）
        if len(lines) < 3:
            return []
        
        # 提取表头
        header_line = lines[0].strip()
        headers = [h.strip() for h in header_line.strip('|').split('|')]
        
        # 跳过分隔行
        data = []
        for line in lines[2:]:
            if '|' not in line:
                continue
            
            # 提取单元格数据
            cells = [cell.strip() for cell in line.strip('|').split('|')]
            
            # 确保单元格数量与表头一致
            if len(cells) == len(headers):
                row = {}
                for i, header in enumerate(headers):
                    row[header] = cells[i]
                data.append(row)
        
        return data
    except Exception as e:
        logger.error(f"解析Markdown表格时出错: {e}")
        return []

@router.post("/process-files")
async def process_files(
    background_tasks: BackgroundTasks,
    periods: List[str] = Form(...),
    files: List[UploadFile] = File(...),
    enable_privacy_protection: bool = Form(False),
    mask_color: str = Form("red")
):
    """
    处理上传的文件，调用金投大脑API进行分析，返回处理结果
    - **periods**: 期间信息，JSON字符串列表，每个元素包含id、year、month字段
    - **files**: 要处理的文件列表
    - **enable_privacy_protection**: 是否启用隐私保护（遮盖企业名称）
    - **mask_color**: 遮盖颜色（red, black, blue, green）
    """
    start_time = time.time()
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 初始化任务状态
    processing_tasks[task_id] = {
        "status": "processing",
        "message": "正在处理文件...",
        "progress": 0,
        "result": None
    }
    
    # 解析期间信息
    period_data = {}
    for period_str in periods:
        try:
            period = json.loads(period_str)
            period_id = period.get('id')
            if period_id:
                period_data[period_id] = {
                    'year': period.get('year'),
                    'month': period.get('month')
                }
        except Exception as e:
            logger.error(f"解析期间信息失败: {e}")
    
    # 检查期间信息
    if not period_data:
        raise HTTPException(
            status_code=400,
            detail="未提供有效的期间信息"
        )
    
    # 解析遮盖颜色
    mask_color_map = {
        "red": (255, 0, 0),
        "black": (0, 0, 0),
        "blue": (0, 0, 255),
        "green": (0, 255, 0),
    }
    mask_color_rgb = mask_color_map.get(mask_color.lower(), (255, 0, 0))
    
    # 在后台处理文件
    background_tasks.add_task(
        process_files_background,
        task_id=task_id,
        files=files,
        period_data=period_data,
        enable_privacy_protection=enable_privacy_protection,
        mask_color_rgb=mask_color_rgb
    )
    
    # 返回任务ID
    return {
        "task_id": task_id,
        "status": "processing",
        "message": "文件处理已开始，请通过任务ID查询进度"
    }

@router.get("/task-status/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务处理状态
    - **task_id**: 任务ID
    """
    if task_id not in processing_tasks:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    task_info = processing_tasks[task_id]
    
    # 如果任务已完成，返回结果
    if task_info["status"] == "completed":
        result = task_info.get("result", {})
        
        # 清理大型数据，只保留必要信息
        if "raw_data" in result:
            del result["raw_data"]
        
        return {
            "status": "completed",
            "message": "处理完成",
            "progress": 100,
            "result": result
        }
    
    # 如果任务失败，返回错误信息
    if task_info["status"] == "failed":
        return {
            "status": "failed",
            "message": task_info.get("message", "处理失败"),
            "error": task_info.get("error", "未知错误")
        }
    
    # 如果任务仍在处理中，返回进度
    return {
        "status": "processing",
        "message": task_info.get("message", "正在处理..."),
        "progress": task_info.get("progress", 0)
    }

@router.get("/stream-result/{task_id}")
async def stream_task_result(task_id: str):
    """
    流式获取任务处理结果
    - **task_id**: 任务ID
    """
    if task_id not in processing_tasks:
        raise HTTPException(
            status_code=404,
            detail="任务不存在"
        )
    
    # 返回流式响应
    return StreamingResponse(
        stream_task_updates(task_id),
        media_type="text/event-stream"
    )

async def stream_task_updates(task_id: str):
    """
    流式返回任务更新
    Args:
        task_id: 任务ID
    Yields:
        流式响应的内容
    """
    last_status = None
    last_progress = -1
    last_streaming_content = ""
    
    while True:
        # 获取当前任务状态
        task_info = processing_tasks.get(task_id)
        if not task_info:
            yield f"data: {json.dumps({'error': '任务不存在'})}\n\n"
            break
        
        current_status = task_info["status"]
        current_progress = task_info.get("progress", 0)
        
        # 检查是否有提示词需要发送
        if "current_prompt" in task_info and task_info["current_prompt"]:
            prompt_data = task_info["current_prompt"]
            yield f"data: {json.dumps({'status': 'prompt', 'prompt': prompt_data})}\n\n"
            # 发送后清除，避免重复发送
            task_info["current_prompt"] = None
        
        # 检查是否有部分结果可用（处理后的图片）
        if "partial_result" in task_info and task_info["partial_result"]:
            partial_result = task_info["partial_result"]
            yield f"data: {json.dumps({'status': 'partial_result', 'partial_result': partial_result})}\n\n"
            # 发送后清除，避免重复发送
            task_info["partial_result"] = None
        
        # 检查是否在流式输出AI分析结果
        if task_info.get("streaming", False) or task_info.get("stream_content"):
            current_content = task_info.get("stream_content", "")
            # 只发送新增的内容
            if current_content != last_streaming_content:
                # 计算新增内容
                new_content = current_content
                if last_streaming_content and current_content.startswith(last_streaming_content):
                    new_content = current_content[len(last_streaming_content):]
                
                # 更新上次内容
                last_streaming_content = current_content
                
                # 发送流式更新
                stream_update = {
                    "status": "streaming",
                    "message": task_info.get("message", "正在生成分析结果..."),
                    "progress": current_progress,
                    "current_page": task_info.get("current_page", 1),
                    "total_pages": task_info.get("total_pages", 1),
                    "content": new_content
                }
                yield f"data: {json.dumps(stream_update)}\n\n"
        
        # 如果状态或进度发生变化，发送更新
        elif current_status != last_status or current_progress != last_progress:
            update_data = {
                "status": current_status,
                "message": task_info.get("message", ""),
                "progress": current_progress
            }
            
            # 如果任务已完成，包含结果
            if current_status == "completed":
                result = task_info.get("result", {})
                # 确保流式内容被包含在结果中
                if task_info.get("stream_content"):
                    for period_id, period_data in result.get("periods", {}).items():
                        for file_data in period_data.get("files", []):
                            if "ai_response" not in file_data:
                                file_data["ai_response"] = task_info["stream_content"]
                
                update_data["result"] = result
                # 清理大型数据
                if "raw_data" in update_data["result"]:
                    del update_data["result"]["raw_data"]
                
                yield f"data: {json.dumps(update_data)}\n\n"
                yield f"data: [DONE]\n\n"
                break
            
            # 如果任务失败，包含错误信息
            if current_status == "failed":
                update_data["error"] = task_info.get("error", "未知错误")
                yield f"data: {json.dumps(update_data)}\n\n"
                yield f"data: [DONE]\n\n"
                break
            
            # 发送更新
            yield f"data: {json.dumps(update_data)}\n\n"
            
            # 更新上次状态
            last_status = current_status
            last_progress = current_progress
        
        # 等待一段时间再检查
        await asyncio.sleep(0.5)  # 减少检查间隔，提高流式响应的实时性

async def process_files_background(task_id, files, period_data, enable_privacy_protection, mask_color_rgb):
    """
    在后台处理文件
    Args:
        task_id: 任务ID
        files: 文件列表
        period_data: 期间数据
        enable_privacy_protection: 是否启用隐私保护
        mask_color_rgb: 遮盖颜色
    """
    try:
        # 更新任务状态
        processing_tasks[task_id]["message"] = "正在处理文件..."
        processing_tasks[task_id]["progress"] = 10
        
        # 处理文件
        result = {}
        file_count = 0
        total_files = len(files)
        
        # 存储所有提示词
        all_prompts = []
        
        for file_idx, file in enumerate(files):
            try:
                # 更新进度
                progress = 10 + int(40 * file_idx / total_files)  # 修改进度计算，预留更多进度给AI分析阶段
                processing_tasks[task_id]["progress"] = progress
                processing_tasks[task_id]["message"] = f"正在处理文件 {file_idx + 1}/{total_files}..."
                
                # 解析文件名，格式为: files_{period_id}_{filename}
                filename_parts = file.filename.split('_', 2)
                if len(filename_parts) < 3 or filename_parts[0] != 'files':
                    logger.warning(f"文件名格式不正确: {file.filename}")
                    continue
                
                period_id = filename_parts[1]
                original_filename = filename_parts[2]
                
                # 检查期间ID是否有效
                if period_id not in period_data:
                    logger.warning(f"未找到期间ID: {period_id}")
                    continue
                
                # 获取期间信息
                period_info = f"{period_data[period_id]['year']}年{period_data[period_id]['month']}月"
                
                # 检查文件格式
                file_ext = os.path.splitext(original_filename)[1].lower()
                
                # 读取文件内容
                content = await file.read()
                
                # 初始化期间结果
                if period_id not in result:
                    result[period_id] = []
                
                # 根据文件类型处理
                if file_ext in ['.jpg', '.jpeg', '.png', '.pdf']:
                    # 图片或PDF文件，使用OCR处理
                    
                    # 获取文件类型
                    image_type = file_ext.replace('.', '')
                    if image_type == 'jpg':
                        image_type = 'jpeg'
                    
                    # 转换为base64
                    image_base64 = image_to_base64(content, image_type)
                    if not image_base64:
                        logger.error(f"转换文件到base64失败: {original_filename}")
                        continue
                    
                    # 检查是否是PDF文件
                    is_pdf = file_ext.lower() == '.pdf'
                    
                    # 如果启用了隐私保护，先处理图像
                    if enable_privacy_protection:
                        # 为PDF文件添加正确的MIME类型前缀
                        if is_pdf and "data:application/pdf" not in image_base64:
                            # 移除任何可能的现有前缀
                            if "base64," in image_base64:
                                image_base64 = image_base64.split("base64,")[1]
                            # 添加PDF前缀
                            image_base64 = f"data:application/pdf;base64,{image_base64}"
                        
                        # 处理隐私保护
                        masked_image_base64 = protect_privacy(image_base64, mask_color_rgb)
                        
                        # 检查是否返回的是数组（多页PDF的情况）
                        if isinstance(masked_image_base64, list):
                            # 多页PDF，每页单独处理
                            logger.info(f"检测到多页PDF，共 {len(masked_image_base64)} 页")
                            
                            # 对每一页进行OCR识别，并合并结果
                            all_ocr_results = []
                            processed_images = []
                            
                            for page_idx, page_image_base64 in enumerate(masked_image_base64):
                                try:
                                    logger.info(f"对第 {page_idx + 1} 页进行OCR识别")
                                    page_ocr_result = call_ocr_service(page_image_base64)
                                    if page_ocr_result:
                                        # 添加页码信息
                                        page_ocr_result["page_number"] = page_idx + 1
                                        page_ocr_result["total_pages"] = len(masked_image_base64)
                                        all_ocr_results.append(page_ocr_result)
                                        processed_images.append(page_image_base64)
                                    else:
                                        logger.warning(f"第 {page_idx + 1} 页OCR识别失败")
                                except Exception as e:
                                    logger.error(f"第 {page_idx + 1} 页OCR识别出错: {e}")
                            
                            # 合并OCR结果
                            if all_ocr_results:
                                # 使用第一页的基本结构
                                ocr_result = all_ocr_results[0].copy()
                                # 添加所有页面的结果
                                ocr_result["all_pages"] = all_ocr_results
                            else:
                                ocr_result = None
                                logger.warning("所有页面OCR识别均失败")
                            
                            # 创建初始结果对象并立即返回处理后的图片
                            initial_result = {
                                "file_name": original_filename,
                                "file_type": "pdf",
                                "privacy_protected": True,
                                "is_pdf": True,
                                "is_multi_page": True,
                                "page_count": len(masked_image_base64),
                                "period": period_info,
                                "processed_images": processed_images,  # 添加处理后的图片
                                "status": "processing",  # 标记为处理中
                                "message": "图片处理完成，正在进行AI分析..."
                            }
                            
                            # 将初始结果添加到结果列表
                            result[period_id].append(initial_result)
                            
                            # 更新任务状态，提供初步结果给前端
                            processing_tasks[task_id]["progress"] = 50
                            processing_tasks[task_id]["message"] = "图片处理完成，正在进行AI分析..."
                            processing_tasks[task_id]["partial_result"] = {
                                "periods": {
                                    period_id: {
                                        "period": period_info,
                                        "files": result[period_id],
                                        "status": "images_ready"
                                    }
                                }
                            }
                            
                            # 分页处理大型文件 - 对多页PDF进行分段处理
                            processing_tasks[task_id]["message"] = f"正在分析文件 {file_idx + 1}/{total_files}..."
                            
                            # 分页处理
                            all_responses = []
                            
                            # 对每一页单独生成提示词并处理
                            for page_idx, page_ocr_result in enumerate(all_ocr_results):
                                processing_tasks[task_id]["message"] = f"正在分析第 {page_idx + 1}/{len(all_ocr_results)} 页..."
                                processing_tasks[task_id]["progress"] = 50 + int(40 * page_idx / len(all_ocr_results))
                                
                                # 生成当前页的提示词
                                page_prompt = generate_financial_prompt_template(page_ocr_result, period_info)
                                
                                # 直接发送提示词给前端
                                processing_tasks[task_id]["current_prompt"] = {
                                    "file_name": original_filename,
                                    "page": page_idx + 1,
                                    "prompt": page_prompt
                                }
                                
                                # 保存提示词到列表（保留原有功能）
                                all_prompts.append({
                                    "file_name": original_filename,
                                    "page": page_idx + 1,
                                    "prompt": page_prompt
                                })
                                
                                # 流式更新状态
                                processing_tasks[task_id]["streaming"] = True
                                processing_tasks[task_id]["current_page"] = page_idx + 1
                                processing_tasks[task_id]["total_pages"] = len(all_ocr_results)
                                processing_tasks[task_id]["stream_content"] = ""
                                
                                # 调用金投大脑API处理当前页，使用流式处理
                                async for chunk in deepseek_stream_chat_with_prompt(page_prompt):
                                    if chunk.startswith("data: "):
                                        try:
                                            data = json.loads(chunk[6:])
                                            if "content" in data:
                                                # 更新流式内容
                                                processing_tasks[task_id]["stream_content"] += data["content"]
                                        except:
                                            pass
                                
                                # 获取完整的响应
                                page_response = processing_tasks[task_id]["stream_content"]
                                all_responses.append(page_response)
                                
                                # 重置流式状态
                                processing_tasks[task_id]["streaming"] = False
                            
                            # 合并所有页面的结果
                            ai_response = "\n\n===== 多页文件分析结果 =====\n\n"
                            for page_idx, page_response in enumerate(all_responses):
                                ai_response += f"--- 第 {page_idx + 1} 页分析结果 ---\n\n"
                                ai_response += page_response
                                ai_response += "\n\n"
                            
                            # 如果有多个页面，尝试合并表格数据
                            if len(all_responses) > 1:
                                processing_tasks[task_id]["message"] = f"正在合并多页分析结果..."
                                
                                # 提取所有页面的表格数据
                                all_tables = []
                                for page_response in all_responses:
                                    tables = parse_markdown_table(page_response)
                                    if tables:
                                        all_tables.extend(tables)
                                
                                # 解析合并后的表格数据
                                table_data = all_tables
                            else:
                                # 只有一页，直接解析
                                table_data = parse_markdown_table(ai_response)
                            
                            # 更新结果对象
                            result[period_id][-1].update({
                                "ai_response": ai_response,
                                "table_data": table_data,
                                "status": "completed",  # 标记为完成
                                "message": "分析完成",
                                "prompts": [p for p in all_prompts if p["file_name"] == original_filename]  # 添加所有相关页面的提示词
                            })
                            
                        else:
                            # 单页图像，直接调用OCR
                            ocr_result = call_ocr_service(masked_image_base64)
                            
                            # 创建初始结果对象并立即返回处理后的图片
                            initial_result = {
                                "file_name": original_filename,
                                "file_type": "pdf" if is_pdf else "image",
                                "privacy_protected": True,
                                "is_pdf": is_pdf,
                                "period": period_info,
                                "processed_images": [masked_image_base64],  # 添加处理后的图片
                                "status": "processing",  # 标记为处理中
                                "message": "图片处理完成，正在进行AI分析..."
                            }
                            
                            # 将初始结果添加到结果列表
                            result[period_id].append(initial_result)
                            
                            # 更新任务状态，提供初步结果给前端
                            processing_tasks[task_id]["progress"] = 50
                            processing_tasks[task_id]["message"] = "图片处理完成，正在进行AI分析..."
                            processing_tasks[task_id]["partial_result"] = {
                                "periods": {
                                    period_id: {
                                        "period": period_info,
                                        "files": result[period_id],
                                        "status": "images_ready"
                                    }
                                }
                            }
                            
                            # 生成提示词
                            prompt = generate_financial_prompt_template(ocr_result, period_info)
                            
                            # 直接发送提示词给前端
                            processing_tasks[task_id]["current_prompt"] = {
                                "file_name": original_filename,
                                "prompt": prompt
                            }
                            
                            # 保存提示词到列表（保留原有功能）
                            all_prompts.append({
                                "file_name": original_filename,
                                "prompt": prompt
                            })
                            
                            # 流式更新状态
                            processing_tasks[task_id]["streaming"] = True
                            processing_tasks[task_id]["current_page"] = 1
                            processing_tasks[task_id]["total_pages"] = 1
                            processing_tasks[task_id]["stream_content"] = ""
                            
                            # 调用金投大脑API，使用流式处理
                            processing_tasks[task_id]["message"] = f"正在分析文件 {file_idx + 1}/{total_files}..."
                            async for chunk in deepseek_stream_chat_with_prompt(prompt):
                                if chunk.startswith("data: "):
                                    try:
                                        data = json.loads(chunk[6:])
                                        if "content" in data:
                                            # 更新流式内容
                                            processing_tasks[task_id]["stream_content"] += data["content"]
                                    except:
                                        pass
                            
                            # 获取完整的响应
                            ai_response = processing_tasks[task_id]["stream_content"]
                            
                            # 重置流式状态
                            processing_tasks[task_id]["streaming"] = False
                            
                            # 解析结果
                            table_data = parse_markdown_table(ai_response)
                            
                            # 更新结果对象
                            result[period_id][-1].update({
                                "ai_response": ai_response,
                                "table_data": table_data,
                                "status": "completed",  # 标记为完成
                                "message": "分析完成",
                                "prompts": [p for p in all_prompts if p["file_name"] == original_filename]  # 添加所有相关页面的提示词
                            })
                    else:
                        # 直接调用OCR服务
                        ocr_result = call_ocr_service(image_base64)
                        
                        # 创建初始结果对象并立即返回处理后的图片
                        initial_result = {
                            "file_name": original_filename,
                            "file_type": "pdf" if is_pdf else "image",
                            "privacy_protected": False,
                            "period": period_info,
                            "processed_images": [image_base64],  # 添加处理后的图片
                            "status": "processing",  # 标记为处理中
                            "message": "图片处理完成，正在进行AI分析..."
                        }
                        
                        # 将初始结果添加到结果列表
                        result[period_id].append(initial_result)
                        
                        # 更新任务状态，提供初步结果给前端
                        processing_tasks[task_id]["progress"] = 50
                        processing_tasks[task_id]["message"] = "图片处理完成，正在进行AI分析..."
                        processing_tasks[task_id]["partial_result"] = {
                            "periods": {
                                period_id: {
                                    "period": period_info,
                                    "files": result[period_id],
                                    "status": "images_ready"
                                }
                            }
                        }
                        
                        # 生成提示词
                        prompt = generate_financial_prompt_template(ocr_result, period_info)
                        
                        # 直接发送提示词给前端
                        processing_tasks[task_id]["current_prompt"] = {
                            "file_name": original_filename,
                            "prompt": prompt
                        }
                        
                        # 保存提示词到列表（保留原有功能）
                        all_prompts.append({
                            "file_name": original_filename,
                            "prompt": prompt
                        })
                        
                        # 流式更新状态
                        processing_tasks[task_id]["streaming"] = True
                        processing_tasks[task_id]["current_page"] = 1
                        processing_tasks[task_id]["total_pages"] = 1
                        processing_tasks[task_id]["stream_content"] = ""
                        
                        # 调用金投大脑API，使用流式处理
                        processing_tasks[task_id]["message"] = f"正在分析文件 {file_idx + 1}/{total_files}..."
                        async for chunk in deepseek_stream_chat_with_prompt(prompt):
                            if chunk.startswith("data: "):
                                try:
                                    data = json.loads(chunk[6:])
                                    if "content" in data:
                                        # 更新流式内容
                                        processing_tasks[task_id]["stream_content"] += data["content"]
                                except:
                                    pass
                        
                        # 获取完整的响应
                        ai_response = processing_tasks[task_id]["stream_content"]
                        
                        # 重置流式状态
                        processing_tasks[task_id]["streaming"] = False
                        
                        # 解析结果
                        table_data = parse_markdown_table(ai_response)
                        
                        # 更新结果对象
                        result[period_id][-1].update({
                            "ai_response": ai_response,
                            "table_data": table_data,
                            "status": "completed",  # 标记为完成
                            "message": "分析完成",
                            "prompts": [p for p in all_prompts if p["file_name"] == original_filename]  # 添加所有相关页面的提示词
                        })
                
                elif file_ext in ['.xls', '.xlsx']:
                    # Excel文件，直接处理
                    excel_data = process_excel_data(content)
                    
                    # 检查是否是多工作表的Excel
                    is_multi_sheet = isinstance(excel_data, dict) and "_sheet_info" in excel_data
                    
                    # 创建初始结果对象
                    initial_result = {
                        "file_name": original_filename,
                        "file_type": "excel",
                        "period": period_info,
                        "status": "processing",  # 标记为处理中
                        "message": "Excel文件已加载，正在进行AI分析...",
                        "is_multi_sheet": is_multi_sheet,  # 标记是否是多工作表
                        "processed_images": []  # 添加空的processed_images字段，与图片/PDF处理保持一致
                    }
                    
                    # 如果是多工作表，添加工作表信息
                    if is_multi_sheet:
                        initial_result["sheet_info"] = excel_data["_sheet_info"]
                    
                    # 将初始结果添加到结果列表
                    result[period_id].append(initial_result)
                    
                    # 更新任务状态，提供初步结果给前端
                    processing_tasks[task_id]["progress"] = 50
                    processing_tasks[task_id]["message"] = "Excel文件已加载，正在进行AI分析..."
                    processing_tasks[task_id]["partial_result"] = {
                        "periods": {
                            period_id: {
                                "period": period_info,
                                "files": result[period_id],
                                "status": "images_ready"  # 修改为"images_ready"，与图片/PDF处理保持一致
                            }
                        }
                    }
                    
                    # 处理Excel数据
                    if is_multi_sheet and len(excel_data["_sheet_info"]["sheet_names"]) > 1:
                        # 多工作表Excel，按工作表分割处理
                        sheet_names = excel_data["_sheet_info"]["sheet_names"]
                        all_responses = []
                        sheet_data = {}
                        
                        # 对每个工作表单独处理
                        for sheet_idx, sheet_name in enumerate(sheet_names):
                            if sheet_name in excel_data:
                                processing_tasks[task_id]["message"] = f"正在分析工作表 {sheet_idx + 1}/{len(sheet_names)}: {sheet_name}..."
                                processing_tasks[task_id]["progress"] = 50 + int(40 * sheet_idx / len(sheet_names))
                                
                                # 只提取当前工作表的数据
                                sheet_only_data = {
                                    "_sheet_info": {"sheet_names": [sheet_name], "sheet_count": 1},
                                    sheet_name: excel_data[sheet_name]
                                }
                                
                                # 生成当前工作表的提示词
                                sheet_prompt = generate_financial_prompt_template(sheet_only_data, period_info)
                                
                                # 直接发送提示词给前端
                                processing_tasks[task_id]["current_prompt"] = {
                                    "file_name": original_filename,
                                    "sheet": sheet_name,
                                    "prompt": sheet_prompt
                                }
                                
                                # 保存提示词到列表（保留原有功能）
                                all_prompts.append({
                                    "file_name": original_filename,
                                    "sheet": sheet_name,
                                    "prompt": sheet_prompt
                                })
                                
                                # 流式更新状态
                                processing_tasks[task_id]["streaming"] = True
                                processing_tasks[task_id]["current_page"] = sheet_idx + 1
                                processing_tasks[task_id]["total_pages"] = len(sheet_names)
                                processing_tasks[task_id]["stream_content"] = ""
                                
                                # 调用金投大脑API处理当前工作表
                                async for chunk in deepseek_stream_chat_with_prompt(sheet_prompt):
                                    if chunk.startswith("data: "):
                                        try:
                                            data = json.loads(chunk[6:])
                                            if "content" in data:
                                                # 更新流式内容
                                                processing_tasks[task_id]["stream_content"] += data["content"]
                                        except:
                                            pass
                                
                                # 获取完整的响应
                                sheet_response = processing_tasks[task_id]["stream_content"]
                                all_responses.append({"sheet": sheet_name, "response": sheet_response})
                                
                                # 解析当前工作表的表格
                                sheet_tables = parse_markdown_table(sheet_response)
                                if sheet_tables:
                                    sheet_data[sheet_name] = sheet_tables
                                
                                # 重置流式状态
                                processing_tasks[task_id]["streaming"] = False
                        
                        # 合并所有工作表的结果
                        ai_response = "\n\n===== 多工作表Excel分析结果 =====\n\n"
                        for sheet_result in all_responses:
                            sheet_name = sheet_result["sheet"]
                            sheet_response = sheet_result["response"]
                            ai_response += f"=== 工作表: {sheet_name} ===\n\n"
                            ai_response += sheet_response
                            ai_response += "\n\n"
                        
                        # 提取所有工作表的表格数据合并为一个总表
                        all_tables = []
                        for sheet_name, tables in sheet_data.items():
                            all_tables.extend(tables)
                        
                        # 更新结果对象
                        result[period_id][-1].update({
                            "ai_response": ai_response,
                            "table_data": all_tables,  # 保留整体解析结果
                            "sheet_data": sheet_data,  # 添加按工作表分类的数据
                            "status": "completed",  # 标记为完成
                            "message": "分析完成",
                            "prompts": [p for p in all_prompts if p["file_name"] == original_filename]  # 添加所有相关页面的提示词
                        })
                    else:
                        # 单工作表或简单Excel，使用原来的处理逻辑
                        # 生成提示词
                        prompt = generate_financial_prompt_template(excel_data, period_info)
                        
                        # 直接发送提示词给前端
                        processing_tasks[task_id]["current_prompt"] = {
                            "file_name": original_filename,
                            "prompt": prompt
                        }
                        
                        # 保存提示词到列表（保留原有功能）
                        all_prompts.append({
                            "file_name": original_filename,
                            "prompt": prompt
                        })
                        
                        # 流式更新状态
                        processing_tasks[task_id]["streaming"] = True
                        processing_tasks[task_id]["current_page"] = 1
                        processing_tasks[task_id]["total_pages"] = 1
                        processing_tasks[task_id]["stream_content"] = ""
                        
                        # 调用金投大脑API，使用流式处理
                        processing_tasks[task_id]["message"] = f"正在分析文件 {file_idx + 1}/{total_files}..."
                        async for chunk in deepseek_stream_chat_with_prompt(prompt):
                            if chunk.startswith("data: "):
                                try:
                                    data = json.loads(chunk[6:])
                                    if "content" in data:
                                        # 更新流式内容
                                        processing_tasks[task_id]["stream_content"] += data["content"]
                                except:
                                    pass
                        
                        # 获取完整的响应
                        ai_response = processing_tasks[task_id]["stream_content"]
                        
                        # 重置流式状态
                        processing_tasks[task_id]["streaming"] = False
                        
                        # 解析结果
                        table_data = parse_markdown_table(ai_response)
                        
                        # 如果是多工作表，为每个工作表创建单独的表格数据
                        if is_multi_sheet:
                            # 创建工作表数据结构
                            sheet_data = {}
                            sheet_names = excel_data["_sheet_info"]["sheet_names"]
                            
                            # 对于每个工作表，尝试从AI响应中提取对应的表格
                            for sheet_name in sheet_names:
                                # 查找工作表名称对应的表格部分
                                sheet_pattern = f"=== 工作表: {sheet_name} ===([\\s\\S]*?)(?:===|$)"
                                sheet_match = re.search(sheet_pattern, ai_response)
                                
                                if sheet_match:
                                    sheet_content = sheet_match.group(1).strip()
                                    # 解析该工作表的表格
                                    sheet_table = parse_markdown_table(sheet_content)
                                    if sheet_table:
                                        sheet_data[sheet_name] = sheet_table
                            
                            # 如果没有成功解析出工作表数据，使用整体解析结果
                            if not sheet_data:
                                sheet_data = {"全部数据": table_data}
                            
                            # 更新结果对象
                            result[period_id][-1].update({
                                "ai_response": ai_response,
                                "table_data": table_data,  # 保留整体解析结果
                                "sheet_data": sheet_data,  # 添加按工作表分类的数据
                                "status": "completed",  # 标记为完成
                                "message": "分析完成",
                                "prompts": [p for p in all_prompts if p["file_name"] == original_filename]  # 添加所有相关页面的提示词
                            })
                        else:
                            # 单工作表，使用原来的逻辑
                            result[period_id][-1].update({
                                "ai_response": ai_response,
                                "table_data": table_data,
                                "sheet_data": {"全部数据": table_data},  # 添加sheet_data字段
                                "status": "completed",  # 标记为完成
                                "message": "分析完成",
                                "prompts": [p for p in all_prompts if p["file_name"] == original_filename]  # 添加所有相关页面的提示词
                            })
                else:
                    logger.warning(f"不支持的文件格式: {file_ext}")
                    continue
                
                file_count += 1
                
            except Exception as e:
                logger.error(f"处理文件时出错: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
        
        # 如果没有成功处理任何文件
        if file_count == 0:
            processing_tasks[task_id]["status"] = "failed"
            processing_tasks[task_id]["message"] = "未处理任何有效文件"
            processing_tasks[task_id]["error"] = "未处理任何有效文件"
            return
        
        # 更新任务状态
        processing_tasks[task_id]["progress"] = 90
        processing_tasks[task_id]["message"] = "正在整理结果..."
        
        # 合并所有期间的数据
        final_result = {}
        for period_id, period_files in result.items():
            period_info = f"{period_data[period_id]['year']}年{period_data[period_id]['month']}月"
            
            # 合并同一期间的所有表格数据
            all_table_data = []
            all_sheet_data = {}
            
            for file_result in period_files:
                # 处理常规表格数据
                if "table_data" in file_result and file_result["table_data"]:
                    all_table_data.extend(file_result["table_data"])
                
                # 处理多工作表数据
                if "is_multi_sheet" in file_result and file_result["is_multi_sheet"] and "sheet_data" in file_result:
                    for sheet_name, sheet_table in file_result["sheet_data"].items():
                        if sheet_name not in all_sheet_data:
                            all_sheet_data[sheet_name] = []
                        all_sheet_data[sheet_name].extend(sheet_table)
            
            # 保存期间结果
            final_result[period_id] = {
                "period": period_info,
                "files": period_files,
                "table_data": all_table_data
            }
            
            # 如果有多工作表数据，也添加到结果中
            if all_sheet_data:
                final_result[period_id]["sheet_data"] = all_sheet_data
        
        # 更新任务状态
        processing_tasks[task_id]["status"] = "completed"
        processing_tasks[task_id]["progress"] = 100
        processing_tasks[task_id]["message"] = "处理完成"
        processing_tasks[task_id]["result"] = {
            "periods": final_result,
            "file_count": file_count,
            "prompts": all_prompts  # 添加所有提示词到结果中
        }
        
    except Exception as e:
        logger.error(f"处理任务时出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        
        # 更新任务状态
        processing_tasks[task_id]["status"] = "failed"
        processing_tasks[task_id]["message"] = "处理失败"
        processing_tasks[task_id]["error"] = str(e)

@router.get("/test-connection")
async def test_deepseek_connection():
    """
    测试与Deepseek API的连接是否正常
    """
    try:
        logger.info(f"测试连接到 Deepseek API: {DEEPSEEK_API_URL}")
        
        # 构建简单请求
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, are you working?"}
        ]
        
        request_body = {
            "model": DEEPSEEK_MODEL,
            "messages": messages,
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 10
        }
        
        # 发送请求
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                DEEPSEEK_API_URL,
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                return {
                    "status": "success",
                    "message": "成功连接到Deepseek API",
                    "api_url": DEEPSEEK_API_URL,
                    "model": DEEPSEEK_MODEL,
                    "response": content
                }
            else:
                return {
                    "status": "error",
                    "message": f"API返回错误: {response.status_code}",
                    "details": response.text,
                    "api_url": DEEPSEEK_API_URL
                }
    except Exception as e:
        logger.error(f"测试连接时出错: {e}")
        import traceback
        error_details = traceback.format_exc()
        
        return {
            "status": "error",
            "message": f"连接测试失败: {str(e)}",
            "details": error_details,
            "api_url": DEEPSEEK_API_URL,
            "api_key_length": len(DEEPSEEK_API_KEY) if DEEPSEEK_API_KEY else 0
        }