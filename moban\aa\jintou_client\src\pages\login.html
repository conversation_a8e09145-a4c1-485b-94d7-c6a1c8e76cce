<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>登录 - 金投大脑</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      background-color: #f5f7fa;
      color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    
    .login-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 360px;
      text-align: center;
    }
    
    .logo {
      width: 100px;
      height: 100px;
      margin: 0 auto 20px;
      display: block;
    }
    
    h1 {
      color: #2c3e50;
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 24px;
    }
    
    .form-group {
      margin-bottom: 20px;
      text-align: left;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-size: 14px;
      color: #7f8c8d;
    }
    
    input[type="text"],
    input[type="password"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .remember-me {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .remember-me input {
      margin-right: 5px;
    }
    
    button {
      background-color: #3498db;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 12px;
      width: 100%;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #2980b9;
    }
    
    .error-message {
      color: #e74c3c;
      font-size: 14px;
      margin-top: 15px;
      display: none;
    }
    
    .password-container {
      position: relative;
    }
    
    .toggle-password {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      cursor: pointer;
      padding: 0;
      width: auto;
    }
    
    .eye-icon {
      width: 20px;
      height: 20px;
      opacity: 1;
    }
    
    .version {
      margin-top: 20px;
      font-size: 0.8rem;
      color: #999;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <img src="../../assets/icon.png" alt="金投AI Logo" class="logo">
    <h1>金投大脑 - 登录</h1>
    
    <form id="login-form">
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" placeholder="请输入用户名" required>
      </div>
      
      <div class="form-group">
        <label for="password">密码</label>
        <div class="password-container">
          <input type="password" id="password" placeholder="请输入密码" required>
          <button type="button" id="toggle-password" class="toggle-password">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItZXllIj48cGF0aCBkPSJNMSAxMnMzLTggMTEtOCAxMSA4IDExIDgtMyA4LTExIDgtMTEtOC0xMS04eiI+PC9wYXRoPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiPjwvY2lyY2xlPjwvc3ZnPg==" alt="Show/Hide Password" class="eye-icon">
          </button>
        </div>
      </div>
      
      <div class="remember-me">
        <input type="checkbox" id="remember-me">
        <label for="remember-me">记住我</label>
      </div>
      
      <button type="submit" id="login-button">登录</button>
      
      <div id="login-error" class="error-message"></div>
    </form>
    
    <div class="version">版本 1.0.0 | © 2023-2024 金投大脑团队</div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const loginForm = document.getElementById('login-form');
      const usernameInput = document.getElementById('username');
      const passwordInput = document.getElementById('password');
      const loginButton = document.getElementById('login-button');
      const loginError = document.getElementById('login-error');
      const togglePasswordButton = document.getElementById('toggle-password');
      const rememberMeCheckbox = document.getElementById('remember-me');
      
      // 检查保存的凭据
      const savedUsername = localStorage.getItem('jintou_username');
      const savedPassword = localStorage.getItem('jintou_password');
      const rememberMe = localStorage.getItem('jintou_remember_me') === 'true';
      
      if (savedUsername && savedPassword && rememberMe) {
        usernameInput.value = savedUsername;
        passwordInput.value = savedPassword;
        rememberMeCheckbox.checked = true;
      }
      
      // 显示/隐藏密码
      togglePasswordButton.addEventListener('click', function() {
        if (passwordInput.type === 'password') {
          passwordInput.type = 'text';
          togglePasswordButton.querySelector('.eye-icon').style.opacity = '0.7';
        } else {
          passwordInput.type = 'password';
          togglePasswordButton.querySelector('.eye-icon').style.opacity = '1';
        }
      });
      
      // 处理登录
      loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!username || !password) {
          showError('请输入用户名和密码');
          return;
        }
        
        // 显示登录中状态
        loginButton.disabled = true;
        loginButton.textContent = '登录中...';
        
        try {
          // 保存凭据（如果用户勾选了"记住我"）
          if (rememberMeCheckbox.checked) {
            localStorage.setItem('jintou_username', username);
            localStorage.setItem('jintou_password', password);
            localStorage.setItem('jintou_remember_me', 'true');
          } else {
            localStorage.removeItem('jintou_username');
            localStorage.removeItem('jintou_password');
            localStorage.removeItem('jintou_remember_me');
          }
          
          // 发送登录请求到服务器
          const response = await fetch('http://localhost:5000/api/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              username: username,
              password: password
            })
          });
          
          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.message || '登录失败');
          }
          
          const data = await response.json();
          
          if (!data.success) {
            throw new Error(data.message || '登录失败');
          }
          
          // 保存令牌
          localStorage.setItem('jintou_token', data.token);
          
          // 保存会话状态
          sessionStorage.setItem('isLoggedIn', 'true');
          sessionStorage.setItem('username', username);
          
          // 登录成功，跳转到功能选择页面
          window.location.href = '../../index.html';
          
        } catch (error) {
          console.error('登录错误:', error);
          showError(error.message || '登录失败，请检查用户名和密码');
        } finally {
          // 恢复登录按钮状态
          loginButton.disabled = false;
          loginButton.textContent = '登录';
        }
      });
      
      // 显示错误消息
      function showError(message) {
        loginError.textContent = message;
        loginError.style.display = 'block';
      }
    });
  </script>
</body>
</html> 