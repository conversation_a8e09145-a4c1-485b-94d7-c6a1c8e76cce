2025-06-08 18:23:59,014 - api_test - INFO - 开始API响应完整性测试
2025-06-08 18:23:59,014 - api_test - INFO - 测试服务器API可用性: http://localhost:5000/api
2025-06-08 18:23:59,365 - httpx - INFO - HTTP Request: GET http://localhost:5000/api/health "HTTP/1.1 404 Not Found"
2025-06-08 18:23:59,366 - api_test - ERROR - 服务器API不可用: 404 - {"detail":"Not Found"}
2025-06-08 18:23:59,367 - api_test - WARNING - 服务器API不可用，跳过服务器API测试
2025-06-08 18:23:59,368 - api_test - INFO - 测试大模型API可用性: http://**********:8000/v1/chat/completions
2025-06-08 18:23:59,818 - httpx - INFO - HTTP Request: POST http://**********:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 18:23:59,823 - api_test - INFO - 大模型API可用
2025-06-08 18:23:59,824 - api_test - INFO - 开始直接调用大模型API测试
2025-06-08 18:23:59,824 - api_test - INFO - 发送请求到 http://**********:8000/v1/chat/completions
2025-06-08 18:23:59,824 - api_test - INFO - 请求头: Authorization: Bearer szjf@...
2025-06-08 18:23:59,824 - api_test - INFO - 请求体: {"model": "/models/Qwen3-32B", "messages": [{"role": "system", "content": "你是一个智能助手，请提供详细且全面的回答。"}, {"role": "user", "content": "请详细介绍Python的历史、特点、应用场景和未来发展，要求内容全面，至少1000字。"}], "temperature": 0.7, "max_tokens": 32000}
2025-06-08 18:25:14,491 - httpx - INFO - HTTP Request: POST http://**********:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 18:25:14,492 - api_test - INFO - API响应状态码: 200
2025-06-08 18:25:14,492 - api_test - INFO - 解析API响应...
2025-06-08 18:25:14,493 - api_test - INFO - 响应数据: {"id": "chatcmpl-6a662aeb66f94e58aa2490dbf0b8f705", "object": "chat.completion", "created": 1749378239, "model": "/models/Qwen3-32B", "choices": [{"index": 0, "message": {"role": "assistant", "reasoning_content": null, "content": "<think>\n嗯，用户让我详细介绍Python的历史、特点、应用场景和未来发展，要求内容全面，至少1000字。首先，我需要确定每个部分应该涵盖哪些内容。\n\n历史部分，应该从Guido van Rossum的创造动机开始，提到1980年代末到1991年发布第一个版本。需要提到ABC语言的影响，以及Python 2和3的分叉，还有社区的发展，比如Python Software Foundation的成立。可能还要提到一些关键版本，比如Python 3的发布时间和主要改进。\n\n特点方面，用户可能想知道Python的易读性、跨平...
2025-06-08 18:25:14,494 - api_test - INFO - 直接API调用完成，耗时: 74.67秒
2025-06-08 18:25:14,494 - api_test - INFO - 响应内容长度: 4465字符
2025-06-08 18:25:14,495 - api_test - INFO - 响应内容已保存到 direct_api_response.txt
2025-06-08 18:25:14,496 - api_test - WARNING - 无法比较响应，至少有一个响应为空
2025-06-08 18:25:14,496 - api_test - INFO - API响应完整性测试完成
2025-06-08 18:28:54,788 - api_test - INFO - 开始API响应完整性测试
2025-06-08 18:28:54,789 - api_test - INFO - 测试服务器API可用性: http://localhost:5000/api
2025-06-08 18:28:57,172 - api_test - ERROR - 服务器API连接错误: All connection attempts failed
2025-06-08 18:28:57,174 - api_test - WARNING - 服务器API不可用，跳过服务器API测试
2025-06-08 18:28:57,175 - api_test - INFO - 测试大模型API可用性: http://**********:8000/v1/chat/completions
2025-06-08 18:28:57,863 - httpx - INFO - HTTP Request: POST http://**********:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 18:28:57,864 - api_test - INFO - 大模型API可用
2025-06-08 18:28:57,866 - api_test - INFO - 开始直接调用大模型API测试
2025-06-08 18:28:57,867 - api_test - INFO - 发送请求到 http://**********:8000/v1/chat/completions
2025-06-08 18:28:57,867 - api_test - INFO - 请求头: Authorization: Bearer szjf@...
2025-06-08 18:28:57,868 - api_test - INFO - 请求体: {"model": "/models/Qwen3-32B", "messages": [{"role": "system", "content": "你是一个智能助手，请提供详细且全面的回答。"}, {"role": "user", "content": "请详细介绍Python的历史、特点、应用场景和未来发展，要求内容全面，至少1000字。"}], "temperature": 0.7, "max_tokens": 32000}
2025-06-08 18:30:15,838 - httpx - INFO - HTTP Request: POST http://**********:8000/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-08 18:30:15,841 - api_test - INFO - API响应状态码: 200
2025-06-08 18:30:15,842 - api_test - INFO - 解析API响应...
2025-06-08 18:30:15,842 - api_test - INFO - 响应数据: {"id": "chatcmpl-1c4292b83afd462b9b4276ad99c67a5b", "object": "chat.completion", "created": 1749378537, "model": "/models/Qwen3-32B", "choices": [{"index": 0, "message": {"role": "assistant", "reasoning_content": null, "content": "<think>\n嗯，用户让我详细介绍Python的历史、特点、应用场景和未来发展，要求内容全面，至少1000字。首先，我需要确定每个部分的关键点，确保覆盖所有要求的内容。\n\n先从历史开始。Python是由Guido van Rossum在1980年代末开始设计的，1991年发布第一个版本。要提到他为什么创建Python，比如对ABC语言的改进，强调代码的可读性和简洁性。然后，Python 2和3的区别，特别是2008年Python 3的发布，以及社区的迁移过程。还有近年来的发展，比如Python 3.10和3.11的性能提升，...
2025-06-08 18:30:15,843 - api_test - INFO - 直接API调用完成，耗时: 77.98秒
2025-06-08 18:30:15,843 - api_test - INFO - 响应内容长度: 5015字符
2025-06-08 18:30:15,844 - api_test - INFO - 响应内容已保存到 direct_api_response.txt
2025-06-08 18:30:15,844 - api_test - WARNING - 无法比较响应，至少有一个响应为空
2025-06-08 18:30:15,845 - api_test - INFO - API响应完整性测试完成
