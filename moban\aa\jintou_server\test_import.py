#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试导入模块
"""

import os
import sys

# 添加当前目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, current_dir)

print(f"当前Python路径: {sys.path}")
print(f"当前工作目录: {os.getcwd()}")

# 尝试直接导入
try:
    print("尝试从api.chat_api导入...")
    from api.chat_api import router as chat_router
    print("成功导入api.chat_api")
except ImportError as e:
    print(f"导入api.chat_api失败: {e}")

# 尝试使用完整路径导入
try:
    print("尝试从jintou_server.api.chat_api导入...")
    from jintou_server.api.chat_api import router as chat_router
    print("成功导入jintou_server.api.chat_api")
except ImportError as e:
    print(f"导入jintou_server.api.chat_api失败: {e}")

# 尝试直接导入模块
try:
    print("尝试导入api模块...")
    import api
    print(f"成功导入api模块: {dir(api)}")
except ImportError as e:
    print(f"导入api模块失败: {e}")

# 尝试导入jintou_server模块
try:
    print("尝试导入jintou_server模块...")
    import jintou_server
    print(f"成功导入jintou_server模块: {dir(jintou_server)}")
except ImportError as e:
    print(f"导入jintou_server模块失败: {e}")

print("测试完成") 