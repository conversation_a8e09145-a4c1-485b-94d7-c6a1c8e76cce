#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试真正的长文本分割处理
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1/document"

def test_real_long_text():
    """
    测试真正超过15000字符的长文本
    """
    print("📄 测试真正的长文本分割处理")
    print("=" * 50)
    
    # 创建一个真正超过15000字符的文本
    base_content = """
    请根据以下详细的财务信息生成完整的资产负债表：
    
    公司基本信息：
    - 公司名称：某某科技有限公司
    - 报告期间：2023年12月31日
    - 货币单位：人民币元
    
    详细的资产信息：
    流动资产包括：
    1. 货币资金：银行存款5,000,000元，现金50,000元，其他货币资金200,000元
    2. 应收账款：应收货款3,500,000元，减：坏账准备175,000元，净额3,325,000元
    3. 预付款项：预付材料款800,000元，预付租金120,000元
    4. 存货：原材料2,100,000元，在产品450,000元，产成品1,800,000元，减：存货跌价准备80,000元
    5. 其他流动资产：待摊费用150,000元，短期投资500,000元
    
    非流动资产包括：
    1. 长期股权投资：对子公司投资8,000,000元，对联营企业投资2,500,000元
    2. 固定资产：房屋建筑物原值15,000,000元，减：累计折旧3,000,000元，净值12,000,000元
    3. 机器设备原值8,500,000元，减：累计折旧2,550,000元，净值5,950,000元
    4. 运输工具原值1,200,000元，减：累计折旧720,000元，净值480,000元
    5. 无形资产：土地使用权3,500,000元，专利权800,000元，软件600,000元
    6. 长期待摊费用：装修费用450,000元，其他200,000元
    7. 递延所得税资产：280,000元
    
    负债信息：
    流动负债包括：
    1. 短期借款：银行借款4,500,000元，利率6.5%，到期日2024年6月30日
    2. 应付账款：应付材料款2,800,000元，应付设备款1,200,000元
    3. 预收款项：预收货款950,000元
    4. 应付职工薪酬：应付工资680,000元，应付社保费120,000元
    5. 应交税费：应交增值税350,000元，应交企业所得税280,000元，应交个人所得税45,000元
    6. 其他应付款：应付利息85,000元，其他150,000元
    
    非流动负债包括：
    1. 长期借款：银行长期借款12,000,000元，利率5.8%，到期日2028年12月31日
    2. 应付债券：公司债券5,000,000元，票面利率7.2%
    3. 长期应付款：融资租赁款1,500,000元
    4. 递延所得税负债：150,000元
    
    所有者权益信息：
    1. 实收资本：注册资本20,000,000元，实收资本20,000,000元
    2. 资本公积：股本溢价8,500,000元，其他资本公积1,200,000元
    3. 盈余公积：法定盈余公积2,800,000元，任意盈余公积500,000元
    4. 未分配利润：期初未分配利润5,200,000元，本期净利润3,800,000元，减：提取盈余公积380,000元，分配现金股利1,500,000元，期末未分配利润7,120,000元
    
    其他重要信息：
    1. 本期发生的重大会计政策变更和会计估计变更
    2. 重要的非经常性损益项目
    3. 关联方交易情况
    4. 或有事项和承诺事项
    5. 期后事项
    
    请根据以上信息，严格按照企业会计准则的要求，生成标准格式的资产负债表，确保：
    1. 所有数据计算准确
    2. 资产总计等于负债和所有者权益总计
    3. 格式规范，科目齐全
    4. 数据逻辑合理
    """
    
    # 重复内容以确保超过15000字符
    repeated_content = base_content * 10  # 重复10次以确保超过15000字符
    
    print(f"生成的长文本长度: {len(repeated_content)} 字符")
    print(f"是否超过15000字符: {'是' if len(repeated_content) > 15000 else '否'}")
    
    request_data = {
        'prompt': repeated_content,
        'template_name': '一般企业 (2).xls',
        'selected_files': ['超长文本测试.pdf']
    }
    
    try:
        print("\n正在处理超长文本，请稍候...")
        response = requests.post(f'{BASE_URL}/call-qwen-api', json=request_data, timeout=300)
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('ai_response', '')
            
            print(f"✅ 超长文本处理成功")
            print(f"输入长度: {len(repeated_content)} 字符")
            print(f"输出长度: {len(ai_response)} 字符")
            print(f"包含分割标识: {'是' if '分割处理汇总' in ai_response else '否'}")
            print(f"前端应显示: {'分割处理' if '分割处理汇总' in ai_response else '单次处理'}")
            
            # 显示分割处理的详细信息
            if '分割处理汇总' in ai_response:
                lines = ai_response.split('\n')
                for line in lines:
                    if '分割为:' in line or '处理完成的部分:' in line or '原始提示词长度:' in line:
                        print(f"📊 {line.strip()}")
            
            # 显示回答预览
            print(f"\n📄 AI回答预览（前500字符）:")
            print(ai_response[:500] + "...")
            
        else:
            print(f"❌ 超长文本处理失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 超长文本处理异常: {str(e)}")

if __name__ == "__main__":
    test_real_long_text()
