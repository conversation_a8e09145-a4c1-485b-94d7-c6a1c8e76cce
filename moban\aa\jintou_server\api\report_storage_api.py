from fastapi import APIRouter, Depends, HTTPException, Request, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import json
import os
import logging
from datetime import datetime
import sys

# 添加当前目录的父目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 使用绝对导入
from security.jwt_handler import get_current_user

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(tags=["报表存储"])

# 报表数据目录
REPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "reports")

# 确保报表数据目录存在
os.makedirs(REPORTS_DIR, exist_ok=True)

class ReportData(BaseModel):
    """报表数据模型"""
    company_name: str = Field(..., description="公司名称")
    report_year: str = Field(..., description="报表年份")
    report_month: str = Field(..., description="报表月份")
    table_data: str = Field(..., description="表格数据（Markdown格式）")
    source_image_count: Optional[int] = Field(None, description="源图片数量")
    notes: Optional[str] = Field(None, description="备注信息")

class CompanyInfo(BaseModel):
    """公司信息模型"""
    name: str = Field(..., description="公司名称")
    report_count: int = Field(..., description="报表数量")
    last_update: str = Field(..., description="最后更新时间")

class ReportInfo(BaseModel):
    """报表信息模型"""
    year: str = Field(..., description="年份")
    month: str = Field(..., description="月份")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

@router.post("/reports/save")
async def save_report(
    report: ReportData,
    current_user: dict = Depends(get_current_user),
):
    """
    保存报表数据
    """
    try:
        # 验证必要参数
        if not report.company_name:
            raise HTTPException(status_code=400, detail="缺少必要参数：company_name")
        
        if not report.report_year:
            raise HTTPException(status_code=400, detail="缺少必要参数：report_year")
        
        if not report.report_month:
            raise HTTPException(status_code=400, detail="缺少必要参数：report_month")
        
        if not report.table_data:
            raise HTTPException(status_code=400, detail="缺少必要参数：table_data")
        
        # 创建公司目录
        company_dir = os.path.join(REPORTS_DIR, report.company_name)
        os.makedirs(company_dir, exist_ok=True)
        
        # 创建年份目录
        year_dir = os.path.join(company_dir, report.report_year)
        os.makedirs(year_dir, exist_ok=True)
        
        # 报表文件路径
        report_file = os.path.join(year_dir, f"{report.report_month}.json")
        
        # 当前时间
        now = datetime.now().isoformat()
        
        # 准备报表数据
        report_data = {
            "company_name": report.company_name,
            "report_year": report.report_year,
            "report_month": report.report_month,
            "table_data": report.table_data,
            "source_image_count": report.source_image_count,
            "notes": report.notes,
            "created_by": current_user.get("username", "unknown"),
            "created_at": now,
            "updated_at": now
        }
        
        # 如果文件已存在，更新更新时间
        if os.path.exists(report_file):
            try:
                with open(report_file, "r", encoding="utf-8") as f:
                    existing_data = json.load(f)
                    report_data["created_at"] = existing_data.get("created_at", now)
                    report_data["updated_at"] = now
            except Exception as e:
                logger.error(f"读取现有报表文件时出错: {str(e)}")
        
        # 保存报表数据
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "message": "报表数据保存成功",
            "file_path": report_file
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"保存报表数据时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存报表数据失败: {str(e)}")

@router.get("/reports/companies")
async def get_companies(
    current_user: dict = Depends(get_current_user),
):
    """
    获取所有公司列表
    """
    try:
        companies = []
        
        # 检查报表目录是否存在
        if not os.path.exists(REPORTS_DIR):
            return {"companies": []}
        
        # 遍历报表目录
        for company_name in os.listdir(REPORTS_DIR):
            company_dir = os.path.join(REPORTS_DIR, company_name)
            
            # 跳过非目录
            if not os.path.isdir(company_dir):
                continue
            
            # 计算报表数量和最后更新时间
            report_count = 0
            last_update = "1970-01-01T00:00:00"
            
            for year in os.listdir(company_dir):
                year_dir = os.path.join(company_dir, year)
                
                if not os.path.isdir(year_dir):
                    continue
                
                for month_file in os.listdir(year_dir):
                    if month_file.endswith(".json"):
                        report_count += 1
                        
                        # 读取报表文件获取更新时间
                        try:
                            with open(os.path.join(year_dir, month_file), "r", encoding="utf-8") as f:
                                report_data = json.load(f)
                                updated_at = report_data.get("updated_at", "")
                                
                                if updated_at > last_update:
                                    last_update = updated_at
                        except Exception as e:
                            logger.error(f"读取报表文件时出错: {str(e)}")
            
            # 添加到公司列表
            companies.append({
                "name": company_name,
                "report_count": report_count,
                "last_update": last_update
            })
        
        # 按报表数量和最后更新时间排序
        companies.sort(key=lambda x: (x["report_count"], x["last_update"]), reverse=True)
        
        return {"companies": companies}
    except Exception as e:
        logger.exception(f"获取公司列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取公司列表失败: {str(e)}")

@router.get("/reports/company/{company_name}")
async def get_company_reports(
    company_name: str,
    current_user: dict = Depends(get_current_user),
):
    """
    获取指定公司的所有报表
    """
    try:
        reports = []
        
        # 公司目录
        company_dir = os.path.join(REPORTS_DIR, company_name)
        
        # 检查公司目录是否存在
        if not os.path.exists(company_dir):
            return {"reports": []}
        
        # 遍历年份目录
        for year in sorted(os.listdir(company_dir), reverse=True):
            year_dir = os.path.join(company_dir, year)
            
            if not os.path.isdir(year_dir):
                continue
            
            # 遍历月份文件
            for month_file in sorted(os.listdir(year_dir)):
                if month_file.endswith(".json"):
                    month = month_file.replace(".json", "")
                    
                    # 读取报表文件获取详细信息
                    try:
                        with open(os.path.join(year_dir, month_file), "r", encoding="utf-8") as f:
                            report_data = json.load(f)
                            
                            reports.append({
                                "year": year,
                                "month": month,
                                "created_at": report_data.get("created_at", ""),
                                "updated_at": report_data.get("updated_at", "")
                            })
                    except Exception as e:
                        logger.error(f"读取报表文件时出错: {str(e)}")
        
        return {"reports": reports}
    except Exception as e:
        logger.exception(f"获取公司报表列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取公司报表列表失败: {str(e)}")

@router.get("/reports/{company_name}/{year}/{month}")
async def get_report(
    company_name: str,
    year: str,
    month: str,
    current_user: dict = Depends(get_current_user),
):
    """
    获取指定报表数据
    """
    try:
        # 报表文件路径
        report_file = os.path.join(REPORTS_DIR, company_name, year, f"{month}.json")
        
        # 检查报表文件是否存在
        if not os.path.exists(report_file):
            raise HTTPException(status_code=404, detail="报表不存在")
        
        # 读取报表数据
        with open(report_file, "r", encoding="utf-8") as f:
            report_data = json.load(f)
        
        return report_data
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取报表数据时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报表数据失败: {str(e)}") 