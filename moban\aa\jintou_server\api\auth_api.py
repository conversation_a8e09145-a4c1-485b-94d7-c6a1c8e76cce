#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 认证API服务
提供用户登录和身份验证功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Optional
from datetime import timedelta
import logging
from passlib.context import CryptContext

# 导入JWT处理工具
from jintou_server.security.jwt_handler import (
    create_access_token, 
    get_current_user,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    mock_users_db
)

# 创建路由
router = APIRouter(
    prefix="/api/auth",
    tags=["认证服务"],
    responses={404: {"description": "Not found"}},
)

# 配置日志
logger = logging.getLogger("jintou_brain.auth")

# 密码哈希工具
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 数据模型
class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

class UserInfo(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None

def verify_password(plain_password, hashed_password):
    """验证密码是否正确"""
    return pwd_context.verify(plain_password, hashed_password)

def get_user(username: str):
    """从数据库获取用户（这里使用模拟数据）"""
    if username in mock_users_db:
        return mock_users_db[username]
    return None

def authenticate_user(username: str, password: str):
    """验证用户身份"""
    user = get_user(username)
    if not user:
        return False
    if not verify_password(password, user["hashed_password"]):
        return False
    return user

@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    用户登录获取访问令牌
    使用用户名和密码进行身份验证，返回JWT令牌
    """
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        logger.warning(f"登录失败：用户名或密码错误 - {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否被禁用
    if user.get("disabled", False):
        logger.warning(f"登录失败：用户已被禁用 - {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, 
        expires_delta=access_token_expires
    )
    
    logger.info(f"用户登录成功: {form_data.username}")
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }

@router.get("/me", response_model=UserInfo)
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """
    获取当前登录用户信息
    需要有效的JWT令牌
    """
    username = current_user.get("sub")
    user = get_user(username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return {
        "username": user["username"],
        "email": user.get("email"),
        "full_name": user.get("full_name")
    }

@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """
    用户登出
    在实际实现中，可能需要将令牌加入黑名单或执行其他清理操作
    """
    logger.info(f"用户登出: {current_user.get('sub')}")
    return {"detail": "登出成功"}

@router.get("/status")
async def auth_status():
    """
    认证服务状态检查
    """
    return {
        "status": "运行中",
        "service": "金投大脑认证服务",
        "version": "1.0.0"
    } 