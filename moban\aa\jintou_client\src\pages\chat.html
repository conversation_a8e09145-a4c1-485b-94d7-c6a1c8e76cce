<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金投大脑 - 智能问答</title>
    <link rel="stylesheet" href="../css/styles.css">
    <!-- 添加Prism.js代码高亮样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css">
    <style>
        /* 添加logo样式 */
        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 1rem;
            display: block;
        }
        
        .login-container {
            max-width: 400px;
            margin: 50px auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            text-align: center;
        }
        
        .chat-logo {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        .version {
            margin-top: 1rem;
            font-size: 0.8rem;
            color: #999;
            text-align: center;
        }

        /* 会话管理样式 */
        .chat-layout {
            display: flex;
            height: 100%;
            min-height: calc(100vh - 40px);
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            text-align: center;
        }
        
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }
        
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            max-height: calc(100vh - 40px);
        }
        
        .session-item {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .session-item:hover {
            background: #f1f3f4;
        }
        
        .session-item.active {
            background: #e8f0fe;
            font-weight: bold;
        }
        
        .session-info {
            flex: 1;
            overflow: hidden;
        }
        
        .session-title {
            font-size: 14px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .session-preview {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .session-actions {
            display: none;
            margin-left: 5px;
        }
        
        .session-item:hover .session-actions {
            display: block;
        }
        
        .session-delete-btn {
            background: none;
            border: none;
            color: #d32f2f;
            cursor: pointer;
            font-size: 16px;
            padding: 2px;
            opacity: 0.7;
        }
        
        .session-delete-btn:hover {
            opacity: 1;
        }
        
        .chat-actions-bar {
            display: flex;
            justify-content: space-between;
            padding: 5px 10px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }
        
        /* 侧边栏切换按钮 */
        .toggle-sidebar-btn {
            position: absolute;
            left: 250px;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border: 1px solid #ddd;
            border-left: none;
            border-radius: 0 4px 4px 0;
            width: 20px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            transition: left 0.3s;
        }
        
        .sidebar.collapsed + .toggle-sidebar-btn {
            left: 0;
        }
        
        /* 文件上传按钮 */
        .file-upload-btn {
            background: none;
            border: none;
            color: #555;
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
            margin-right: 5px;
            border-radius: 4px;
        }
        
        .file-upload-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .file-input {
            display: none;
        }
        
        /* 键盘快捷键提示 */
        .keyboard-shortcut-hint {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            color: #999;
            cursor: pointer;
        }
        
        .keyboard-shortcut-hint:hover {
            text-decoration: underline;
        }

        /* 消息容器样式修改 */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            min-height: 300px;
            display: flex;
            flex-direction: column;
        }
        
        /* 优化输入框容器 */
        .input-container {
            display: flex;
            padding: 15px;
            background: #f5f7fa;
            border-top: 1px solid #ddd;
            position: relative;
        }
        
        /* 确保输入框能够自适应高度 */
        .message-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            resize: none;
            min-height: 44px;
            max-height: 200px;
            overflow-y: auto;
            margin-right: 10px;
            transition: border-color 0.3s;
        }
        
        /* 优化代码块显示 */
        .code-block-wrapper {
            position: relative;
            margin: 1em 0;
            width: 100%;
            overflow: visible;
        }
        
        pre {
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-width: 100%;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录界面 -->
        <div id="login-form" class="login-form">
            <div class="login-header">
                <img src="data:image/png;base64,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" alt="金投AI Logo" class="login-logo">
                <h2>金投大脑 - 企业AI助手</h2>
            </div>
            <div id="login-error" class="error-message"></div>
            <div class="form-group">
                <label for="login-username">用户名</label>
                <input type="text" id="login-username" placeholder="请输入用户名" required>
            </div>
            <div class="form-group">
                <label for="login-password">密码</label>
                <div class="password-container">
                    <input type="password" id="login-password" placeholder="请输入密码" required>
                    <button type="button" id="toggle-password" class="toggle-password" title="显示/隐藏密码">
                        <span class="eye-icon">👁️</span>
                    </button>
                </div>
            </div>
            <div class="form-group remember-me">
                <input type="checkbox" id="remember-me">
                <label for="remember-me">记住我</label>
            </div>
            <button type="submit" id="login-button" class="login-button">登录</button>
            <div class="login-footer">
                <p>版本 1.0.0 | © 2023-2024 金投大脑团队</p>
                <p class="connection-status" id="connection-status">正在连接到服务器...</p>
            </div>
        </div>

        <!-- 聊天界面 -->
        <div id="chat-container" class="chat-container" style="display: none;">
            <div class="chat-layout">
                <!-- 侧边栏 - 会话管理 -->
                <div class="sidebar">
                    <div class="sidebar-header">
                        <button class="btn" id="newChatBtn">新建对话</button>
                    </div>
                    <div class="sidebar-content" id="sessionList">
                        <!-- 会话列表将在这里动态加载 -->
                    </div>
                    <div class="sidebar-footer">
                        <button id="import-session-btn" class="btn-light btn-small" title="导入会话">
                            <span>导入会话</span>
                        </button>
                        <input type="file" id="import-session-file" class="file-input" accept=".json">
                    </div>
                </div>
                
                <!-- 侧边栏切换按钮 -->
                <button id="toggle-sidebar-btn" class="toggle-sidebar-btn" title="切换侧边栏 (Ctrl+B)">
                    <span>◀</span>
                </button>
                
                <!-- 主聊天区域 -->
                <div class="chat-main">
                    <div class="chat-header">
                        <div class="chat-title">
                            <img src="../../assets/icon.png" alt="金投AI Logo" class="chat-logo">
                            金投大脑 - 智能助手
                        </div>
                        <div class="chat-actions">
                            <select id="model-selector" class="model-selector">
                                <option value="/models/Qwen3-32B" selected>Qwen3-32B</option>
                            </select>
                            <button id="model-settings-btn" class="settings-button" title="模型参数设置 (Ctrl+,)">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
                            </button>
                            <button id="back-to-home" class="back-button" title="返回功能选择页面">返回主页</button>
                            <button id="logout-button" class="logout-button" onclick="logout()">退出</button>
                        </div>
                    </div>
                    
                    <div class="chat-actions-bar">
                        <div>
                            <button class="btn-light btn-small" id="clearChatBtn" title="清空当前会话消息 (Ctrl+L)">清空聊天</button>
                            <button class="btn-light btn-small" id="deleteSessionBtn" title="删除当前会话">删除会话</button>
                            <button class="btn-light btn-small" id="export-session-btn" title="导出当前会话 (Ctrl+E)">导出会话</button>
                        </div>
                        <div class="status-info" id="statusBar">
                            已连接到服务器
                        </div>
                    </div>

                    <div id="messages-container" class="messages-container">
                        <!-- 消息将在这里动态添加 -->
                        <div class="welcome-message">
                            <p>欢迎使用金投大脑智能助手！请在下方输入您的问题。</p>
                            <p>使用 <kbd>Ctrl</kbd> + <kbd>Enter</kbd> 发送消息，<kbd>/</kbd> 聚焦输入框，<kbd>Shift</kbd> + <kbd>?</kbd> 查看所有快捷键。</p>
                        </div>
                    </div>
                    <div class="input-container">
                        <button id="file-upload-btn" class="file-upload-btn" title="上传文件">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                        </button>
                        <input type="file" id="file-input" class="file-input">
                        <textarea id="message-input" class="message-input" placeholder="请输入您的问题... (按 / 聚焦)" rows="1"></textarea>
                        <button id="send-button" class="send-button" title="发送 (Ctrl+Enter)">发送</button>
                    </div>
                    <div class="keyboard-shortcut-hint" onclick="shortcutManager.showHelp()">
                        <kbd>?</kbd> 查看快捷键
                    </div>
                </div>
            </div>
            <div class="version">版本 1.0.0 | © 2023-2024 金投大脑团队</div>
        </div>
    </div>
    
    <!-- 模型参数设置对话框 -->
    <div id="model-settings-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>模型参数设置</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="temperature-slider">温度 (Temperature): <span id="temperature-value">0.7</span></label>
                    <input type="range" id="temperature-slider" min="0" max="2" step="0.1" value="0.7">
                    <p class="setting-description">控制回答的随机性。较低的值使回答更确定，较高的值使回答更多样化。</p>
                </div>
                <div class="form-group">
                    <label for="max-tokens-input">最大生成长度: <span id="max-tokens-value">2000</span></label>
                    <input type="range" id="max-tokens-slider" min="100" max="4000" step="100" value="2000">
                    <p class="setting-description">控制AI回答的最大长度。</p>
                </div>
                <div class="form-group">
                    <label for="system-prompt">系统提示词:</label>
                    <textarea id="system-prompt" rows="4" placeholder="设置AI的行为和角色...">你是一个智能助手，由金投大脑提供支持。请用简洁、专业的方式回答问题。</textarea>
                    <p class="setting-description">定义AI的角色和行为方式。</p>
                </div>
            </div>
            <div class="modal-footer">
                <button id="save-settings" class="btn">保存设置</button>
                <button id="reset-settings" class="btn-light">重置默认</button>
            </div>
        </div>
    </div>

    <!-- 添加Markdown解析和代码高亮库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-java.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-c.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-cpp.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-csharp.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-sql.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-json.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-markdown.min.js"></script>

    <script src="../js/index.js"></script>
</body>
</html> 