<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财报视觉分析(直接转发) - 金投大脑</title>
    <link rel="stylesheet" href="../css/styles.css">
    <!-- 添加Prism.js代码高亮样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Marked.js 用于Markdown渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <style>
        /* 聊天布局 */
        .chat-layout {
            display: flex;
            height: 100%;
            min-height: calc(100vh - 60px);
        }
        
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            max-height: calc(100vh - 60px);
        }
        
        /* 消息容器样式 */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            min-height: 300px;
            display: flex;
            flex-direction: column;
        }
        
        /* 消息样式 */
        .message {
            display: flex;
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        
        .user-avatar {
            background-color: #007bff;
        }
        
        .assistant-avatar {
            background-color: #28a745;
        }
        
        .message-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: calc(100% - 55px);
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .message-sender {
            font-weight: 500;
        }
        
        .message-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .message-content {
            padding: 12px 15px;
            border-radius: 8px;
            word-wrap: break-word;
        }
        
        .user-message .message-content {
            background-color: #e6f2ff;
        }
        
        .assistant-message .message-content {
            background-color: #f8f9fa;
        }
        
        /* 输入框容器 */
        .input-container {
            display: flex;
            flex-direction: column;
            padding: 15px;
            background: #f5f7fa;
            border-top: 1px solid #ddd;
            position: relative;
        }
        
        /* 图片上传和预览区域 */
        .image-upload-area {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .file-upload-btn {
            background: none;
            border: none;
            color: #007bff;
            cursor: pointer;
            font-size: 16px;
            padding: 5px 10px;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        
        .file-upload-btn:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        .file-upload-btn i {
            margin-right: 5px;
        }
        
        .file-input {
            display: none;
        }
        
        .image-preview-container {
            display: flex;
            align-items: center;
            margin-left: 10px;
            position: relative;
            max-width: 300px;
        }
        
        .image-preview {
            max-height: 60px;
            max-width: 100%;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .remove-file-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #dc3545;
            color: white;
            border: none;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        /* 消息输入框 */
        .message-input-area {
            display: flex;
        }
        
        .message-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            resize: none;
            min-height: 44px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .send-btn {
            margin-left: 10px;
            padding: 0 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .send-btn:hover {
            background-color: #0069d9;
        }
        
        .send-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        /* 清除聊天按钮 */
        .clear-chat-btn {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        
        .clear-chat-btn:hover {
            background-color: rgba(220, 53, 69, 0.1);
        }
        
        .clear-chat-btn i {
            margin-right: 5px;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* 加载指示器 */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            display: none;
            z-index: 1000;
        }
        
        .loading-indicator.active {
            display: block;
            display: flex;
            align-items: center;
        }
        
        .loading-indicator::before {
            content: "";
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 错误和信息提示 */
        .message-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 300px;
        }
        
        .message-notification.active {
            opacity: 1;
        }
        
        .error-message {
            background-color: #dc3545;
        }
        
        .info-message {
            background-color: #17a2b8;
        }
        
        /* 错误信息 */
        .error-info {
            color: #dc3545;
            padding: 8px 12px;
            background-color: #f8d7da;
            border-radius: 4px;
            margin-top: 5px;
            font-size: 14px;
        }
        
        /* 图片模态框 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1050;
            padding-top: 50px;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.9);
        }
        
        .image-modal.active {
            display: block;
        }
        
        .modal-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 80%;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        
        /* 消息图片 */
        .message-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 4px;
            margin-top: 10px;
            cursor: pointer;
            border: 1px solid #ddd;
        }
        
        /* 顶部导航栏 */
        .top-nav {
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-logo {
            display: flex;
            align-items: center;
        }
        
        .nav-logo img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .nav-actions {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            margin-right: 15px;
            padding: 5px 10px;
            background-color: transparent;
            border: 1px solid #007bff;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .back-btn:hover {
            background-color: #007bff;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .logout-btn {
            margin-left: 10px;
            padding: 5px 10px;
            background-color: transparent;
            border: 1px solid #dc3545;
            color: #dc3545;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            }
        
        .logout-btn:hover {
            background-color: #dc3545;
            color: white;
        }

        /* 提示信息样式 */
        .mode-info {
            background-color: #17a2b8;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="top-nav">
        <div class="nav-logo">
            <img src="../../assets/icon.png" alt="金投大脑Logo">
            <div class="nav-title">财报视觉分析(直接转发)</div>
        </div>
        <div class="nav-actions">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> 返回主页
            </button>
            <div class="user-info">
                <div class="user-avatar" id="user-avatar">U</div>
                <span id="user-name">用户</span>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>

    <!-- 聊天主界面 -->
    <div class="chat-layout">
        <div class="chat-main">
            <!-- 提示信息 -->
            <div class="mode-info">
                您正在使用直接转发模式，上传的图片和输入的问题将直接发送给模型，不经过任何处理。
            </div>
            
            <!-- 消息容器 -->
            <div class="messages-container" id="chat-messages">
                <!-- 消息将动态添加在这里 -->
            </div>
            
            <!-- 输入区域 -->
            <div class="input-container">
                <div class="action-buttons">
                    <div class="image-upload-area">
                        <label for="file-upload" class="file-upload-btn">
                            <i class="fas fa-image"></i> 上传图片
                        </label>
                        <input type="file" id="file-upload" class="file-input" accept=".jpg,.jpeg,.png">
                        
                        <div class="image-preview-container" id="image-preview-container">
                            <!-- 图片预览将显示在这里 -->
                        </div>
                    </div>
                    
                    <button id="clear-chat" class="clear-chat-btn">
                        <i class="fas fa-trash"></i> 清空对话
                    </button>
                </div>
                
                <!-- 消息输入区域 -->
                <div class="message-input-area">
                    <textarea id="message-input" class="message-input" placeholder="输入你的问题，内容会直接转发给模型，不进行任何修改..."></textarea>
                    <button id="send-btn" class="send-btn">发送</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载指示器 -->
    <div id="loading-indicator" class="loading-indicator"></div>
    
    <!-- 消息通知 -->
    <div id="error-message" class="message-notification error-message"></div>
    <div id="info-message" class="message-notification info-message"></div>
    
    <!-- 图片查看模态框 -->
    <div id="image-modal" class="image-modal">
        <span class="close-modal" onclick="closeImageModal()">&times;</span>
        <img class="modal-content" id="modal-image">
    </div>
    
    <!-- 引入页面脚本 -->
    <script src="../js/ocr-vision-direct.js"></script>
    
    <!-- 导入Prism.js用于代码高亮 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-json.min.js"></script>
</body>
</html> 