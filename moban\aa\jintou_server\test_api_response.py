#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API响应完整性
"""

import asyncio
import httpx
import json
import os
import time
import logging
import traceback
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("api_test.log", encoding="utf-8")
    ]
)
logger = logging.getLogger("api_test")

# API配置
API_URL = "http://localhost:5000/api"
DEEPSEEK_API_URL = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

# 测试服务器API是否可用
async def test_server_availability():
    """测试服务器API是否可用"""
    logger.info(f"测试服务器API可用性: {API_URL}")
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{API_URL}/status")
            if response.status_code == 200:
                logger.info("服务器API可用")
                return True
            else:
                logger.error(f"服务器API不可用: {response.status_code} - {response.text}")
                return False
    except Exception as e:
        logger.error(f"服务器API连接错误: {str(e)}")
        return False

# 测试大模型API是否可用
async def test_model_availability():
    """测试大模型API是否可用"""
    logger.info(f"测试大模型API可用性: {DEEPSEEK_API_URL}")
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                DEEPSEEK_API_URL,
                json={
                    "model": DEEPSEEK_MODEL,
                    "messages": [
                        {"role": "user", "content": "Hello"}
                    ],
                    "max_tokens": 10
                },
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                }
            )
            if response.status_code == 200:
                logger.info("大模型API可用")
                return True
            else:
                logger.error(f"大模型API不可用: {response.status_code} - {response.text}")
                return False
    except Exception as e:
        logger.error(f"大模型API连接错误: {str(e)}")
        logger.error(traceback.format_exc())
        return False

async def test_direct_api():
    """直接调用大模型API测试"""
    logger.info("开始直接调用大模型API测试")
    
    # 构建请求
    request_data = {
        "model": DEEPSEEK_MODEL,
        "messages": [
            {"role": "system", "content": "你是一个智能助手，请提供详细且全面的回答。"},
            {"role": "user", "content": "请详细介绍Python的历史、特点、应用场景和未来发展，要求内容全面，至少1000字。"}
        ],
        "temperature": 0.7,
        "max_tokens": 32000
    }
    
    start_time = time.time()
    
    try:
        logger.info(f"发送请求到 {DEEPSEEK_API_URL}")
        logger.info(f"请求头: Authorization: Bearer {DEEPSEEK_API_KEY[:5]}...")
        logger.info(f"请求体: {json.dumps(request_data, ensure_ascii=False)}")
        
        async with httpx.AsyncClient(timeout=300.0) as client:
            response = await client.post(
                DEEPSEEK_API_URL,
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                }
            )
            
            logger.info(f"API响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                logger.error(f"API错误: {response.status_code} - {response.text}")
                return
            
            logger.info("解析API响应...")
            data = response.json()
            logger.info(f"响应数据: {json.dumps(data, ensure_ascii=False)[:500]}...")
            
            content = data["choices"][0]["message"]["content"]
            
            logger.info(f"直接API调用完成，耗时: {time.time() - start_time:.2f}秒")
            logger.info(f"响应内容长度: {len(content)}字符")
            
            # 保存响应内容到文件
            with open("direct_api_response.txt", "w", encoding="utf-8") as f:
                f.write(content)
            
            logger.info(f"响应内容已保存到 direct_api_response.txt")
            
            return content
    
    except Exception as e:
        logger.error(f"直接API调用错误: {str(e)}")
        logger.error(traceback.format_exc())
        return None

async def test_server_api():
    """通过服务器API测试"""
    logger.info("开始通过服务器API测试")
    
    # 首先登录获取token
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            login_response = await client.post(
                f"{API_URL}/login",
                json={
                    "username": "admin",
                    "password": "admin123"
                }
            )
            
            if login_response.status_code != 200:
                logger.error(f"登录失败: {login_response.status_code} - {login_response.text}")
                return
            
            login_data = login_response.json()
            token = login_data["token"]
            
            logger.info("登录成功，获取token")
            
            # 创建会话
            session_response = await client.post(
                f"{API_URL}/chat/sessions",
                json={"title": "API测试会话"},
                headers={"Authorization": f"Bearer {token}"}
            )
            
            if session_response.status_code != 200:
                logger.error(f"创建会话失败: {session_response.status_code} - {session_response.text}")
                return
            
            session_data = session_response.json()
            session_id = session_data["session"]["id"]
            
            logger.info(f"创建会话成功，ID: {session_id}")
            
            # 构建流式请求URL
            message_text = "请详细介绍Python的历史、特点、应用场景和未来发展，要求内容全面，至少1000字。"
            encoded_message = httpx.QueryParams({"message": message_text}).get("message")
            url = f"{API_URL}/chat/stream?session_id={session_id}&token={token}&model={DEEPSEEK_MODEL}&max_tokens=32000&message={encoded_message}"
            
            logger.info(f"开始流式请求: {url}")
            
            start_time = time.time()
            full_response = ""
            
            async with client.stream("GET", url) as stream:
                async for line in stream.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]
                        if data == "[DONE]":
                            logger.info("收到流式响应结束标记 [DONE]")
                            continue
                        
                        try:
                            json_data = json.loads(data)
                            if "content" in json_data:
                                full_response += json_data["content"]
                                # 每收到100个字符记录一次
                                if len(full_response) % 1000 == 0:
                                    logger.info(f"已接收 {len(full_response)} 字符")
                        except json.JSONDecodeError:
                            logger.warning(f"JSON解析错误: {data}")
                            continue
            
            logger.info(f"服务器API调用完成，耗时: {time.time() - start_time:.2f}秒")
            logger.info(f"响应内容长度: {len(full_response)}字符")
            
            # 保存响应内容到文件
            with open("server_api_response.txt", "w", encoding="utf-8") as f:
                f.write(full_response)
            
            logger.info(f"响应内容已保存到 server_api_response.txt")
            
            return full_response
    
    except Exception as e:
        logger.error(f"服务器API调用错误: {str(e)}")
        logger.error(traceback.format_exc())
        return None

async def compare_responses(direct_response, server_response):
    """比较两个API的响应"""
    if direct_response is None or server_response is None:
        logger.error("无法比较响应，至少有一个响应为空")
        return
    
    # 计算响应长度
    direct_len = len(direct_response)
    server_len = len(server_response)
    
    # 计算相似度（简单比较）
    min_len = min(direct_len, server_len)
    same_chars = sum(1 for i in range(min_len) if direct_response[i] == server_response[i])
    similarity = same_chars / min_len * 100
    
    logger.info(f"直接API响应长度: {direct_len}字符")
    logger.info(f"服务器API响应长度: {server_len}字符")
    logger.info(f"长度差异: {abs(direct_len - server_len)}字符")
    logger.info(f"内容相似度: {similarity:.2f}%")
    
    if direct_len > server_len:
        logger.warning("服务器API响应内容不完整，可能存在截断")
    elif server_len > direct_len:
        logger.warning("服务器API响应内容比直接API更长，可能存在额外内容")
    
    # 保存比较结果
    with open("api_comparison.txt", "w", encoding="utf-8") as f:
        f.write(f"直接API响应长度: {direct_len}字符\n")
        f.write(f"服务器API响应长度: {server_len}字符\n")
        f.write(f"长度差异: {abs(direct_len - server_len)}字符\n")
        f.write(f"内容相似度: {similarity:.2f}%\n\n")
        
        if direct_len != server_len:
            f.write("--- 差异分析 ---\n")
            if direct_len > server_len:
                f.write(f"服务器API响应被截断的内容:\n{direct_response[server_len:]}\n")
            else:
                f.write(f"服务器API响应的额外内容:\n{server_response[direct_len:]}\n")

async def main():
    """主函数"""
    logger.info("开始API响应完整性测试")
    
    # 测试服务器API可用性
    server_available = await test_server_availability()
    if not server_available:
        logger.warning("服务器API不可用，跳过服务器API测试")
    
    # 测试大模型API可用性
    model_available = await test_model_availability()
    if not model_available:
        logger.warning("大模型API不可用，跳过直接API测试")
    
    # 直接调用大模型API
    direct_response = None
    if model_available:
        direct_response = await test_direct_api()
    
    # 通过服务器API调用
    server_response = None
    if server_available:
        server_response = await test_server_api()
    
    # 比较响应
    if direct_response and server_response:
        await compare_responses(direct_response, server_response)
    else:
        logger.warning("无法比较响应，至少有一个响应为空")
    
    logger.info("API响应完整性测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 