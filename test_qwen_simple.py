#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试千问API
"""

import requests
import json

def test_qwen_api():
    # 测试调用千问API
    request_data = {
        'prompt': '请生成一个简单的资产负债表示例，包含主要科目。',
        'template_name': '一般企业 (2).xls',
        'selected_files': ['测试.pdf']
    }

    print('测试调用千问API...')
    try:
        response = requests.post('http://localhost:8000/api/v1/document/call-qwen-api', json=request_data, timeout=90)
        print(f'状态码: {response.status_code}')

        if response.status_code == 200:
            data = response.json()
            print('✅ 调用成功')
            ai_response = data.get('ai_response', '')
            print(f'AI回答长度: {len(ai_response)} 字符')
            print(f'AI回答预览: {ai_response[:300]}...')
            return True
        else:
            print(f'❌ 调用失败: {response.text}')
            return False
    except Exception as e:
        print(f'❌ 调用异常: {str(e)}')
        return False

if __name__ == "__main__":
    test_qwen_api()
