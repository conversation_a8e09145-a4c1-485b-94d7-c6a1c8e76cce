#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
JWT身份验证处理
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging
from datetime import datetime, timedelta
from jose import JWTError, jwt
from typing import Optional

# JWT配置
SECRET_KEY = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7"  # 实际应用中应从环境变量获取
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 创建日志
logger = logging.getLogger("jintou_brain.security")

# 创建HTTP bearer认证模式
security = HTTPBearer(auto_error=False)

# 模拟用户数据库
mock_users_db = {
    "admin": {
        "username": "admin",
        "full_name": "Administrator",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "admin123"
        "disabled": False,
    },
    "test": {
        "username": "test",
        "full_name": "Test User",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$ib8N/z3zqQHQYR1YYnjOJewwjKdQPxQyO.vSH0y7ONNhFEYRgNGbC",  # "test123"
        "disabled": False,
    }
}

# 模拟用户数据库
USER_DB = {
    "admin": {
        "password": "admin123",
        "display_name": "Administrator",
        "role": "admin",
        "permissions": ["read", "write"]
    },
    "test": {
        "password": "test123",
        "display_name": "Test User",
        "role": "user",
        "permissions": ["read"]
    }
}

# 简单的token存储
TOKENS = {}

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    创建JWT访问令牌
    
    Args:
        data: 要编码到令牌中的数据
        expires_delta: 令牌过期时间增量
        
    Returns:
        编码后的JWT令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    return encoded_jwt

# 简化函数，实际应用中应使用JWT验证
async def get_current_user(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    获取当前用户信息
    
    支持两种方式获取token:
    1. 通过Authorization header (Bearer token)
    2. 通过URL查询参数 (token=xxx)
    
    为了简化测试，任何token都被认为有效，并返回admin用户
    """
    token = None
    
    # 尝试从Authorization header获取token
    if credentials:
        token = credentials.credentials
        logger.info(f"从Authorization header获取Token: {token}")
    
    # 如果header中没有token，尝试从URL参数获取
    if not token:
        token = request.query_params.get("token")
        logger.info(f"从URL参数获取Token: {token}")
    
    # 如果没有提供token，返回匿名用户
    if not token:
        logger.warning("未提供认证凭据，返回匿名用户")
        return {"sub": "anonymous", "role": "guest"}
    
    # 简化验证逻辑 - 实际环境中应该验证token的有效性
    # 这里我们假设所有token都有效，直接返回admin用户
    return {
        "sub": "admin",
        "role": "admin",
        "permissions": ["read", "write"]
    } 