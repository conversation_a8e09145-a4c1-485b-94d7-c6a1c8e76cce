<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尽调助手 - 金投大脑</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/due-diligence.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Marked.js 用于Markdown渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="../../assets/logo.png" alt="金投大脑">
                <h1>尽调助手</h1>
            </div>
            <div class="user-info">
                <span id="username">用户</span>
                <button id="logoutBtn" class="btn btn-outline">退出登录</button>
            </div>
        </div>

        <div class="due-diligence-container">
            <div class="steps-container">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-text">上传财务报表</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-text">数据确认</div>
                </div>
            </div>

            <!-- 企业名称选择区域 -->
            <div class="company-selection-container">
                <h3>企业信息</h3>
                <div class="company-input-group">
                    <div class="company-name-input">
                        <label for="companyNameInput">企业名称：</label>
                        <input type="text" id="companyNameInput" placeholder="请输入企业名称">
                    </div>
                    <div class="company-actions">
                        <button id="saveCompanyBtn" class="btn btn-outline">保存企业</button>
                        <select id="savedCompaniesSelect">
                            <option value="">选择已保存的企业</option>
                        </select>
                    </div>
                </div>
                
                <!-- 企业财务数据表格 -->
                <div class="company-financial-data" id="companyFinancialData">
                    <h4>
                        企业财务数据
                        <div class="table-actions-right">
                            <button id="addColumnBtn" class="btn btn-outline">
                                <i class="fas fa-plus"></i> 添加列
                            </button>
                            <button id="exportDataBtn" class="btn btn-outline">
                                <i class="fas fa-file-export"></i> 导出数据
                            </button>
                        </div>
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-bordered company-table" id="companyDataTable">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>科目名称</th>
                                    <th>金额</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 表格内容将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="step-content active" id="step-1">
                <h2>上传财务报表</h2>
                <p class="step-description">请选择财务报表对应的年月，并上传相关文件（支持Excel、PDF、图片格式）</p>
                
                <!-- 隐私保护设置 -->
                <div class="privacy-protection-settings">
                    <h3>隐私保护设置</h3>
                    <div class="privacy-option">
                        <label class="switch-label">
                            <input type="checkbox" id="enablePrivacyProtection" checked>
                            <span class="switch-text">启用隐私保护（自动遮盖企业名称）</span>
                        </label>
                        <div class="mask-color-selector">
                            <span>遮盖颜色：</span>
                            <select id="maskColor">
                                <option value="red">红色</option>
                                <option value="black">黑色</option>
                                <option value="blue">蓝色</option>
                                <option value="green">绿色</option>
                            </select>
                        </div>
                        <div class="privacy-description">
                            <i class="fas fa-info-circle"></i>
                            <span>启用后，系统将自动识别并遮盖文件中的企业名称，保护隐私信息</span>
                        </div>
                    </div>
                </div>
                
                <div class="period-selection">
                    <div class="period-header">
                        <h3>报表期间</h3>
                        <button id="addPeriodBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增期间
                        </button>
                    </div>
                    
                    <div class="period-list" id="periodList">
                        <!-- 期间列表将在这里动态生成 -->
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="recognizeBtn" class="btn btn-primary" disabled>开始识别</button>
                </div>
            </div>

            <div class="step-content" id="step-2">
                <h2>数据确认</h2>
                
                <!-- 识别后的数据将在这里显示 -->
                <div class="recognized-data" id="recognizedData">
                    <!-- 识别结果将在这里显示 -->
                </div>
                
                <!-- 提示词显示区域 -->
                <div class="card mt-4 mb-4">
                    <div class="card-header bg-info text-white" id="promptsHeader">
                        <button class="btn btn-link text-white" type="button" data-bs-toggle="collapse" data-bs-target="#promptsCollapse" aria-expanded="false" aria-controls="promptsCollapse">
                            <i class="fas fa-code"></i> 显示/隐藏大模型提示词
                        </button>
                    </div>
                    <div id="promptsCollapse" class="collapse" aria-labelledby="promptsHeader">
                        <div class="card-body">
                            <div id="promptsContainer" class="bg-light p-3" style="max-height: 500px; overflow-y: auto;">
                                <p class="text-muted">暂无提示词数据</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 隐私保护确认区域 -->
                <div class="privacy-confirmation" id="privacyConfirmation">
                    <h3>隐私保护确认</h3>
                    <p class="step-description">以下是经过隐私保护处理的图片，请确认企业名称是否已被正确遮盖</p>
                    
                    <div class="privacy-images-container" id="privacyImagesContainer">
                        <!-- 处理后的图片将在这里显示 -->
                        <div class="no-images-message">处理中，请稍候...</div>
                    </div>
                    
                    <div class="privacy-editor-container" id="privacyEditorContainer" style="display: none;">
                        <h4>图片编辑</h4>
                        <div class="editor-tools">
                            <button class="btn btn-tool" id="rectTool" title="矩形遮盖工具">
                                <i class="fas fa-square"></i>
                            </button>
                            <button class="btn btn-tool" id="brushTool" title="涂抹工具">
                                <i class="fas fa-paint-brush"></i>
                            </button>
                            <div class="color-picker">
                                <span>颜色：</span>
                                <select id="editorColor">
                                    <option value="red">红色</option>
                                    <option value="black">黑色</option>
                                    <option value="blue">蓝色</option>
                                    <option value="green">绿色</option>
                                </select>
                            </div>
                            <button class="btn btn-tool" id="undoTool" title="撤销">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-tool" id="redoTool" title="重做">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                        <div class="canvas-container">
                            <canvas id="imageEditor"></canvas>
                        </div>
                        <div class="editor-actions">
                            <button class="btn btn-outline" id="cancelEdit">取消</button>
                            <button class="btn btn-primary" id="saveEdit">保存修改</button>
                        </div>
                    </div>
                    
                    <div class="privacy-confirmation-actions">
                        <button class="btn btn-primary" id="confirmPrivacyBtn">确认并继续</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 期间模板 -->
    <template id="period-template">
        <div class="period-item" data-id="{id}">
            <div class="period-info">
                <div class="period-year-month">
                    <select class="period-year">
                        <option value="">选择年份</option>
                        <!-- 年份选项将动态生成 -->
                    </select>
                    <select class="period-month">
                        <option value="">选择月份</option>
                        <!-- 月份选项将动态生成 -->
                    </select>
                </div>
                <button class="btn btn-outline btn-delete-period">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="file-upload-container">
                <div class="file-upload">
                    <input type="file" class="file-input" multiple accept=".xlsx,.xls,.pdf,.jpg,.jpeg,.png">
                    <div class="upload-placeholder">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>点击或拖拽文件到此处</p>
                        <p class="file-types">支持Excel、PDF和图片格式</p>
                    </div>
                </div>
                <div class="file-list">
                    <!-- 上传的文件列表将在这里显示 -->
                </div>
            </div>
        </div>
    </template>

    <!-- 文件项模板 -->
    <template id="file-item-template">
        <div class="file-item" data-name="{name}">
            <div class="file-icon">
                <i class="{icon-class}"></i>
            </div>
            <div class="file-details">
                <div class="file-name">{name}</div>
                <div class="file-size">{size}</div>
            </div>
            <button class="btn-remove-file">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </template>

    <!-- 表格显示模板 -->
    <template id="table-template">
        <div class="recognized-table">
            <h3>{period} 财务数据</h3>
            <div class="table-container">
                <table>
                    <!-- 表格内容将动态生成 -->
                </table>
            </div>
        </div>
    </template>
    
    <!-- 隐私图片项模板 -->
    <template id="privacy-image-template">
        <div class="privacy-image-item" data-file-id="{fileId}">
            <div class="privacy-image-header">
                <span class="privacy-image-title">{title}</span>
                <button class="btn-edit-image">
                    <i class="fas fa-edit"></i> 编辑
                </button>
            </div>
            <div class="privacy-image-container">
                <img src="" alt="{title}" class="privacy-image">
            </div>
        </div>
    </template>

    <script src="../js/due-diligence.js"></script>
    <!-- 添加XLSX.js用于Excel导出 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
</body>
</html> 