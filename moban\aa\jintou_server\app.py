from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# 导入我们的路由模块
from jintou_server.api import ocr_chat_api, ocr_vision_api
# 导入直接转发API路由
from jintou_server.api import ocr_vision_direct_api
# 导入新的外部大模型API路由
from jintou_server.api import ocr_external_api

# 创建FastAPI应用
app = FastAPI(
    title="金投大脑API服务",
    description="为金投大脑前端应用提供后端API服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(ocr_chat_api.router)
app.include_router(ocr_vision_api.router)
# 添加OCR视觉直接转发API路由
app.include_router(ocr_vision_direct_api.router)
# 添加新的外部大模型API路由
app.include_router(ocr_external_api.router)

# 提供静态文件（如果有）
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    print("警告: 无法挂载静态文件目录，可能不存在")

# 根路由
@app.get("/")
async def root():
    return {"message": "欢迎使用金投大脑API服务"}

# 健康检查
@app.get("/health")
async def health():
    return {"status": "healthy"} 