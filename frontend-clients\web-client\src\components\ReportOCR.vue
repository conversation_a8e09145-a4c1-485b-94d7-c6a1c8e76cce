<template>
  <div class="report-ocr">
    <div class="ocr-header">
      <h2>📊 报表OCR</h2>
      <p>支持批量上传Excel、Word、PDF、PPT、图片等文件，自动解析文档内容</p>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
      <div 
        class="file-drop-zone"
        :class="{ 'drag-over': isDragOver }"
        @click="triggerFileInput"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
      >
        <div class="drop-zone-content">
          <div class="upload-icon">📁</div>
          <div class="upload-text">
            <p><strong>点击选择文件</strong> 或 <strong>拖拽文件到此处</strong></p>
            <p class="file-hint">支持: Excel (.xlsx, .xls), Word (.docx, .doc), PDF, PPT (.pptx, .ppt), 图片 (.jpg, .png, .gif, .bmp)</p>
            <p class="file-hint">单个文件最大10MB，支持批量上传</p>
          </div>
        </div>
      </div>

      <!-- 隐藏的文件输入 -->
      <input
        ref="fileInput"
        type="file"
        multiple
        accept=".xlsx,.xls,.docx,.doc,.pdf,.pptx,.ppt,.txt,.csv,.jpg,.jpeg,.png,.gif,.bmp"
        @change="handleFileSelect"
        style="display: none"
      />
    </div>

    <!-- 文件列表 -->
    <div v-if="uploadedFiles.length > 0" class="file-list-section">
      <div class="section-header">
        <h3>📋 已选择文件 ({{ uploadedFiles.length }})</h3>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :loading="parsing" 
            @click="parseFiles"
            :disabled="uploadedFiles.length === 0"
          >
            <i class="el-icon-document"></i>
            {{ parsing ? '解析中...' : '开始解析' }}
          </el-button>
          <el-button type="danger" @click="clearFiles">
            <i class="el-icon-delete"></i>
            清空文件
          </el-button>
        </div>
      </div>

      <div class="file-list">
        <div 
          v-for="(file, index) in uploadedFiles" 
          :key="index" 
          class="file-item"
        >
          <div class="file-info">
            <span class="file-icon">{{ getFileIcon(file.name) }}</span>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
          </div>
          <div class="file-status">
            <span v-if="getFileStatus(file.name) === 'parsing'" class="status parsing">
              <i class="el-icon-loading"></i> 解析中
            </span>
            <span v-else-if="getFileStatus(file.name) === 'success'" class="status success">
              <i class="el-icon-check"></i> 成功
            </span>
            <span v-else-if="getFileStatus(file.name) === 'error'" class="status error">
              <i class="el-icon-close"></i> 失败
            </span>
            <span v-else class="status pending">
              <i class="el-icon-time"></i> 待解析
            </span>
          </div>
          <el-button 
            type="text" 
            size="small" 
            @click="removeFile(index)"
            class="remove-btn"
          >
            <i class="el-icon-delete"></i>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 解析结果 -->
    <div v-if="parsedResults.length > 0" class="results-section">
      <div class="section-header">
        <h3>📄 解析结果</h3>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="analyzeReports"
            :loading="analyzing"
            :disabled="!hasSuccessfulResults"
          >
            <i class="el-icon-chat-dot-round"></i>
            {{ analyzing ? '分析中...' : '智能分析报表' }}
          </el-button>
          <el-button type="success" @click="exportResults">
            <i class="el-icon-download"></i>
            导出结果
          </el-button>
        </div>
      </div>

      <div class="results-list">
        <div 
          v-for="(result, index) in parsedResults" 
          :key="index" 
          class="result-item"
        >
          <div class="result-header">
            <div class="result-title">
              <span class="file-icon">{{ getFileIcon(result.filename) }}</span>
              <span class="filename">{{ result.filename }}</span>
              <span v-if="result.success" class="success-badge">✅ 解析成功</span>
              <span v-else class="error-badge">❌ 解析失败</span>
            </div>
            <el-button 
              type="text" 
              size="small" 
              @click="toggleResultContent(index)"
            >
              {{ result.expanded ? '收起' : '展开' }}
              <i :class="result.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </el-button>
          </div>
          
          <div v-if="result.expanded" class="result-content">
            <div v-if="result.success" class="content-text">
              <div v-html="formatMessage ? formatMessage(result.content) : result.content"></div>
            </div>
            <div v-else class="error-text">
              {{ result.error }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能分析结果 -->
    <div v-if="analysisResults.length > 0" class="analysis-section">
      <div class="section-header">
        <h3>🤖 智能分析结果</h3>
        <div class="header-actions">
          <el-button
            type="warning"
            @click="selectAllReports"
            :disabled="!hasAnalysisResults"
          >
            <i class="el-icon-check"></i>
            全选报表文件
          </el-button>
          <div class="template-selection">
            <el-select
              v-model="selectedTemplate"
              placeholder="选择报表模板"
              style="width: 200px; margin-right: 10px;"
              @focus="loadReportTemplates"
            >
              <el-option
                v-for="template in reportTemplates"
                :key="template.name"
                :label="template.display_name"
                :value="template.name"
              />
            </el-select>
          </div>
          <el-button
            type="success"
            @click="generateReportWithTemplate"
            :loading="generatingReport"
            :disabled="selectedFilesCount === 0 || !selectedTemplate"
          >
            <i class="el-icon-document-add"></i>
            {{ generatingReport ? '生成中...' : `生成最终报表 (${selectedFilesCount})` }}
          </el-button>
          <el-button type="info" @click="exportAnalysisResults">
            <i class="el-icon-download"></i>
            导出分析结果
          </el-button>
        </div>
      </div>

      <!-- 汇总分析结果表格 -->
      <div class="summary-analysis-table">
        <h4>📊 分析结果汇总</h4>
        <el-table :data="analysisResults" border style="width: 100%" max-height="600">
          <el-table-column prop="filename" label="文件名" width="200">
            <template #default="scope">
              <div class="file-info">
                <span class="file-icon">{{ getFileIcon(scope.row.filename) }}</span>
                <span class="filename">{{ scope.row.filename }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分析状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="是否为报表" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.parsed_result" :type="scope.row.parsed_result.是否为报表 === '是' ? 'success' : 'info'">
                {{ scope.row.parsed_result.是否为报表 }}
              </el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="parsed_result.报表类型" label="报表类型" width="150">
            <template #default="scope">
              {{ scope.row.parsed_result?.报表类型 || '-' }}
            </template>
          </el-table-column>

          <el-table-column prop="parsed_result.报表主体" label="报表主体" width="200">
            <template #default="scope">
              {{ scope.row.parsed_result?.报表主体 || '-' }}
            </template>
          </el-table-column>

          <el-table-column prop="parsed_result.报表时期" label="报表时期" width="150">
            <template #default="scope">
              {{ scope.row.parsed_result?.报表时期 || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="置信度" width="80" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.parsed_result" :type="getConfidenceType(scope.row.parsed_result.置信度)">
                {{ scope.row.parsed_result.置信度 }}分
              </el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <div class="action-buttons">
                <el-button
                  type="text"
                  size="small"
                  @click="showAnalysisDetail(scope.row)"
                  :disabled="!scope.row.success"
                >
                  查看详情
                </el-button>
                <el-checkbox
                  v-model="scope.row.selected"
                  @change="updateSelectedFiles"
                  :disabled="!scope.row.success || scope.row.parsed_result?.是否为报表 !== '是'"
                >
                  选择
                </el-checkbox>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 最终报表生成结果 -->
    <div v-if="finalReport" class="final-report-section">
      <div class="section-header">
        <h3>📊 最终报表</h3>
        <div class="header-actions">
          <el-button type="success" @click="exportFinalReport">
            <i class="el-icon-download"></i>
            导出最终报表
          </el-button>
        </div>
      </div>

      <div class="final-report-content">
        <div class="report-summary">
          <h4>📋 报表汇总信息</h4>
          <div class="summary-info">
            <p><strong>生成时间：</strong>{{ finalReport.generated_at || finalReport.generated_time }}</p>
            <p><strong>包含文件：</strong>{{ finalReport.selected_files?.length || 0 }} 个</p>
            <p><strong>使用模板：</strong>{{ finalReport.template_name || '无' }}</p>
            <p><strong>报表类型：</strong>{{ finalReport.report_types?.join(', ') || '混合类型' }}</p>
          </div>
        </div>

        <!-- 显示完整提示词 -->
        <div v-if="templatePrompt" class="prompt-section">
          <h4>📝 生成提示词</h4>
          <div class="prompt-stats">
            <el-tag type="info">长度: {{ templatePrompt.length }} 字符</el-tag>
            <el-tag type="success">状态: 已生成</el-tag>
            <el-tag v-if="templatePrompt.length > 15000" type="warning">长文本将分割处理</el-tag>
            <el-tag v-else type="primary">单次处理</el-tag>
            <el-button
              v-if="!templateAIResponse"
              type="primary"
              size="small"
              @click="continueWithQwenAPI"
              :loading="generatingReport"
              style="margin-left: 10px;"
            >
              {{ generatingReport ? '生成中...' : '继续调用千问大模型' }}
            </el-button>
          </div>
          <div class="prompt-content">
            <pre>{{ templatePrompt }}</pre>
          </div>
        </div>

        <!-- 显示AI回答 -->
        <div v-if="templateAIResponse" class="ai-response-section">
          <h4>🤖 千问AI生成结果</h4>
          <div class="ai-response-stats">
            <el-tag type="info">长度: {{ templateAIResponse.length }} 字符</el-tag>
            <el-tag type="success">状态: 已完成</el-tag>
            <el-tag v-if="templateAIResponse.includes('分割处理汇总')" type="warning">分割处理</el-tag>
            <el-tag v-else type="primary">单次处理</el-tag>
          </div>
          <div class="ai-response-content">
            <div v-html="formatMessage ? formatMessage(templateAIResponse) : templateAIResponse"></div>
          </div>
        </div>

        <!-- 原有的详细内容显示 -->
        <div v-if="finalReport.content" class="report-details">
          <h4>📄 详细内容</h4>
          <div class="report-content">
            <div v-html="formatMessage ? formatMessage(finalReport.content) : finalReport.content"></div>
          </div>
        </div>

        <div class="selected-files-list">
          <h4>📁 包含的文件</h4>
          <div class="files-grid">
            <div
              v-for="file in finalReport.selected_files"
              :key="file.filename"
              class="file-card"
            >
              <div class="file-info">
                <span class="file-icon">{{ getFileIcon(file.filename) }}</span>
                <span class="filename">{{ file.filename }}</span>
              </div>
              <div class="file-analysis">
                <span class="report-type">{{ file.report_type || '未知类型' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="uploadedFiles.length === 0 && parsedResults.length === 0" class="empty-state">
      <div class="empty-icon">📄</div>
      <h3>开始上传文档</h3>
      <p>选择或拖拽文件到上方区域，开始批量文档解析</p>
    </div>

    <!-- 分析详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="分析详情"
      width="80%"
      :before-close="closeDetailDialog"
    >
      <div v-if="selectedAnalysis" class="detail-content">
        <!-- 文件信息 -->
        <div class="file-detail-info">
          <h4>📁 文件信息</h4>
          <p><strong>文件名：</strong>{{ selectedAnalysis.filename }}</p>
          <p><strong>分析状态：</strong>
            <el-tag :type="selectedAnalysis.success ? 'success' : 'danger'">
              {{ selectedAnalysis.success ? '分析成功' : '分析失败' }}
            </el-tag>
          </p>
        </div>

        <div v-if="selectedAnalysis.success">
          <!-- 显示完整提示词 -->
          <div class="prompt-section">
            <h4>📝 分析提示词</h4>
            <div class="prompt-content">
              <pre>{{ selectedAnalysis.prompt }}</pre>
            </div>
          </div>

          <!-- 显示AI回答 -->
          <div class="ai-response-section">
            <h4>🤖 AI分析结果</h4>

            <!-- 结构化显示 -->
            <div v-if="selectedAnalysis.parsed_result" class="structured-result">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="是否为报表">
                  <el-tag :type="selectedAnalysis.parsed_result.是否为报表 === '是' ? 'success' : 'info'">
                    {{ selectedAnalysis.parsed_result.是否为报表 }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="报表类型">{{ selectedAnalysis.parsed_result.报表类型 }}</el-descriptions-item>
                <el-descriptions-item label="报表主体">{{ selectedAnalysis.parsed_result.报表主体 }}</el-descriptions-item>
                <el-descriptions-item label="报表时期">{{ selectedAnalysis.parsed_result.报表时期 }}</el-descriptions-item>
                <el-descriptions-item label="置信度">
                  <el-tag :type="getConfidenceType(selectedAnalysis.parsed_result.置信度)">
                    {{ selectedAnalysis.parsed_result.置信度 }}分
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="备注">{{ selectedAnalysis.parsed_result.备注 || '无' }}</el-descriptions-item>
              </el-descriptions>

              <!-- 关键财务数据 -->
              <div v-if="selectedAnalysis.parsed_result.关键财务数据 && selectedAnalysis.parsed_result.关键财务数据.length > 0" class="financial-data">
                <h5>💰 关键财务数据</h5>
                <el-table :data="selectedAnalysis.parsed_result.关键财务数据.map((item, index) => ({ index: index + 1, data: item }))" border size="small">
                  <el-table-column prop="index" label="序号" width="60" align="center" />
                  <el-table-column prop="data" label="财务数据" />
                </el-table>
              </div>
            </div>

            <!-- 原始回答 -->
            <div class="raw-response-section">
              <h5>📄 原始AI回答</h5>
              <div class="raw-response-content">
                <div v-html="formatMessage ? formatMessage(selectedAnalysis.ai_response) : selectedAnalysis.ai_response"></div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="error-detail">
          <h4>❌ 错误信息</h4>
          <p>{{ selectedAnalysis.error || '分析失败，请重试' }}</p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDetailDialog">关闭</el-button>
          <el-button
            v-if="selectedAnalysis?.success && selectedAnalysis?.parsed_result?.是否为报表 === '是'"
            type="primary"
            @click="toggleSelection"
          >
            {{ selectedAnalysis.selected ? '取消选择' : '选择此文件' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from '@/utils/request'

// 响应式数据
const uploadedFiles = ref([])
const parsedResults = ref([])
const analysisResults = ref([])
const finalReport = ref(null)
const parsing = ref(false)
const analyzing = ref(false)
const generatingReport = ref(false)
const isDragOver = ref(false)
const fileInput = ref(null)
const fileStatusMap = reactive({})

// 详情对话框相关
const detailDialogVisible = ref(false)
const selectedAnalysis = ref(null)

// 报表模板相关
const reportTemplates = ref([])
const selectedTemplate = ref('')
const templatePrompt = ref('')
const templateAIResponse = ref('')

// 计算属性
const hasSuccessfulResults = computed(() => {
  return parsedResults.value.some(result => result.success && result.content)
})

const hasAnalysisResults = computed(() => {
  return analysisResults.value.some(result => result.success)
})

const selectedFilesCount = computed(() => {
  return analysisResults.value.filter(result => result.selected && result.success).length
})

// 文件处理方法
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  addFiles(files)
  // 清空input，允许重复选择同一文件
  event.target.value = ''
}

const handleDragOver = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  const files = Array.from(event.dataTransfer.files)
  addFiles(files)
}

const addFiles = (files) => {
  const validFiles = files.filter(file => {
    // 检查文件大小（10MB限制）
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.warning(`文件 ${file.name} 超过10MB限制`)
      return false
    }

    // 检查文件类型
    const allowedExtensions = ['xlsx', 'xls', 'docx', 'doc', 'pdf', 'pptx', 'ppt', 'txt', 'csv', 'jpg', 'jpeg', 'png', 'gif', 'bmp']
    const fileExtension = file.name.split('.').pop().toLowerCase()
    
    if (!allowedExtensions.includes(fileExtension)) {
      ElMessage.warning(`不支持的文件格式: ${file.name}`)
      return false
    }

    return true
  })

  if (validFiles.length > 0) {
    uploadedFiles.value.push(...validFiles)
    ElMessage.success(`已添加 ${validFiles.length} 个文件`)
  }
}

const removeFile = (index) => {
  const removedFile = uploadedFiles.value[index]
  uploadedFiles.value.splice(index, 1)
  
  // 从状态映射中移除
  delete fileStatusMap[removedFile.name]
  
  // 从解析结果中移除
  const resultIndex = parsedResults.value.findIndex(r => r.filename === removedFile.name)
  if (resultIndex !== -1) {
    parsedResults.value.splice(resultIndex, 1)
  }
}

const clearFiles = () => {
  uploadedFiles.value = []
  parsedResults.value = []
  Object.keys(fileStatusMap).forEach(key => delete fileStatusMap[key])
}

const parseFiles = async () => {
  if (uploadedFiles.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }

  parsing.value = true

  // 设置所有文件状态为解析中
  uploadedFiles.value.forEach(file => {
    fileStatusMap[file.name] = 'parsing'
  })

  try {
    // 创建FormData
    const formData = new FormData()
    uploadedFiles.value.forEach(file => {
      formData.append('files', file)
    })

    // 获取认证token
    const token = localStorage.getItem('token')

    // 发送文件解析请求
    const response = await axios.post('/document/upload-files', formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000 // 2分钟超时
    })

    // 检查响应数据结构
    if (response.data && response.data.code === 200 && response.data.data) {
      const results = response.data.data.files || []

      // 更新解析结果
      parsedResults.value = results.map(result => ({
        ...result,
        expanded: false
      }))

      // 更新文件状态
      results.forEach(result => {
        fileStatusMap[result.filename] = result.success ? 'success' : 'error'
      })

      const successCount = response.data.data.success_count || 0
      const totalCount = response.data.data.files_count || results.length

      if (successCount === totalCount) {
        ElMessage.success(`成功解析了 ${successCount} 个文件`)
      } else {
        ElMessage.warning(`解析了 ${successCount}/${totalCount} 个文件，部分文件解析失败`)
      }

    } else {
      throw new Error('文件解析失败')
    }

  } catch (error) {
    console.error('文件解析错误:', error)
    ElMessage.error(`文件解析失败: ${error.message}`)

    // 设置所有文件状态为错误
    uploadedFiles.value.forEach(file => {
      fileStatusMap[file.name] = 'error'
    })
  } finally {
    parsing.value = false
  }
}

// 工具方法
const getFileIcon = (fileName) => {
  if (!fileName || typeof fileName !== 'string') {
    return '📄'
  }
  const ext = fileName.split('.').pop()?.toLowerCase()
  if (!ext) {
    return '📄'
  }
  const iconMap = {
    'xlsx': '📊', 'xls': '📊',
    'docx': '📄', 'doc': '📄',
    'pdf': '📕',
    'pptx': '📊', 'ppt': '📊',
    'txt': '📝', 'csv': '📋',
    'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️'
  }
  return iconMap[ext] || '📄'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileStatus = (filename) => {
  return fileStatusMap[filename] || 'pending'
}

const toggleResultContent = (index) => {
  parsedResults.value[index].expanded = !parsedResults.value[index].expanded
}

// 格式化消息内容（处理代码和表格）- 完全按照PMO实现
function formatMessage(content) {
  if (!content) return '';

  // 处理代码块
  let formatted = content.replace(/```sql([\s\S]*?)```/g, '<div class="code-block"><pre>$1</pre></div>');
  formatted = formatted.replace(/```([\s\S]*?)```/g, '<div class="code-block"><pre>$1</pre></div>');

  // 处理表格 (简单的Markdown表格处理) - 完全按照PMO实现
  const tableRegex = /\|(.+)\|[\r\n]\|[-:| ]+\|[\r\n]((?:\|.+\|[\r\n])+)/g;
  formatted = formatted.replace(tableRegex, (match, headerRow, bodyRows) => {
    const headers = headerRow.split('|').map(h => h.trim()).filter(h => h);
    const rows = bodyRows.trim().split('\n').map(row => {
      return row.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
    });

    let tableHtml = '<table class="message-table"><thead><tr>';
    headers.forEach(header => {
      tableHtml += `<th>${header}</th>`;
    });
    tableHtml += '</tr></thead><tbody>';

    rows.forEach(row => {
      tableHtml += '<tr>';
      row.forEach(cell => {
        tableHtml += `<td>${cell}</td>`;
      });
      tableHtml += '</tr>';
    });

    tableHtml += '</tbody></table>';
    return tableHtml;
  });

  // 处理换行
  formatted = formatted.replace(/\n/g, '<br>');

  return formatted;
}

// 分析报表
const analyzeReports = async () => {
  if (!hasSuccessfulResults.value) {
    ElMessage.warning('没有成功解析的文件可供分析')
    return
  }

  analyzing.value = true

  try {
    // 准备OCR结果数据
    const ocrResults = parsedResults.value
      .filter(result => result.success && result.content)
      .map(result => ({
        filename: result.filename,
        content: result.content
      }))

    if (ocrResults.length === 0) {
      ElMessage.warning('没有有效的OCR识别内容')
      return
    }

    // 调用后端分析API
    const response = await axios.post('/document/analyze-reports', {
      ocr_results: ocrResults
    })

    if (response.data && response.data.success) {
      // 为每个分析结果添加展开状态和选择状态，并尝试解析AI回答
      const results = response.data.results.map(result => ({
        ...result,
        expanded: false,
        selected: false,
        parsed_result: parseAIResponse(result.ai_response)
      }))

      analysisResults.value = results

      ElMessage.success(`成功分析了 ${response.data.analyzed_files}/${response.data.total_files} 个文件`)
    } else {
      throw new Error(response.data?.error || '分析失败')
    }

  } catch (error) {
    console.error('报表分析失败:', error)
    ElMessage.error(`报表分析失败: ${error.message}`)
  } finally {
    analyzing.value = false
  }
}

// 切换分析内容显示
const toggleAnalysisContent = (index) => {
  analysisResults.value[index].expanded = !analysisResults.value[index].expanded
}

// 导出分析结果
const exportAnalysisResults = () => {
  const content = analysisResults.value
    .filter(result => result.success)
    .map(result => {
      return `文件名: ${result.filename}\n\n提示词:\n${result.prompt}\n\nAI分析结果:\n${result.ai_response}\n\n${'='.repeat(50)}\n`
    })
    .join('\n')

  if (!content) {
    ElMessage.warning('没有可导出的分析结果')
    return
  }

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `报表分析结果_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('分析结果已导出')
}

// 更新选中文件
const updateSelectedFiles = () => {
  // 这个方法会在checkbox变化时自动触发，用于响应式更新
}

// 全选报表文件
const selectAllReports = () => {
  analysisResults.value.forEach(result => {
    if (result.success) {
      result.selected = true
    }
  })
  ElMessage.success('已选择所有成功分析的报表文件')
}

// 生成最终报表
const generateFinalReport = async () => {
  const selectedFiles = analysisResults.value.filter(result => result.selected && result.success)

  if (selectedFiles.length === 0) {
    ElMessage.warning('请先选择要生成报表的文件')
    return
  }

  generatingReport.value = true

  try {
    // 调用后端API生成最终报表
    const response = await axios.post('/document/generate-final-report', {
      selected_files: selectedFiles.map(file => ({
        filename: file.filename,
        ai_response: file.ai_response,
        original_content: file.original_content
      }))
    })

    if (response.data && response.data.success) {
      finalReport.value = {
        ...response.data.report,
        generated_time: new Date().toLocaleString('zh-CN'),
        selected_files: selectedFiles.map(file => ({
          filename: file.filename,
          report_type: extractReportType(file.ai_response)
        }))
      }

      ElMessage.success(`成功生成包含 ${selectedFiles.length} 个文件的最终报表`)

      // 滚动到最终报表区域
      setTimeout(() => {
        const element = document.querySelector('.final-report-section')
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)

    } else {
      throw new Error(response.data?.error || '生成最终报表失败')
    }

  } catch (error) {
    console.error('生成最终报表失败:', error)
    ElMessage.error(`生成最终报表失败: ${error.message}`)
  } finally {
    generatingReport.value = false
  }
}

// 解析AI回答为结构化数据
const parseAIResponse = (aiResponse) => {
  if (!aiResponse) return null

  try {
    // 尝试解析JSON格式的回答
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      const jsonStr = jsonMatch[0]
      const parsed = JSON.parse(jsonStr)
      return parsed
    }
  } catch (error) {
    console.warn('无法解析AI回答为JSON格式:', error)
  }

  // 如果JSON解析失败，尝试从文本中提取信息
  try {
    const result = {}

    // 提取是否为报表
    const isReportMatch = aiResponse.match(/是否为报表[：:]\s*([^\n]+)/i)
    result.是否为报表 = isReportMatch ? isReportMatch[1].trim() : '未知'

    // 提取报表类型
    const typeMatch = aiResponse.match(/报表类型[：:]\s*([^\n]+)/i)
    result.报表类型 = typeMatch ? typeMatch[1].trim() : '未知类型'

    // 提取报表主体
    const subjectMatch = aiResponse.match(/报表主体[：:]\s*([^\n]+)/i)
    result.报表主体 = subjectMatch ? subjectMatch[1].trim() : '未知主体'

    // 提取报表时期
    const periodMatch = aiResponse.match(/报表时期[：:]\s*([^\n]+)/i)
    result.报表时期 = periodMatch ? periodMatch[1].trim() : '未知时期'

    // 提取置信度
    const confidenceMatch = aiResponse.match(/置信度[：:]\s*(\d+)/i)
    result.置信度 = confidenceMatch ? confidenceMatch[1] : '5'

    // 提取关键信息作为财务数据
    const keyInfoMatch = aiResponse.match(/关键信息[：:][\s\S]*?(?=\d+\.|$)/i)
    if (keyInfoMatch) {
      const keyInfo = keyInfoMatch[0]
      const dataItems = keyInfo.split(/[，。；\n]/).filter(item =>
        item.trim() && item.includes('：') || item.includes(':')
      ).slice(0, 5)
      result.关键财务数据 = dataItems.length > 0 ? dataItems : ['暂无关键数据']
    } else {
      result.关键财务数据 = ['暂无关键数据']
    }

    result.备注 = '从文本格式解析得出'

    return result
  } catch (error) {
    console.error('解析AI回答失败:', error)
    return null
  }
}

// 从AI回答中提取报表类型
const extractReportType = (aiResponse) => {
  if (!aiResponse) return '未知类型'

  const parsed = parseAIResponse(aiResponse)
  return parsed ? parsed.报表类型 : '未知类型'
}

// 获取置信度标签类型
const getConfidenceType = (confidence) => {
  const score = parseInt(confidence)
  if (score >= 8) return 'success'
  if (score >= 6) return 'warning'
  return 'danger'
}

// 显示分析详情
const showAnalysisDetail = (analysis) => {
  selectedAnalysis.value = analysis
  detailDialogVisible.value = true
}

// 关闭详情对话框
const closeDetailDialog = () => {
  detailDialogVisible.value = false
  selectedAnalysis.value = null
}

// 切换选择状态
const toggleSelection = () => {
  if (selectedAnalysis.value) {
    selectedAnalysis.value.selected = !selectedAnalysis.value.selected
    updateSelectedFiles()
  }
}

// 加载报表模板
const loadReportTemplates = async () => {
  if (reportTemplates.value.length > 0) return // 已经加载过了

  try {
    console.log('开始加载报表模板...')
    const response = await axios.get('/document/report-templates')
    console.log('API响应:', response.data)

    // 检查响应数据结构
    if (response.data && response.data.success && response.data.templates) {
      reportTemplates.value = response.data.templates || []
      console.log('加载的模板:', reportTemplates.value)

      // 自动选择第一个模板
      if (reportTemplates.value.length > 0 && !selectedTemplate.value) {
        selectedTemplate.value = reportTemplates.value[0].name
        console.log('自动选择第一个模板:', selectedTemplate.value)
      }
    } else {
      console.log('API响应格式错误:', response.data)
      ElMessage.warning('模板数据格式错误')
    }
  } catch (error) {
    console.error('加载报表模板失败:', error)
    ElMessage.error('加载报表模板失败')
  }
}

// 使用模板生成报表（分步骤）
const generateReportWithTemplate = async () => {
  console.log('🚀 开始生成报表...')

  if (selectedFilesCount.value === 0) {
    ElMessage.warning('请先选择要生成报表的文件')
    return
  }

  if (!selectedTemplate.value) {
    ElMessage.warning('请选择报表模板')
    return
  }

  try {
    generatingReport.value = true
    console.log('设置生成状态为true')

    // 准备请求数据
    const selectedResults = analysisResults.value.filter(result => result.selected)
    const requestData = {
      selected_files: selectedResults.map(result => result.filename),
      template_name: selectedTemplate.value,
      analysis_results: selectedResults
    }

    console.log('📋 请求数据:', requestData)

    // 第一步：生成提示词
    console.log('第一步：生成提示词...')
    ElMessage.info('正在生成提示词...')

    console.log('发送生成提示词请求到:', '/document/generate-prompt')
    const promptResponse = await axios.post('/document/generate-prompt', requestData)

    if (promptResponse.data && promptResponse.data.success) {
      // 显示提示词
      templatePrompt.value = promptResponse.data.prompt

      // 保存基本信息
      finalReport.value = {
        template_name: promptResponse.data.template_name,
        selected_files: promptResponse.data.selected_files,
        template_data: promptResponse.data.template_data,
        generated_at: new Date().toLocaleString()
      }

      ElMessage.success('提示词生成成功！请查看下方显示的提示词内容，然后点击"继续调用千问大模型"按钮')
      console.log('提示词已生成，长度:', templatePrompt.value.length)

      // 滚动到提示词显示区域
      setTimeout(() => {
        const element = document.querySelector('.prompt-section')
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    } else {
      throw new Error(promptResponse.data?.message || '生成提示词失败')
    }
  } catch (error) {
    console.error('生成报表失败:', error)
    if (error !== 'cancel') {
      ElMessage.error(`生成报表失败: ${error.response?.data?.detail || error.message}`)
    }
  } finally {
    generatingReport.value = false
  }
}

// 继续调用千问API
const continueWithQwenAPI = async () => {
  if (!templatePrompt.value) {
    ElMessage.warning('请先生成提示词')
    return
  }

  try {
    generatingReport.value = true

    // 调用千问API
    console.log('调用千问API...')
    ElMessage.info('正在调用千问大模型...')

    const selectedResults = analysisResults.value.filter(result => result.selected)
    const qwenRequest = {
      prompt: templatePrompt.value,
      template_name: selectedTemplate.value,
      selected_files: selectedResults.map(result => result.filename)
    }

    const aiResponse = await axios.post('/document/call-qwen-api', qwenRequest)

    if (aiResponse.data && aiResponse.data.success) {
      // 显示AI回答
      templateAIResponse.value = aiResponse.data.ai_response

      // 更新最终报表信息
      finalReport.value = {
        ...finalReport.value,
        ai_response: aiResponse.data.ai_response
      }

      ElMessage.success('报表生成完成！')
      console.log('AI回答已生成，长度:', templateAIResponse.value.length)

      // 滚动到AI回答区域
      setTimeout(() => {
        const element = document.querySelector('.ai-response-section')
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    } else {
      throw new Error(aiResponse.data?.message || '调用千问API失败')
    }
  } catch (error) {
    console.error('调用千问API失败:', error)
    ElMessage.error(`调用千问API失败: ${error.response?.data?.detail || error.message}`)
  } finally {
    generatingReport.value = false
  }
}

// 导出最终报表
const exportFinalReport = () => {
  if (!finalReport.value) {
    ElMessage.warning('没有可导出的最终报表')
    return
  }

  const content = `最终报表
生成时间: ${finalReport.value.generated_time}
包含文件: ${finalReport.value.selected_files?.length || 0} 个

${finalReport.value.content}

包含的文件列表:
${finalReport.value.selected_files?.map(file => `- ${file.filename} (${file.report_type})`).join('\n') || '无'}
`

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `最终报表_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('最终报表已导出')
}

const exportResults = () => {
  // 导出解析结果
  const content = parsedResults.value
    .filter(result => result.success)
    .map(result => `文件: ${result.filename}\n内容:\n${result.content}\n\n${'='.repeat(50)}\n`)
    .join('\n')

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `解析结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('解析结果已导出')
}

// 组件挂载时自动加载模板
onMounted(() => {
  console.log('ReportOCR组件已挂载，开始加载报表模板...')
  loadReportTemplates()
})
</script>

<style scoped>
.report-ocr {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.ocr-header {
  text-align: center;
  margin-bottom: 30px;
}

.ocr-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.ocr-header p {
  color: #7f8c8d;
  font-size: 14px;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 30px;
}

.file-drop-zone {
  border: 2px dashed #dcdfe6;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.drop-zone-content {
  color: #606266;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 15px;
  color: #409eff;
}

.upload-text p {
  margin: 8px 0;
}

.file-hint {
  font-size: 12px;
  color: #909399;
}

/* 文件列表区域 */
.file-list-section,
.results-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.file-list {
  max-height: 400px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 10px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.file-item:hover {
  background: #f0f9ff;
  border-color: #409eff;
  transform: translateX(5px);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  margin-right: 15px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-status {
  margin-right: 15px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status.pending {
  background: #f4f4f5;
  color: #909399;
}

.status.parsing {
  background: #e1f3ff;
  color: #409eff;
}

.status.success {
  background: #f0f9ff;
  color: #67c23a;
}

.status.error {
  background: #fef0f0;
  color: #f56c6c;
}

.remove-btn {
  color: #f56c6c;
}

/* 解析结果区域 */
.results-list {
  max-height: 600px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.result-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filename {
  font-weight: 500;
  color: #2c3e50;
}

.success-badge {
  background: #f0f9ff;
  color: #67c23a;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.error-badge {
  background: #fef0f0;
  color: #f56c6c;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.result-content {
  padding: 20px;
  background: white;
}

.content-text pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  max-height: 400px;
  overflow-y: auto;
}

.error-text {
  color: #f56c6c;
  background: #fef0f0;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #f56c6c;
}

/* 表格样式 - 完全按照PMO实现 */
:deep(.message-table) {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

:deep(.message-table th),
:deep(.message-table td) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

:deep(.message-table th) {
  background-color: #f2f2f2;
  font-weight: bold;
}

:deep(.message-table tr:nth-child(even)) {
  background-color: #f9f9f9;
}

/* 代码块样式 */
:deep(.code-block) {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  margin: 10px 0;
}

:deep(.code-block pre) {
  padding: 16px;
  margin: 0;
  white-space: pre-wrap;
  font-family: monospace;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #606266;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-ocr {
    padding: 15px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .header-actions {
    justify-content: center;
  }

  .file-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .result-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}

/* 智能分析结果样式 */
.analysis-section {
  margin-top: 30px;
}

.analysis-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analysis-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.analysis-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.analysis-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.analysis-content {
  padding: 25px;
}

.prompt-section {
  margin-bottom: 25px;
}

.prompt-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.prompt-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #667eea;
}

.prompt-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
}

.ai-response-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-response-content {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #28a745;
}

.ai-response-content :deep(h1),
.ai-response-content :deep(h2),
.ai-response-content :deep(h3),
.ai-response-content :deep(h4) {
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 10px;
}

.ai-response-content :deep(strong) {
  color: #495057;
  font-weight: 600;
}

.ai-response-content :deep(p) {
  margin-bottom: 12px;
  line-height: 1.6;
}

.ai-response-content :deep(ul),
.ai-response-content :deep(ol) {
  margin-left: 20px;
  margin-bottom: 15px;
}

.ai-response-content :deep(li) {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 文件选择样式 */
.file-selection-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
}

.file-selection-section h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

/* 最终报表样式 */
.final-report-section {
  margin-top: 30px;
}

.final-report-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.report-summary {
  padding: 25px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.report-summary h4 {
  color: white;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.summary-info p {
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-info strong {
  font-weight: 600;
}

.report-details {
  padding: 25px;
  border-bottom: 1px solid #e9ecef;
}

.report-details h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.report-content {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #28a745;
}

.selected-files-list {
  padding: 25px;
}

.selected-files-list h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.file-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.file-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.file-info .filename {
  font-weight: 500;
  color: #2c3e50;
}

.file-analysis {
  font-size: 12px;
  color: #6c757d;
}

.report-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

/* 分析表格样式 */
.analysis-table {
  margin-bottom: 20px;
}

.analysis-table .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.analysis-table .el-table th {
  background: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.financial-data {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.financial-data h5 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

.remark-info {
  margin-top: 15px;
  padding: 15px;
  background: #fff3cd;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
}

.remark-info h5 {
  color: #856404;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
}

.remark-info p {
  color: #856404;
  margin: 0;
  font-size: 13px;
}

.raw-response {
  padding: 15px;
  background: #f8d7da;
  border-radius: 8px;
  border-left: 4px solid #dc3545;
}

.raw-response h5 {
  color: #721c24;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

/* 汇总分析表格样式 */
.summary-analysis-table {
  margin-top: 20px;
}

.summary-analysis-table h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.summary-analysis-table .file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-analysis-table .filename {
  font-weight: 500;
  color: #2c3e50;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.text-muted {
  color: #6c757d;
  font-style: italic;
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.file-detail-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.file-detail-info h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.file-detail-info p {
  margin-bottom: 8px;
  font-size: 14px;
}

.structured-result {
  margin-bottom: 20px;
}

.raw-response-section {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #6c757d;
}

.raw-response-section h5 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

.raw-response-content {
  max-height: 300px;
  overflow-y: auto;
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.error-detail {
  padding: 15px;
  background: #f8d7da;
  border-radius: 8px;
  border-left: 4px solid #dc3545;
}

.error-detail h4 {
  color: #721c24;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.error-detail p {
  color: #721c24;
  margin: 0;
}

/* 模板选择样式 */
.template-selection {
  display: inline-block;
  margin-right: 10px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

/* 提示词和AI回答样式 */
.prompt-section {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.prompt-section h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.prompt-stats, .ai-response-stats {
  margin-bottom: 10px;
}

.prompt-stats .el-tag, .ai-response-stats .el-tag {
  margin-right: 8px;
}

.ai-response-section {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #10b981;
}

.ai-response-section h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.prompt-content {
  max-height: 400px;
  overflow-y: auto;
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.ai-response-section {
  margin: 20px 0;
  padding: 15px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.ai-response-section h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.ai-response-content {
  max-height: 500px;
  overflow-y: auto;
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  line-height: 1.6;
}
</style>
