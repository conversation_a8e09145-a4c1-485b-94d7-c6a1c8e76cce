#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试前端显示功能
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1/document"

def test_frontend_features():
    """
    测试前端显示相关的功能
    """
    print("🖥️ 测试前端显示功能")
    print("=" * 50)
    
    # 测试1：短文本（应该显示"单次处理"）
    print("\n📝 测试1：短文本处理...")
    short_request = {
        'prompt': '请生成一个简单的资产负债表示例。',
        'template_name': '一般企业 (2).xls',
        'selected_files': ['短文本测试.pdf']
    }
    
    try:
        response = requests.post(f'{BASE_URL}/call-qwen-api', json=short_request, timeout=30)
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('ai_response', '')
            print(f"✅ 短文本处理成功")
            print(f"回答长度: {len(ai_response)} 字符")
            print(f"包含分割标识: {'是' if '分割处理汇总' in ai_response else '否'}")
            print(f"前端应显示: {'分割处理' if '分割处理汇总' in ai_response else '单次处理'}")
        else:
            print(f"❌ 短文本测试失败: {response.text}")
    except Exception as e:
        print(f"❌ 短文本测试异常: {str(e)}")
    
    # 测试2：长文本（应该显示"分割处理"）
    print("\n📄 测试2：长文本处理...")
    long_content = "请根据以下详细信息生成完整的财务报表：\n\n" + "详细的财务数据和要求。" * 800 + "\n\n请生成标准格式的报表。"
    
    long_request = {
        'prompt': long_content,
        'template_name': '一般企业 (2).xls',
        'selected_files': ['长文本测试.pdf']
    }
    
    print(f"长文本长度: {len(long_content)} 字符")
    print(f"预期处理方式: {'分割处理' if len(long_content) > 15000 else '单次处理'}")
    
    try:
        response = requests.post(f'{BASE_URL}/call-qwen-api', json=long_request, timeout=120)
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('ai_response', '')
            print(f"✅ 长文本处理成功")
            print(f"回答长度: {len(ai_response)} 字符")
            print(f"包含分割标识: {'是' if '分割处理汇总' in ai_response else '否'}")
            print(f"前端应显示: {'分割处理' if '分割处理汇总' in ai_response else '单次处理'}")
        else:
            print(f"❌ 长文本测试失败: {response.text}")
    except Exception as e:
        print(f"❌ 长文本测试异常: {str(e)}")
    
    # 测试3：生成提示词（测试长度标识）
    print("\n📋 测试3：提示词长度标识...")
    
    mock_analysis_results = [
        {
            "filename": "测试文件.pdf",
            "content": "测试内容" * 1000,  # 创建长内容
            "parsed_result": {
                "是否为报表": "是",
                "报表类型": "资产负债表",
                "报表主体": "测试公司",
                "报表时期": "2023年12月31日"
            },
            "selected": True
        }
    ]
    
    prompt_request = {
        "selected_files": ["测试文件.pdf"],
        "template_name": "一般企业 (2).xls",
        "analysis_results": mock_analysis_results
    }
    
    try:
        response = requests.post(f'{BASE_URL}/generate-prompt', json=prompt_request, timeout=30)
        if response.status_code == 200:
            data = response.json()
            prompt = data.get('prompt', '')
            print(f"✅ 提示词生成成功")
            print(f"提示词长度: {len(prompt)} 字符")
            print(f"前端应显示: {'长文本将分割处理' if len(prompt) > 15000 else '单次处理'}")
        else:
            print(f"❌ 提示词生成失败: {response.text}")
    except Exception as e:
        print(f"❌ 提示词生成异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 前端显示测试完成")
    print("\n📋 前端显示规则总结：")
    print("1. 提示词长度 > 15000字符 → 显示'长文本将分割处理'")
    print("2. 提示词长度 ≤ 15000字符 → 显示'单次处理'")
    print("3. AI回答包含'分割处理汇总' → 显示'分割处理'")
    print("4. AI回答不包含'分割处理汇总' → 显示'单次处理'")

if __name__ == "__main__":
    test_frontend_features()
