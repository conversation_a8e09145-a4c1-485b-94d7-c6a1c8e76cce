#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 工具函数
提供通用工具函数
"""

import os
import json
import time
import hashlib
import uuid
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger("jintou_brain.utils")

def generate_id(prefix=""):
    """
    生成唯一ID
    
    Args:
        prefix: ID前缀
        
    Returns:
        唯一ID字符串
    """
    return f"{prefix}{uuid.uuid4().hex}"

def get_timestamp():
    """
    获取当前时间戳
    
    Returns:
        当前Unix时间戳
    """
    return int(time.time())

def format_datetime(timestamp=None, format_str="%Y-%m-%d %H:%M:%S"):
    """
    将时间戳格式化为日期时间字符串
    
    Args:
        timestamp: Unix时间戳，默认为当前时间
        format_str: 日期时间格式
        
    Returns:
        格式化的日期时间字符串
    """
    if timestamp is None:
        timestamp = time.time()
    return datetime.fromtimestamp(timestamp).strftime(format_str)

def save_json_to_file(data, filepath):
    """
    将JSON数据保存到文件
    
    Args:
        data: 要保存的数据
        filepath: 文件路径
        
    Returns:
        bool: 操作是否成功
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存JSON文件失败: {e}")
        return False

def load_json_from_file(filepath, default=None):
    """
    从文件加载JSON数据
    
    Args:
        filepath: 文件路径
        default: 文件不存在或加载失败时返回的默认值
        
    Returns:
        加载的数据或默认值
    """
    if not os.path.exists(filepath):
        return default
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件失败: {e}")
        return default

def md5(text):
    """
    计算字符串的MD5哈希值
    
    Args:
        text: 要计算哈希的字符串
        
    Returns:
        MD5哈希值
    """
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def truncate_text(text, max_length=100, suffix="..."):
    """
    截断文本，超过最大长度时添加后缀
    
    Args:
        text: 要截断的文本
        max_length: 最大长度
        suffix: 截断时添加的后缀
        
    Returns:
        截断后的文本
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def ensure_dir(directory):
    """
    确保目录存在，不存在则创建
    
    Args:
        directory: 目录路径
        
    Returns:
        bool: 操作是否成功
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False

def is_json(text):
    """
    检查字符串是否为有效的JSON
    
    Args:
        text: 要检查的字符串
        
    Returns:
        bool: 是否为有效的JSON
    """
    try:
        json.loads(text)
        return True
    except:
        return False

def trim_messages(messages, max_tokens=30000, keep_system_message=True, preserve_latest_messages=4):
    """
    裁剪消息列表，确保总token数不超过最大限制
    
    Args:
        messages: 消息列表，每个消息是字典，包含role和content字段
        max_tokens: 最大token数限制
        keep_system_message: 是否保留系统消息
        preserve_latest_messages: 保留最近的几条消息数
        
    Returns:
        裁剪后的消息列表
    """
    if not messages:
        return messages
    
    # 粗略估计token数 (1个token约等于4个字符)
    def estimate_tokens(msg):
        # 简单估算：一个汉字约为1.5个token，英文单词约为1个token
        content = msg.get("content", "")
        
        # 使用更保守的估算方式，确保不超出限制
        return len(content) // 3 + 8  # 使用更激进的估算，每3个字符约为1个token
    
    # 提取系统消息
    system_messages = []
    if keep_system_message:
        system_messages = [msg for msg in messages if msg.get("role") == "system"]
    
    # 确保保留最近的消息
    preserved_messages = messages[-preserve_latest_messages:] if len(messages) > preserve_latest_messages else messages
    
    # 需要考虑裁剪的消息
    candidate_messages = []
    if keep_system_message:
        candidate_messages = [msg for msg in messages[:-preserve_latest_messages] if msg.get("role") != "system"]
    else:
        candidate_messages = messages[:-preserve_latest_messages] if len(messages) > preserve_latest_messages else []
    
    # 计算系统消息和保留消息的token估算值
    reserved_tokens = sum(estimate_tokens(msg) for msg in system_messages + preserved_messages)
    
    # 计算可用于历史消息的token数
    available_tokens = max_tokens - reserved_tokens
    
    # 如果可用token不足，直接返回系统消息和保留的最近消息
    if available_tokens <= 0:
        # 如果情况严重，甚至需要裁剪最近的消息
        if reserved_tokens > max_tokens:
            # 保留系统消息和最后一条用户消息
            user_messages = [msg for msg in preserved_messages if msg.get("role") == "user"]
            if user_messages:
                last_user_message = user_messages[-1]
                # 粗略裁剪最后一条用户消息内容
                content = last_user_message.get("content", "")
                max_content_tokens = max_tokens - 10  # 预留一些token给消息结构
                if estimate_tokens(last_user_message) > max_content_tokens:
                    # 裁剪内容
                    max_content_chars = max_content_tokens * 4
                    last_user_message["content"] = content[:max_content_chars] + "...(内容已截断)"
                return system_messages + [last_user_message]
            return system_messages
        return system_messages + preserved_messages
    
    # 从最新到最早添加历史消息，直到达到token限制
    result = system_messages + preserved_messages
    for msg in reversed(candidate_messages):
        msg_tokens = estimate_tokens(msg)
        if available_tokens >= msg_tokens:
            result.insert(len(system_messages), msg)  # 插入到系统消息之后
            available_tokens -= msg_tokens
        else:
            break
    
    return result 