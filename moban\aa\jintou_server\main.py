#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - API服务主程序
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import logging

# 添加当前目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, current_dir)

# 打印当前路径信息，用于调试
print(f"当前工作目录: {os.getcwd()}")
print(f"当前脚本目录: {current_dir}")
print(f"父级目录: {parent_dir}")

# 导入API路由
try:
    print("尝试导入API模块...")
    from api.chat_api import router as chat_router
    from api.due_diligence_api import router as due_diligence_router
    from api.kimi_api import router as kimi_router
    from api.ocr_assistant_api import router as ocr_assistant_router
    from api.ocr_chat_api import router as ocr_chat_router
    from api import (
        auth_api, 
        due_diligence_api, 
        kimi_api,
        ocr_assistant_api,
        ocr_vision_api,  # OCR视觉模型API
        ocr_vision_direct_api,  # 添加OCR视觉直接转发API
        user_api,
        report_storage_api,
        ocr_chat_api,  # 添加逗号
        ocr_external_api  # 添加外部大模型API
    )
    print("API模块导入成功！")
except ImportError as e:
    print(f"导入错误: {e}")
    print("错误: 无法导入API模块")
    print(f"当前Python路径: {sys.path}")
    print(f"当前工作目录: {os.getcwd()}")
    sys.exit(1)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="金投大脑API",
    description="金投大脑智能问答平台API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 添加API路由
app.include_router(chat_router)
app.include_router(due_diligence_router)
app.include_router(kimi_router)
app.include_router(ocr_assistant_router)
# app.include_router(ocr_chat_router)  # 注释掉重复的路由注册
app.include_router(auth_api.router)
app.include_router(due_diligence_api.router)
app.include_router(kimi_api.router)
app.include_router(ocr_assistant_api.router)
app.include_router(ocr_chat_api.router)  # 保留这一个
app.include_router(ocr_vision_api.router)  # 注册OCR视觉模型API路由
app.include_router(ocr_vision_direct_api.router)  # 注册OCR视觉直接转发API路由
app.include_router(ocr_external_api.router)  # 注册外部大模型API路由
app.include_router(user_api.router, prefix="/api")
app.include_router(report_storage_api.router, prefix="/api")

# API根路径响应
@app.get("/")
async def root():
    return {
        "name": "金投大脑API",
        "version": "1.0.0",
        "description": "欢迎使用金投大脑智能问答平台API服务",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 确保数据目录存在
os.makedirs(os.path.join(os.path.dirname(__file__), "data"), exist_ok=True)
os.makedirs(os.path.join(os.path.dirname(__file__), "data", "reports"), exist_ok=True)

# 启动时的日志
@app.on_event("startup")
async def startup_event():
    logger.info("金投OCR助手API服务已启动")

# 主函数
if __name__ == "__main__":
    # 获取环境变量或使用默认值
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "5000"))
    
    # 启动Uvicorn服务器
    print(f"启动服务器，监听地址: {host}:{port}")
    uvicorn.run(
        app,  # 直接使用app对象
        host=host, 
        port=port, 
        reload=False,
        log_level="info"
    ) 