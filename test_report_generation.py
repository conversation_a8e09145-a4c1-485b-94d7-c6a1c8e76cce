#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试报表生成功能的完整流程
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1/document"

def test_get_templates():
    """
    测试获取报表模板
    """
    print("1. 测试获取报表模板...")
    try:
        response = requests.get(f"{BASE_URL}/report-templates")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取模板成功")
            print(f"模板数量: {len(data.get('templates', []))}")
            for template in data.get('templates', []):
                print(f"  - {template['display_name']} ({template['name']})")
            return data.get('templates', [])
        else:
            print(f"❌ 获取模板失败: {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取模板异常: {str(e)}")
        return []

def test_generate_prompt():
    """
    测试生成提示词
    """
    print("\n2. 测试生成提示词...")
    
    # 模拟分析结果数据
    mock_analysis_results = [
        {
            "filename": "测试财务报表.pdf",
            "content": "资产负债表\n流动资产：\n货币资金 1000000\n应收账款 500000\n存货 300000\n流动资产合计 1800000\n\n非流动资产：\n固定资产 2000000\n无形资产 200000\n非流动资产合计 2200000\n\n资产总计 4000000",
            "parsed_result": {
                "是否为报表": "是",
                "报表类型": "资产负债表",
                "报表主体": "[公司名称]",
                "报表时期": "2023年12月31日",
                "置信度": "95%"
            },
            "selected": True
        }
    ]
    
    request_data = {
        "selected_files": ["测试财务报表.pdf"],
        "template_name": "一般企业 (2).xls",
        "analysis_results": mock_analysis_results
    }
    
    try:
        response = requests.post(f"{BASE_URL}/generate-prompt", json=request_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 生成提示词成功")
            prompt = data.get('prompt', '')
            print(f"提示词长度: {len(prompt)} 字符")
            print(f"提示词预览:\n{prompt[:500]}...")
            return prompt
        else:
            print(f"❌ 生成提示词失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 生成提示词异常: {str(e)}")
        return None

def test_call_qwen_api(prompt):
    """
    测试调用千问API
    """
    print("\n3. 测试调用千问API...")
    
    if not prompt:
        print("❌ 没有提示词，跳过测试")
        return None
    
    request_data = {
        "prompt": prompt,
        "template_name": "一般企业 (2).xls",
        "selected_files": ["测试财务报表.pdf"]
    }
    
    try:
        print("正在调用千问API，请稍候...")
        response = requests.post(f"{BASE_URL}/call-qwen-api", json=request_data, timeout=60)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 调用千问API成功")
            ai_response = data.get('ai_response', '')
            print(f"AI回答长度: {len(ai_response)} 字符")
            print(f"AI回答预览:\n{ai_response[:500]}...")
            return ai_response
        else:
            print(f"❌ 调用千问API失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 调用千问API异常: {str(e)}")
        return None

def test_full_process():
    """
    测试完整的报表生成流程
    """
    print("\n4. 测试完整流程...")
    
    # 模拟分析结果数据
    mock_analysis_results = [
        {
            "filename": "测试财务报表.pdf",
            "content": "资产负债表\n流动资产：\n货币资金 1000000\n应收账款 500000\n存货 300000\n流动资产合计 1800000\n\n非流动资产：\n固定资产 2000000\n无形资产 200000\n非流动资产合计 2200000\n\n资产总计 4000000",
            "parsed_result": {
                "是否为报表": "是",
                "报表类型": "资产负债表",
                "报表主体": "[公司名称]",
                "报表时期": "2023年12月31日",
                "置信度": "95%"
            },
            "selected": True
        }
    ]
    
    request_data = {
        "selected_files": ["测试财务报表.pdf"],
        "template_name": "一般企业 (2).xls",
        "analysis_results": mock_analysis_results
    }
    
    try:
        print("正在执行完整流程，请稍候...")
        response = requests.post(f"{BASE_URL}/generate-report-with-template", json=request_data, timeout=120)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 完整流程执行成功")
            print(f"提示词长度: {len(data.get('prompt', ''))} 字符")
            print(f"AI回答长度: {len(data.get('ai_response', ''))} 字符")
            return True
        else:
            print(f"❌ 完整流程失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 完整流程异常: {str(e)}")
        return False

if __name__ == "__main__":
    print("报表生成功能测试脚本")
    print("=" * 50)
    
    # 测试获取模板
    templates = test_get_templates()
    
    if templates:
        # 测试生成提示词
        prompt = test_generate_prompt()
        
        if prompt:
            # 测试调用千问API
            ai_response = test_call_qwen_api(prompt)
            
            # 测试完整流程
            full_success = test_full_process()
            
            print("\n" + "=" * 50)
            print("测试结果汇总:")
            print(f"获取模板: {'✅ 成功' if templates else '❌ 失败'}")
            print(f"生成提示词: {'✅ 成功' if prompt else '❌ 失败'}")
            print(f"调用千问API: {'✅ 成功' if ai_response else '❌ 失败'}")
            print(f"完整流程: {'✅ 成功' if full_success else '❌ 失败'}")
            
            if templates and prompt and ai_response and full_success:
                print("\n🎉 所有测试通过，报表生成功能完全可用!")
            else:
                print("\n⚠️ 部分测试失败，请检查相关功能")
        else:
            print("\n❌ 提示词生成失败，无法继续测试")
    else:
        print("\n❌ 获取模板失败，无法继续测试")
