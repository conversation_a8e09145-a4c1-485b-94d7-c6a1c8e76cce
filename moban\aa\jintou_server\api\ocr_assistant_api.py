from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks, Body
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import base64
import io
import json
import os
import uuid
import time
from datetime import datetime
import logging
import asyncio
import numpy as np
from PIL import Image, ImageDraw
import fitz  # PyMuPDF
import requests
from io import BytesIO
import re
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ocr_assistant_api")

# 创建路由
router = APIRouter(prefix="/api/ocr-assistant", tags=["OCR助手"])

# 任务状态存储
tasks_status = {}

# OCR服务配置 - 使用与due_diligence_api.py相同的配置
OCR_SERVICE_URL = "http://10.0.10.42:5005/invoke"  # base64接口
OCR_API_KEY = os.getenv("OCR_API_KEY", "")

class OCRResponse(BaseModel):
    """OCR识别响应模型"""
    code: int = 0
    message: str = "识别成功"
    device: str = "GPU"
    duration: Optional[str] = None
    original: Optional[List[Dict[str, Any]]] = None
    result: Optional[str] = None
    image_base64: Optional[str] = None

def check_api_config():
    """检查API配置是否有效"""
    if not OCR_SERVICE_URL:
        logger.warning("OCR API URL未配置，将使用本地默认地址")
    return True

def image_to_base64(image_bytes, image_type="jpeg"):
    """将图片字节转换为Base64编码"""
    return base64.b64encode(image_bytes).decode('utf-8')

def pil_image_to_base64(image, format="JPEG"):
    """将PIL图像转换为Base64编码"""
    buffered = BytesIO()
    image.save(buffered, format=format)
    img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')
    return img_str

def base64_to_pil_image(image_base64):
    """将Base64编码转换为PIL图像"""
    if image_base64.startswith('data:image'):
        # 移除MIME类型前缀
        image_base64 = image_base64.split(',')[1]
    
    image_data = base64.b64decode(image_base64)
    image = Image.open(BytesIO(image_data))
    return image

def call_ocr_service(image_base64):
    """调用OCR服务进行文字识别"""
    check_api_config()
    
    try:
        # 准备请求数据
        payload = {
            "image_base64": image_base64
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        if OCR_API_KEY:
            headers["X-API-KEY"] = OCR_API_KEY
        
        # 发送请求
        start_time = time.time()
        logger.info(f"正在发送OCR请求到 {OCR_SERVICE_URL}...")
        response = requests.post(OCR_SERVICE_URL, headers=headers, json=payload)
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            logger.info(f"OCR请求成功! 耗时: {elapsed:.2f} 秒")
            # 解析JSON
            result = response.json()
            
            # 检查结果是否是字符串，如果是则尝试解析为JSON
            if isinstance(result, str):
                try:
                    result = json.loads(result)
                    logger.info("成功将字符串响应解析为JSON")
                except json.JSONDecodeError:
                    logger.warning("OCR响应是字符串，但无法解析为JSON")
                    # 创建一个包含原始字符串的结构
                    result = {
                        "result": [{"text": result, "box": []}],
                        "raw_text": result
                    }
            
            # 如果结果中没有result字段，添加一个
            if "result" not in result:
                # 尝试从original字段中提取
                if "original" in result:
                    result["result"] = result["original"]
                else:
                    # 创建一个简单的result结构
                    result["result"] = [{"text": str(result), "box": []}]
            
            return result
        else:
            logger.error(f"OCR API请求失败: {response.status_code} - {response.text}")
            return {
                "code": response.status_code,
                "message": f"OCR识别失败: {response.text}",
                "result": []
            }
    except Exception as e:
        logger.exception(f"调用OCR服务时出错: {str(e)}")
        return {
            "code": 500,
            "message": f"OCR识别出错: {str(e)}",
            "result": []
        }

def identify_company_names(ocr_result):
    """
    从OCR结果中识别公司名称
    Args:
        ocr_result: OCR识别结果
    Returns:
        公司名称列表，每个元素为(名称, 位置)元组
    """
    company_names = []
    
    # 检查OCR结果格式
    if not ocr_result:
        logger.error("无效的OCR结果")
        return company_names
    
    # 获取文本块列表
    text_blocks = []
    
    # 尝试从不同格式中获取文本块
    if isinstance(ocr_result, str):
        # 如果是字符串，创建一个简单的文本块
        text_blocks = [{"text": ocr_result, "box": []}]
    elif isinstance(ocr_result, dict):
        if "result" in ocr_result and isinstance(ocr_result["result"], list):
            text_blocks = ocr_result["result"]
        elif "original" in ocr_result and isinstance(ocr_result["original"], list):
            text_blocks = ocr_result["original"]
        elif "raw_text" in ocr_result:
            text_blocks = [{"text": ocr_result["raw_text"], "box": []}]
    
    if not text_blocks:
        logger.error("无法从OCR结果中提取文本块")
        return company_names
    
    # 企业名称的正则表达式模式
    company_patterns = [
        r'[\u4e00-\u9fa5]{2,}(?:公司|集团|企业|有限|责任|股份|合伙|商贸|科技|信息|咨询|服务|投资|金融|证券|保险|银行|资产|管理|控股)',
        r'[\u4e00-\u9fa5]+(?:有限公司|集团公司|股份有限公司)'
    ]
    
    # 排除的财务术语列表
    exclude_terms = [
        "流动资产", "非流动资产", "固定资产", "无形资产", "递延所得税资产", "其他资产", 
        "抵债资产", "可供出售金融资产", "持有至到期投资", "长期股权投资", "其他流动资产", 
        "其他非流动资产", "未到期责任", "提取未到期责任", "摊回未到期责任", "业务及管理",
        "归属于母公司"
    ]
    
    # 遍历每个文本块
    for block in text_blocks:
        # 获取文本内容
        if isinstance(block, dict):
            text = block.get("text", "")
            box = block.get("box", [])
        else:
            # 如果block不是字典，尝试将其作为文本处理
            text = str(block)
            box = []
        
        # 跳过排除的财务术语
        if text in exclude_terms:
            continue
        
        # 检查是否匹配企业名称模式
        is_company_name = False
        matched_name = ""
        
        # 首先检查完整文本是否是企业名称
        for pattern in company_patterns:
            if re.fullmatch(pattern, text):
                is_company_name = True
                matched_name = text
                break
        
        # 如果整个文本不是企业名称，尝试在文本中查找企业名称
        if not is_company_name:
            for pattern in company_patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    company_name = match.group(0)
                    # 再次检查是否在排除列表中
                    if company_name not in exclude_terms:
                        is_company_name = True
                        matched_name = company_name
                        break
        
        # 如果识别到企业名称
        if is_company_name:
            logger.info(f"找到企业名称: {matched_name}")
            
            # 如果有box信息，转换为矩形坐标
            position = None
            if box and len(box) == 4:  # box格式为四个点的坐标
                # 提取四个点的坐标
                points = box
                
                # 计算矩形的左上角和右下角坐标
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                x0 = min(x_coords)
                y0 = min(y_coords)
                x1 = max(x_coords)
                y1 = max(y_coords)
                
                position = [x0, y0, x1, y1]
                logger.info(f"企业名称位置: {position}")
            
            # 保存企业名称和位置
            company_names.append((matched_name, position))
    
    return company_names

def mask_company_names(image, company_names, mask_color=(255, 0, 0)):
    """
    在图像上遮盖公司名称
    Args:
        image: PIL.Image对象
        company_names: 公司名称列表，每个元素为(名称, 位置)元组
        mask_color: 遮盖颜色，默认红色
    Returns:
        处理后的图像
    """
    # 创建一个可绘制的图像副本
    masked_image = image.copy()
    draw = ImageDraw.Draw(masked_image)
    
    # 遍历每个公司名称
    for name, position in company_names:
        if position:
            # 提取位置坐标
            x0, y0, x1, y1 = position
            
            # 增加矩形区域的大小，确保完全覆盖文字
            padding = 2
            x0 = max(0, x0 - padding)
            y0 = max(0, y0 - padding)
            x1 = min(image.width, x1 + padding)
            y1 = min(image.height, y1 + padding)
            
            # 绘制矩形遮盖，使用完全不透明的颜色
            draw.rectangle([x0, y0, x1, y1], fill=mask_color, outline=None)
            
            # 记录日志
            logger.info(f"已遮盖公司名称: {name}, 位置: {position}")
    
    return masked_image

def pdf_to_images(pdf_data):
    """将PDF文件转换为图像列表"""
    images = []
    
    try:
        # 打开PDF
        pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
        
        # 遍历每一页
        for page_num in range(len(pdf_document)):
            # 获取页面
            page = pdf_document[page_num]
            
            # 渲染页面为图像
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            
            # 转换为PIL图像
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            
            # 添加到列表
            images.append(img)
        
        # 关闭PDF
        pdf_document.close()
        
        return images
    except Exception as e:
        logger.exception(f"PDF转换为图像时出错: {str(e)}")
        return []

def extract_pdf_data(image_base64):
    """从Base64编码的PDF中提取数据"""
    try:
        # 解码Base64
        pdf_data = base64.b64decode(image_base64)
        
        # 转换为图像
        images = pdf_to_images(pdf_data)
        
        return images
    except Exception as e:
        logger.exception(f"提取PDF数据时出错: {str(e)}")
        return []

def extract_report_info(ocr_text):
    """
    从OCR文本中提取公司名称、报表年份和月份
    
    Args:
        ocr_text: OCR识别的文本内容
        
    Returns:
        包含公司名称、年份和月份的字典
    """
    info = {
        "company_name": None,
        "report_year": None,
        "report_month": None
    }
    
    # 提取公司名称（通常包含"公司"、"集团"、"有限"等关键词）
    company_patterns = [
        r'([\u4e00-\u9fa5]{2,}(?:公司|集团|企业|有限|股份|责任|合伙|投资|科技|技术)[\u4e00-\u9fa5]{0,20})',
        r'([\u4e00-\u9fa5]{2,}[\u4e00-\u9fa5、]{0,10}(?:公司|集团))',
        r'([\u4e00-\u9fa5]{2,}(?:公司|集团|企业|有限|股份|责任|合伙|投资|科技|技术))',
        r'([\u4e00-\u9fa5]{4,})'  # 备用：匹配任何至少4个字的中文词组
    ]
    
    for pattern in company_patterns:
        company_matches = re.findall(pattern, ocr_text)
        if company_matches:
            # 取最长的匹配结果作为公司名称
            info["company_name"] = max(company_matches, key=len)
            break
    
    # 提取年份（4位数字，通常在2000-2030之间）
    year_patterns = [
        r'(20\d{2})[\s年度]',  # 标准格式：2023年
        r'(20\d{2})[\s./\-年]+(\d{1,2})[\s./\-月]',  # 带月份的格式：2023年6月
        r'(20\d{2})'  # 备用：仅匹配年份
    ]
    
    for pattern in year_patterns:
        year_matches = re.findall(pattern, ocr_text)
        if year_matches:
            if isinstance(year_matches[0], tuple):
                info["report_year"] = year_matches[0][0]
            else:
                info["report_year"] = year_matches[0]
            break
    
    # 提取月份（1-12月）
    month_patterns = [
        r'(\d{1,2})[\s月]',  # 标准格式：6月
        r'20\d{2}[\s./\-年]+(\d{1,2})[\s./\-月]'  # 年月组合格式：2023年6月
    ]
    
    for pattern in month_patterns:
        month_matches = re.findall(pattern, ocr_text)
        if month_matches:
            # 确保月份在1-12之间
            for match in month_matches:
                try:
                    month = int(match)
                    if 1 <= month <= 12:
                        info["report_month"] = f"{month:02d}"  # 格式化为两位数
                        break
                except ValueError:
                    continue
            if info["report_month"]:
                break
    
    # 调试输出
    logger.info(f"提取的报表信息: {info}")
    return info

def protect_privacy(image_base64, mask_color=(255, 0, 0)):
    """
    保护隐私，遮盖图像或PDF中的公司名称
    Args:
        image_base64: Base64编码的图像或PDF数据
        mask_color: 遮盖颜色，默认红色
    Returns:
        处理后的图像列表(Base64编码)和识别结果
    """
    try:
        # 检查是否为PDF
        is_pdf = False
        try:
            if "data:application/pdf" in image_base64:
                is_pdf = True
                # 提取PDF的Base64编码部分
                pdf_base64 = image_base64.replace('data:application/pdf;base64,', '')
                pdf_images = extract_pdf_data(pdf_base64)
                if not pdf_images:
                    logger.error("PDF处理失败")
                    return [image_base64], [{"error": "PDF处理失败"}], [{}]
            else:
                pdf_images = extract_pdf_data(image_base64)
                if pdf_images:
                    is_pdf = True
        except Exception as e:
            logger.error(f"PDF提取失败: {str(e)}")
            is_pdf = False
        
        processed_images = []
        ocr_results = []
        report_infos = []  # 存储每个图片的报表信息
        
        if is_pdf:
            # 处理PDF中的每一页
            for img_data in pdf_images:
                try:
                    # 将PIL图像转换为Base64
                    buffered = BytesIO()
                    img_data.save(buffered, format="PNG")
                    page_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                    
                    # 调用OCR服务
                    ocr_result = call_ocr_service(page_base64)
                    
                    # 检查OCR结果
                    if not ocr_result:
                        logger.error("OCR服务返回无效结果")
                        processed_images.append(page_base64)  # 添加原始图像
                        ocr_results.append({"error": "OCR识别失败"})
                        report_infos.append({})  # 添加空的报表信息
                        continue
                    
                    # 识别公司名称
                    company_names = identify_company_names(ocr_result)
                    
                    # 提取OCR文本
                    ocr_text = ""
                    if "result" in ocr_result and isinstance(ocr_result["result"], list):
                        for item in ocr_result["result"]:
                            if isinstance(item, dict) and "text" in item:
                                ocr_text += item["text"] + "\n"
                    
                    # 提取报表信息
                    report_info = extract_report_info(ocr_text)
                    report_infos.append(report_info)
                    
                    # 如果识别到公司名称，遮盖它们
                    if company_names:
                        # 遮盖公司名称
                        masked_img = mask_company_names(img_data, company_names, mask_color)
                        
                        # 将处理后的图像转换回Base64
                        buffered = BytesIO()
                        masked_img.save(buffered, format="PNG")
                        processed_image_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                        
                        # 添加处理后的图像
                        processed_images.append(processed_image_base64)
                    else:
                        # 如果没有识别到公司名称，添加原始图像
                        processed_images.append(page_base64)
                    
                    # 添加OCR结果
                    ocr_results.append(ocr_result)
                except Exception as e:
                    logger.error(f"处理PDF页面时出错: {str(e)}")
                    # 如果处理某页出错，添加空结果并继续处理下一页
                    processed_images.append("")
                    ocr_results.append({"error": f"处理失败: {str(e)}"})
                    report_infos.append({})  # 添加空的报表信息
        else:
            # 处理单个图像
            # 调用OCR服务
            ocr_result = call_ocr_service(image_base64)
            
            # 检查OCR结果
            if not ocr_result:
                logger.error("OCR服务返回无效结果")
                return [image_base64], [{"error": "OCR识别失败"}], [{}]
            
            # 提取OCR文本
            ocr_text = ""
            if "result" in ocr_result and isinstance(ocr_result["result"], list):
                for item in ocr_result["result"]:
                    if isinstance(item, dict) and "text" in item:
                        ocr_text += item["text"] + "\n"
            
            # 提取报表信息
            report_info = extract_report_info(ocr_text)
            report_infos.append(report_info)
            
            # 识别公司名称
            company_names = identify_company_names(ocr_result)
            
            # 如果识别到公司名称，遮盖它们
            if company_names:
                # 将Base64转换为图像对象
                try:
                    # 检查是否有数据URI前缀
                    if "data:" in image_base64 and ";base64," in image_base64:
                        # 提取Base64部分
                        base64_data = image_base64.split(";base64,")[1]
                    else:
                        base64_data = image_base64
                    
                    image_bytes = base64.b64decode(base64_data)
                    img = Image.open(BytesIO(image_bytes))
                    
                    # 遮盖公司名称
                    masked_img = mask_company_names(img, company_names, mask_color)
                    
                    # 将处理后的图像转换回Base64
                    buffered = BytesIO()
                    masked_img.save(buffered, format="PNG")
                    processed_image_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                    
                    # 添加处理后的图像
                    processed_images.append(processed_image_base64)
                except Exception as e:
                    logger.error(f"处理图像时出错: {str(e)}")
                    # 如果处理出错，返回原始图像
                    processed_images.append(image_base64)
            else:
                # 如果没有识别到公司名称，添加原始图像
                processed_images.append(image_base64)
            
            # 添加OCR结果
            ocr_results.append(ocr_result)
        
        return processed_images, ocr_results, report_infos
    
    except Exception as e:
        logger.error(f"隐私保护处理失败: {str(e)}")
        traceback.print_exc()
        return [image_base64], [{"error": f"处理失败: {str(e)}"}], [{}]

@router.get("/status")
async def get_api_status():
    """获取API状态"""
    try:
        check_api_config()
        return {"status": "ok", "message": "OCR助手API正常运行"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

@router.post("/process-image")
async def process_image(request: dict = Body(...)):
    """
    处理Base64编码的图像
    """
    try:
        # 获取请求参数
        image_base64 = request.get("image_base64", "")
        mask_color = request.get("mask_color", (255, 0, 0))  # 默认红色
        
        # 验证参数
        if not image_base64:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "缺少图像数据", "data": None}
            )
        
        # 处理图像
        processed_images, ocr_results, report_infos = protect_privacy(image_base64, mask_color)
        
        # 构建响应
        result_data = []
        for i, (img, ocr, report_info) in enumerate(zip(processed_images, ocr_results, report_infos)):
            # 确保图像有正确的前缀
            if not img.startswith('data:'):
                img_type = "image/jpeg"  # 默认JPEG
                img = f"data:{img_type};base64,{img}"
            
            # 提取OCR结果中的文本，组合为一个字符串
            ocr_text = ""
            if "result" in ocr and isinstance(ocr["result"], list):
                for item in ocr["result"]:
                    if isinstance(item, dict) and "text" in item:
                        ocr_text += item["text"] + "\n"
            
            result_data.append({
                "image_base64": img,
                "ocr_result": ocr_text,  # 提供文本格式的OCR结果
                "original_data": ocr,     # 保留原始OCR数据
                "report_info": report_info
            })
        
        return JSONResponse(
            content={
                "code": 0,
                "message": "处理成功",
                "processed_images": result_data  # 使用前端期望的字段名
            }
        )
    
    except Exception as e:
        logger.error(f"图像处理失败: {str(e)}")
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"处理失败: {str(e)}", "processed_images": []}
        )

@router.post("/process-file")
async def process_file(file: UploadFile = File(...), mask_color: str = Form("255,0,0")):
    """
    处理上传的文件（PDF或图像）
    """
    try:
        # 解析遮盖颜色
        try:
            mask_color_parts = mask_color.split(",")
            mask_color_tuple = tuple(int(part.strip()) for part in mask_color_parts)
        except:
            mask_color_tuple = (255, 0, 0)  # 默认红色
        
        # 读取文件内容
        file_content = await file.read()
        
        # 检查文件类型
        content_type = file.content_type
        
        # 转换为Base64
        file_base64 = base64.b64encode(file_content).decode('utf-8')
        
        # 添加适当的数据URI前缀
        if content_type.startswith('image/'):
            file_base64 = f"data:{content_type};base64,{file_base64}"
        elif content_type == 'application/pdf':
            file_base64 = f"data:application/pdf;base64,{file_base64}"
        else:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "不支持的文件类型", "processed_images": []}
            )
        
        # 处理文件
        processed_images, ocr_results, report_infos = protect_privacy(file_base64, mask_color_tuple)
        
        # 构建响应
        result_data = []
        for i, (img, ocr, report_info) in enumerate(zip(processed_images, ocr_results, report_infos)):
            # 确保图像有正确的前缀
            if not img.startswith('data:'):
                img_type = "image/jpeg"  # 默认JPEG
                img = f"data:{img_type};base64,{img}"
            
            # 提取OCR结果中的文本，组合为一个字符串
            ocr_text = ""
            if "result" in ocr and isinstance(ocr["result"], list):
                for item in ocr["result"]:
                    if isinstance(item, dict) and "text" in item:
                        ocr_text += item["text"] + "\n"
            
            result_data.append({
                "image_base64": img,
                "ocr_result": ocr_text,  # 提供文本格式的OCR结果
                "original_data": ocr,     # 保留原始OCR数据
                "report_info": report_info
            })
        
        return JSONResponse(
            content={
                "code": 0,
                "message": "处理成功",
                "processed_images": result_data  # 使用前端期望的字段名
            }
        )
    
    except Exception as e:
        logger.error(f"文件处理失败: {str(e)}")
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"处理失败: {str(e)}", "processed_images": []}
        ) 