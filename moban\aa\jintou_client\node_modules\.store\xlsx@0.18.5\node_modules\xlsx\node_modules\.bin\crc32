#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/E:/HuaweiMoveData/Users/<USER>/Desktop/deepseek2(python)/jintou_client/node_modules/.store/crc-32@1.2.2/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/E:/HuaweiMoveData/Users/<USER>/Desktop/deepseek2(python)/jintou_client/node_modules/.store/crc-32@1.2.2/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../crc-32@1.2.2/node_modules/crc-32/bin/crc32.njs" "$@"
else
  exec node  "$basedir/../../../../../crc-32@1.2.2/node_modules/crc-32/bin/crc32.njs" "$@"
fi
