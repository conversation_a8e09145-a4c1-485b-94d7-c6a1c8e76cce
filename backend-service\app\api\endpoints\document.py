"""
文档上传和解析API端点
"""

import logging
import os
import json
import asyncio
import pandas as pd
import xlrd
import re
from typing import List, Dict, Any
from fastapi import APIRouter, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import aiohttp
from http import HTTPStatus
import dashscope

from app.services.document_parser import DocumentParser

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()
file_router = APIRouter()

# 初始化文档解析器
document_parser = DocumentParser()

# 金投大脑API配置
DEEPSEEK_API_URL = os.getenv("DEEPSEEK_API_URL", "http://10.0.10.41:8000/v1/chat/completions")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

# 千问API配置 - 从环境变量读取
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")
DASHSCOPE_MODEL = os.getenv("DASHSCOPE_MODEL", "qwen-plus")
DASHSCOPE_BUSINESS_SPACE = os.getenv("DASHSCOPE_BUSINESS_SPACE")

# 数据模型
class ReportTemplateRequest(BaseModel):
    selected_files: List[str]
    template_name: str

class GenerateReportRequest(BaseModel):
    selected_files: List[str]
    template_name: str
    analysis_results: List[Dict[str, Any]]

@file_router.post("/upload-files")
async def upload_files(
    files: List[UploadFile] = File(...)
):
    """
    第一步：上传多个文件并解析内容，返回解析结果给前端显示
    支持Excel、Word、PDF、PPT、图片等格式
    """
    try:
        parsed_files = []
        success_count = 0

        for file in files:
            # 读取文件内容
            file_content = await file.read()

            # 验证文件大小（限制为10MB）
            if len(file_content) > 10 * 1024 * 1024:
                parsed_files.append({
                    "filename": file.filename,
                    "error": "文件大小超过10MB限制",
                    "success": False
                })
                continue

            # 验证文件内容不为空
            if not file_content or len(file_content) == 0:
                parsed_files.append({
                    "filename": file.filename,
                    "error": "文件内容为空",
                    "success": False
                })
                continue

            try:
                # 解析文档内容
                parsed_content = document_parser.parse_document(file_content, file.filename, file.content_type)

                # 如果是图片，需要异步处理
                if parsed_content.startswith("IMAGE_TO_PROCESS:"):
                    image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                    try:
                        parsed_content = await document_parser.parse_image_with_vision_model(image_base64)
                    except Exception as e:
                        parsed_content = f"图片解析失败: {str(e)}"

                # 如果是PDF，需要异步处理
                elif parsed_content.startswith("PDF_TO_PROCESS:"):
                    pdf_base64 = parsed_content.replace("PDF_TO_PROCESS:", "")
                    try:
                        import base64
                        pdf_content = base64.b64decode(pdf_base64)
                        parsed_content = await document_parser.parse_pdf_file(pdf_content)
                    except Exception as e:
                        parsed_content = f"PDF解析失败: {str(e)}"

                # 如果是包含图片的PPT，需要异步处理图片识别
                elif parsed_content.startswith("PPT_WITH_IMAGES:"):
                    try:
                        # 简化处理，暂时不支持PPT图片识别
                        parsed_content = "PPT文档已解析，但图片内容识别功能暂未启用"
                    except Exception as e:
                        parsed_content = f"PPT图片处理失败: {str(e)}"

                parsed_files.append({
                    "filename": file.filename,
                    "content_type": file.content_type,
                    "size": len(file_content),
                    "content": parsed_content,
                    "success": True
                })
                success_count += 1

            except Exception as e:
                logger.error(f"解析文件 {file.filename} 失败: {str(e)}")
                parsed_files.append({
                    "filename": file.filename,
                    "error": f"解析失败: {str(e)}",
                    "success": False
                })

        return {
            "code": 200,
            "message": "文件解析完成",
            "data": {
                "files_count": len(parsed_files),
                "success_count": success_count,
                "files": parsed_files
            }
        }

    except Exception as e:
        import traceback
        error_detail = traceback.format_exc()
        logger.error(f"文件上传处理失败: {str(e)}")
        logger.error(f"详细错误信息: {error_detail}")
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

@file_router.post("/chat-with-files")
async def chat_with_files(
    query: str = Form(...),
    file_contents: List[dict] = Form(...)
):
    """
    带文件的聊天API
    用户可以基于已解析的文件内容提问
    """
    try:
        # 构建包含文件内容的提示
        file_context = "\n\n".join([
            f"文件: {file_data['filename']}\n内容:\n{file_data['content']}"
            for file_data in file_contents
        ])
        
        # 构建完整的查询
        full_query = f"基于以下文件内容回答问题：\n\n{file_context}\n\n用户问题：{query}"
        
        # 这里应该调用AI服务，暂时返回简单回复
        answer = f"已收到您关于 {len(file_contents)} 个文件的问题：{query}\n\n基于文件内容的分析功能正在开发中，请稍后再试。"
        
        return {
            "code": 200,
            "message": "处理成功",
            "data": {
                "answer": answer,
                "files_processed": len(file_contents)
            }
        }

    except Exception as e:
        logger.error(f"文件聊天处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@file_router.post("/analyze-reports")
async def analyze_reports(request: dict):
    """
    分析OCR识别的内容，判断是否为报表及报表类型
    """
    try:
        # 获取请求数据
        ocr_results = request.get("ocr_results", [])

        if not ocr_results:
            raise HTTPException(status_code=400, detail="没有OCR识别结果")

        # 分析每个文件
        analysis_results = []

        for result in ocr_results:
            filename = result.get("filename", "未知文件")
            content = result.get("content", "")

            if not content:
                analysis_results.append({
                    "filename": filename,
                    "success": False,
                    "error": "没有识别到内容",
                    "prompt": "",
                    "ai_response": ""
                })
                continue

            # 生成分析提示词
            prompt = generate_report_analysis_prompt(filename, content)

            # 调用大模型分析
            ai_response = await call_deepseek_api(prompt)

            analysis_results.append({
                "filename": filename,
                "success": True,
                "prompt": prompt,
                "ai_response": ai_response,
                "original_content": content
            })

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "results": analysis_results,
                "total_files": len(ocr_results),
                "analyzed_files": len([r for r in analysis_results if r["success"]])
            }
        )

    except Exception as e:
        logger.error(f"报表分析失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"报表分析失败: {str(e)}"}
        )


def generate_report_analysis_prompt(filename: str, content: str) -> str:
    """
    生成报表分析的提示词
    """
    prompt = f"""请直接分析以下OCR识别的文档内容，不要输出思考过程，只输出最终的结构化分析结果。

文件名：{filename}

OCR识别内容：
{content}

请严格按照以下JSON格式输出分析结果（不要添加任何解释或思考过程）：

{{
  "是否为报表": "是/否",
  "报表类型": "具体类型（如：资产负债表、利润表、现金流量表等）",
  "报表主体": "公司或机构名称",
  "报表时期": "时间范围（如：2024年第一季度、2024年6月等）",
  "关键财务数据": [
    "数据项1：具体数值",
    "数据项2：具体数值",
    "数据项3：具体数值"
  ],
  "置信度": "1-10分",
  "备注": "如有特殊情况或不确定因素请在此说明"
}}

要求：
1. 直接输出JSON格式，不要前缀说明
2. 如果不是报表，报表类型填"非报表"
3. 如果信息不明确，如实填写"信息不明确"
4. 关键财务数据最多提取5个最重要的数据项"""

    return prompt


async def call_deepseek_api(prompt: str) -> str:
    """
    调用金投大脑API进行分析
    """
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
        }

        data = {
            "model": DEEPSEEK_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 2000
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(DEEPSEEK_API_URL, headers=headers, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]
                    else:
                        return "AI模型返回格式异常"
                else:
                    error_text = await response.text()
                    logger.error(f"AI API调用失败: {response.status}, {error_text}")
                    return f"AI模型调用失败: HTTP {response.status}"

    except Exception as e:
        logger.error(f"调用AI模型时发生错误: {str(e)}")
        return f"AI模型调用异常: {str(e)}"


@file_router.post("/generate-final-report")
async def generate_final_report(request: dict):
    """
    根据用户选择的文件生成最终报表
    """
    try:
        # 获取请求数据
        selected_files = request.get("selected_files", [])

        if not selected_files:
            raise HTTPException(status_code=400, detail="没有选择文件")

        # 生成最终报表的提示词
        final_prompt = generate_final_report_prompt(selected_files)

        # 调用大模型生成最终报表
        final_report_content = await call_deepseek_api(final_prompt)

        # 提取报表类型统计
        report_types = []
        for file in selected_files:
            ai_response = file.get("ai_response", "")
            report_type = extract_report_type_from_response(ai_response)
            if report_type and report_type not in report_types:
                report_types.append(report_type)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "report": {
                    "content": final_report_content,
                    "report_types": report_types,
                    "file_count": len(selected_files),
                    "prompt": final_prompt
                }
            }
        )

    except Exception as e:
        logger.error(f"生成最终报表失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"生成最终报表失败: {str(e)}"}
        )


def generate_final_report_prompt(selected_files: List[Dict]) -> str:
    """
    生成最终报表的提示词
    """
    files_info = []
    for i, file in enumerate(selected_files, 1):
        filename = file.get("filename", f"文件{i}")
        ai_response = file.get("ai_response", "")
        original_content = file.get("original_content", "")

        files_info.append(f"""
文件 {i}：{filename}
AI分析结果：
{ai_response}

原始OCR内容：
{original_content[:1000]}{'...' if len(original_content) > 1000 else ''}
""")

    prompt = f"""你是一个专业的财务报表分析师。请基于以下 {len(selected_files)} 个已分析的报表文件，生成一份综合性的最终报表分析。

{'='*50}
文件分析结果：
{'='*50}
{''.join(files_info)}

请按照以下结构生成最终报表：

# 📊 综合报表分析报告

## 1. 报表概览
- 报表总数：{len(selected_files)} 份
- 报表类型分布
- 涉及主体汇总
- 时间范围覆盖

## 2. 主要发现
- 关键财务指标汇总
- 重要趋势分析
- 异常情况识别

## 3. 分类分析
### 3.1 按报表类型分析
（对不同类型的报表进行分类汇总）

### 3.2 按主体分析
（对不同主体的报表进行对比分析）

### 3.3 按时期分析
（对不同时期的数据进行趋势分析）

## 4. 风险提示
- 数据质量评估
- 潜在风险点
- 需要关注的事项

## 5. 建议与结论
- 基于分析的建议
- 总体结论

请确保分析客观、准确，基于实际的OCR识别内容和AI分析结果。如果某些信息不清晰或缺失，请如实说明。"""

    return prompt


def extract_report_type_from_response(ai_response: str) -> str:
    """
    从AI回答中提取报表类型
    """
    if not ai_response:
        return "未知类型"

    # 尝试匹配报表类型
    import re
    type_match = re.search(r'报表类型[：:]\s*([^\n]+)', ai_response, re.IGNORECASE)
    if type_match:
        return type_match.group(1).strip()

    # 如果没有找到，返回默认值
    return "未知类型"


@file_router.get("/report-templates")
async def get_report_templates():
    """
    获取可用的报表模板列表
    """
    try:
        # 获取backend-service目录路径
        backend_service_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        template_dir = os.path.join(backend_service_dir, "Report_Mode")
        templates = []

        if os.path.exists(template_dir):
            files = os.listdir(template_dir)
            for file in files:
                if file.endswith(('.xls', '.xlsx')):
                    templates.append({
                        "name": file,
                        "display_name": os.path.splitext(file)[0],
                        "path": os.path.join(template_dir, file)
                    })
        return {
            "success": True,
            "templates": templates
        }
    except Exception as e:
        logger.error(f"获取报表模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报表模板失败: {str(e)}")


def read_excel_template(template_path: str) -> Dict[str, Any]:
    """
    读取Excel模板内容
    """
    try:
        # 使用xlrd读取Excel文件
        workbook = xlrd.open_workbook(template_path)
        template_data = {}

        for sheet_name in workbook.sheet_names():
            sheet = workbook.sheet_by_name(sheet_name)
            sheet_data = []

            for row_idx in range(sheet.nrows):
                row_data = []
                for col_idx in range(sheet.ncols):
                    cell_value = sheet.cell_value(row_idx, col_idx)
                    row_data.append(str(cell_value) if cell_value else "")
                sheet_data.append(row_data)

            template_data[sheet_name] = sheet_data

        return template_data
    except Exception as e:
        logger.error(f"读取Excel模板失败: {str(e)}")
        raise Exception(f"读取Excel模板失败: {str(e)}")


def remove_privacy_info(content: str) -> str:
    """
    去除内容中的隐私信息（公司名称等）
    """
    try:
        # 常见的公司名称后缀
        company_suffixes = [
            r'有限公司', r'股份有限公司', r'集团有限公司', r'投资有限公司',
            r'科技有限公司', r'贸易有限公司', r'实业有限公司', r'控股有限公司',
            r'Ltd\.?', r'Co\.?', r'Inc\.?', r'Corp\.?', r'Group'
        ]

        # 替换公司名称
        for suffix in company_suffixes:
            pattern = r'[\u4e00-\u9fa5a-zA-Z0-9]+' + suffix
            content = re.sub(pattern, '[公司名称]', content, flags=re.IGNORECASE)

        # 替换可能的个人姓名（2-4个中文字符）
        content = re.sub(r'(?<![a-zA-Z\u4e00-\u9fa5])[\u4e00-\u9fa5]{2,4}(?![a-zA-Z\u4e00-\u9fa5])', '[姓名]', content)

        # 替换身份证号
        content = re.sub(r'\d{15}|\d{18}', '[身份证号]', content)

        # 替换电话号码
        content = re.sub(r'1[3-9]\d{9}', '[电话号码]', content)

        return content
    except Exception as e:
        logger.error(f"去除隐私信息失败: {str(e)}")
        return content


def generate_report_generation_prompt(template_data: Dict[str, Any], analysis_results: List[Dict[str, Any]]) -> str:
    """
    生成报表生成的提示词
    """
    prompt = f"""你是一个专业的财务报表分析师，请根据以下信息生成标准化的财务报表。

## 报表模板结构：
{json.dumps(template_data, ensure_ascii=False, indent=2)}

## 分析的文件数据：
"""

    for i, result in enumerate(analysis_results, 1):
        # 去除隐私信息
        cleaned_content = remove_privacy_info(result.get('content', ''))

        prompt += f"""
### 文件 {i}: {result.get('filename', '未知文件')}
**分析结果：**
- 是否为报表：{result.get('parsed_result', {}).get('是否为报表', '未知')}
- 报表类型：{result.get('parsed_result', {}).get('报表类型', '未知')}
- 报表主体：{result.get('parsed_result', {}).get('报表主体', '未知')}
- 报表时期：{result.get('parsed_result', {}).get('报表时期', '未知')}
- 置信度：{result.get('parsed_result', {}).get('置信度', '未知')}

**文件内容（已去除隐私信息）：**
{cleaned_content[:2000]}...

"""

    prompt += f"""
## 任务要求：
1. 请严格按照提供的报表模板结构生成财务报表
2. 从分析的文件数据中提取相关的财务数据
3. 确保数据的准确性和一致性
4. 如果某些数据缺失，请标注为"数据缺失"或"待补充"
5. 生成的报表应该是完整的、可用的Excel格式数据

## 输出格式：
请以JSON格式输出，包含以下结构：
{{
  "report_title": "报表标题",
  "sheets": {{
    "工作表名称": [
      ["行1列1", "行1列2", "行1列3", ...],
      ["行2列1", "行2列2", "行2列3", ...],
      ...
    ]
  }},
  "summary": "报表生成总结",
  "data_sources": ["数据来源文件列表"],
  "notes": "备注说明"
}}

请开始生成报表："""

    return prompt


def split_long_prompt(prompt: str, max_length: int = 15000) -> List[str]:
    """
    将长提示词分割成多个部分
    """
    if len(prompt) <= max_length:
        return [prompt]

    # 找到合适的分割点（优先在段落或句子结束处分割）
    parts = []
    current_pos = 0

    while current_pos < len(prompt):
        end_pos = current_pos + max_length

        if end_pos >= len(prompt):
            # 最后一部分
            parts.append(prompt[current_pos:])
            break

        # 寻找合适的分割点
        split_pos = end_pos

        # 优先在段落结束处分割
        for i in range(end_pos, current_pos, -1):
            if prompt[i:i+2] == '\n\n':
                split_pos = i + 2
                break
        else:
            # 其次在句子结束处分割
            for i in range(end_pos, current_pos, -1):
                if prompt[i] in '。！？\n':
                    split_pos = i + 1
                    break

        parts.append(prompt[current_pos:split_pos])
        current_pos = split_pos

    return parts


async def call_qwen_api(prompt: str) -> str:
    """
    调用千问API生成报表，支持长文本分割处理
    """
    try:
        logger.info(f"开始调用千问API，提示词长度: {len(prompt)} 字符")

        # 检查API Key配置
        if not DASHSCOPE_API_KEY:
            raise Exception("DASHSCOPE_API_KEY 未配置，请检查.env文件")

        max_prompt_length = 15000  # 单次请求的最大长度

        if len(prompt) <= max_prompt_length:
            # 短文本直接处理
            return await call_qwen_api_single(prompt)
        else:
            # 长文本分割处理
            logger.info(f"提示词过长({len(prompt)}字符)，将分割为多个部分处理")
            return await call_qwen_api_split(prompt, max_prompt_length)

    except Exception as e:
        error_msg = f"调用千问API失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


async def call_qwen_api_single(prompt: str) -> str:
    """
    单次调用千问API
    """
    messages = [
        {"role": "system", "content": "你是一个专业的财务报表分析师，擅长根据OCR识别的文档内容生成标准化的财务报表。请直接生成表格数据，不要包含思考过程。"},
        {"role": "user", "content": prompt}
    ]

    logger.info("正在调用千问API...")
    responses = dashscope.Generation.call(
        model=DASHSCOPE_MODEL,
        api_key=DASHSCOPE_API_KEY,
        messages=messages,
        stream=False,
        result_format='message',
        top_p=0.8,
        temperature=0.3,
        max_tokens=4000,
        enable_search=False
    )

    logger.info(f"千问API响应状态: {responses.status_code}")

    if responses.status_code == HTTPStatus.OK:
        if hasattr(responses, 'output') and hasattr(responses.output, 'choices') and len(responses.output.choices) > 0:
            choice = responses.output.choices[0]
            if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                content = choice.message.content
                logger.info(f"千问API返回内容长度: {len(content)} 字符")
                return content

    # 处理API错误
    error_msg = f"API调用失败: 状态码 {responses.status_code}"
    if hasattr(responses, 'message'):
        error_msg += f", 错误信息: {responses.message}"
    if hasattr(responses, 'code'):
        error_msg += f", 错误代码: {responses.code}"

    logger.error(error_msg)
    raise Exception(error_msg)


async def call_qwen_api_split(prompt: str, max_length: int) -> str:
    """
    分割长文本并多次调用千问API
    """
    # 分割提示词
    prompt_parts = split_long_prompt(prompt, max_length)
    logger.info(f"提示词已分割为 {len(prompt_parts)} 个部分")

    all_responses = []

    for i, part in enumerate(prompt_parts):
        logger.info(f"处理第 {i+1}/{len(prompt_parts)} 部分，长度: {len(part)} 字符")

        # 为每个部分添加上下文说明
        if i == 0:
            # 第一部分
            context_prompt = f"""这是一个长文档的第 {i+1} 部分（共 {len(prompt_parts)} 部分）。
请分析这部分内容，并生成对应的财务报表数据。

{part}

注意：这只是完整文档的一部分，请根据这部分内容生成相应的报表数据。"""
        elif i == len(prompt_parts) - 1:
            # 最后一部分
            context_prompt = f"""这是一个长文档的第 {i+1} 部分（共 {len(prompt_parts)} 部分，最后一部分）。
请分析这部分内容，并生成对应的财务报表数据。

{part}

注意：这是文档的最后一部分，请根据这部分内容生成相应的报表数据，并尽可能完善整体报表结构。"""
        else:
            # 中间部分
            context_prompt = f"""这是一个长文档的第 {i+1} 部分（共 {len(prompt_parts)} 部分）。
请分析这部分内容，并生成对应的财务报表数据。

{part}

注意：这是文档的中间部分，请根据这部分内容生成相应的报表数据。"""

        try:
            response = await call_qwen_api_single(context_prompt)
            all_responses.append(f"=== 第 {i+1} 部分分析结果 ===\n{response}")

            # 添加延迟避免API限流
            if i < len(prompt_parts) - 1:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"处理第 {i+1} 部分时出错: {str(e)}")
            all_responses.append(f"=== 第 {i+1} 部分处理失败 ===\n错误: {str(e)}")

    # 合并所有响应
    final_response = "\n\n".join(all_responses)

    # 添加汇总说明
    summary = f"""
=== 分割处理汇总 ===
原始提示词长度: {len(prompt)} 字符
分割为: {len(prompt_parts)} 个部分
处理完成的部分: {len([r for r in all_responses if '处理失败' not in r])} 个

以上是对长文档分割处理的结果，每个部分都进行了独立分析。
"""

    final_response = summary + "\n\n" + final_response
    logger.info(f"分割处理完成，最终响应长度: {len(final_response)} 字符")

    return final_response


@file_router.post("/generate-prompt")
async def generate_prompt(request: GenerateReportRequest):
    """
    第一步：生成提示词并返回给前端显示
    """
    try:
        logger.info(f"开始生成提示词，模板: {request.template_name}, 文件数量: {len(request.selected_files)}")

        # 1. 读取报表模板
        backend_service_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        template_dir = os.path.join(backend_service_dir, "Report_Mode")
        template_path = os.path.join(template_dir, request.template_name)

        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_name}")

        template_data = read_excel_template(template_path)

        # 2. 筛选选中的文件分析结果
        selected_results = [
            result for result in request.analysis_results
            if result.get('filename') in request.selected_files
        ]

        if not selected_results:
            raise HTTPException(status_code=400, detail="没有找到选中文件的分析结果")

        # 3. 生成提示词
        prompt = generate_report_generation_prompt(template_data, selected_results)

        return {
            "success": True,
            "prompt": prompt,
            "template_name": request.template_name,
            "selected_files": request.selected_files,
            "template_data": template_data
        }

    except Exception as e:
        logger.error(f"生成提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成提示词失败: {str(e)}")


class QwenRequest(BaseModel):
    prompt: str
    template_name: str
    selected_files: List[str]


@file_router.post("/call-qwen-api")
async def call_qwen_api_endpoint(request: QwenRequest):
    """
    第二步：调用千问API生成报表
    """
    try:
        logger.info(f"开始调用千问API，模板: {request.template_name}")

        # 调用千问API
        ai_response = await call_qwen_api(request.prompt)

        return {
            "success": True,
            "ai_response": ai_response,
            "template_name": request.template_name,
            "selected_files": request.selected_files
        }

    except Exception as e:
        logger.error(f"调用千问API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"调用千问API失败: {str(e)}")


@file_router.post("/generate-report-with-template")
async def generate_report_with_template(request: GenerateReportRequest):
    """
    一步完成：生成提示词并调用千问API（保留兼容性）
    """
    try:
        logger.info(f"开始生成报表，模板: {request.template_name}, 文件数量: {len(request.selected_files)}")

        # 1. 读取报表模板
        backend_service_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        template_dir = os.path.join(backend_service_dir, "Report_Mode")
        template_path = os.path.join(template_dir, request.template_name)

        if not os.path.exists(template_path):
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_name}")

        template_data = read_excel_template(template_path)

        # 2. 筛选选中的文件分析结果
        selected_results = [
            result for result in request.analysis_results
            if result.get('filename') in request.selected_files
        ]

        if not selected_results:
            raise HTTPException(status_code=400, detail="没有找到选中文件的分析结果")

        # 3. 生成提示词
        prompt = generate_report_generation_prompt(template_data, selected_results)

        # 4. 调用千问API生成报表
        ai_response = await call_qwen_api(prompt)

        return {
            "success": True,
            "prompt": prompt,
            "ai_response": ai_response,
            "template_name": request.template_name,
            "selected_files": request.selected_files,
            "template_data": template_data
        }

    except Exception as e:
        logger.error(f"生成报表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成报表失败: {str(e)}")
