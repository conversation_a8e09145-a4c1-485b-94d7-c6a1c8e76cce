/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
!function webpackUniversalModuleDefinition(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],e):"object"==typeof exports?exports["pdfjs-dist/build/pdf"]=e():t["pdfjs-dist/build/pdf"]=t.pdfjsLib=e()}(globalThis,(()=>(()=>{var __webpack_modules__=[,(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.VerbosityLevel=e.Util=e.UnknownErrorException=e.UnexpectedResponseException=e.UNSUPPORTED_FEATURES=e.TextRenderingMode=e.RenderingIntentFlag=e.PermissionFlag=e.PasswordResponses=e.PasswordException=e.PageActionEventType=e.OPS=e.MissingPDFException=e.LINE_FACTOR=e.LINE_DESCENT_FACTOR=e.InvalidPDFException=e.ImageKind=e.IDENTITY_MATRIX=e.FormatError=e.FeatureTest=e.FONT_IDENTITY_MATRIX=e.DocumentActionEventType=e.CMapCompressionType=e.BaseException=e.BASELINE_FACTOR=e.AnnotationType=e.AnnotationStateModelType=e.AnnotationReviewState=e.AnnotationReplyType=e.AnnotationMode=e.AnnotationMarkedState=e.AnnotationFlag=e.AnnotationFieldFlag=e.AnnotationEditorType=e.AnnotationEditorPrefix=e.AnnotationEditorParamsType=e.AnnotationBorderStyleType=e.AnnotationActionEventType=e.AbortException=void 0;e.assert=function assert(t,e){t||unreachable(e)};e.bytesToString=function bytesToString(t){"object"==typeof t&&null!==t&&void 0!==t.length||unreachable("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const r=[];for(let s=0;s<e;s+=i){const a=Math.min(s+i,e),n=t.subarray(s,a);r.push(String.fromCharCode.apply(null,n))}return r.join("")};e.createPromiseCapability=function createPromiseCapability(){const t=Object.create(null);let e=!1;Object.defineProperty(t,"settled",{get:()=>e});t.promise=new Promise((function(i,r){t.resolve=function(t){e=!0;i(t)};t.reject=function(t){e=!0;r(t)}}));return t};e.createValidAbsoluteUrl=function createValidAbsoluteUrl(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!t)return null;try{if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e&&e.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=stringToUTF8String(t)}catch(t){}}const r=e?new URL(t,e):new URL(t);if(function _isValidProtocol(t){if(!t)return!1;switch(t.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r))return r}catch(t){}return null};e.getModificationDate=function getModificationDate(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return[t.getUTCFullYear().toString(),(t.getUTCMonth()+1).toString().padStart(2,"0"),t.getUTCDate().toString().padStart(2,"0"),t.getUTCHours().toString().padStart(2,"0"),t.getUTCMinutes().toString().padStart(2,"0"),t.getUTCSeconds().toString().padStart(2,"0")].join("")};e.getVerbosityLevel=function getVerbosityLevel(){return s};e.info=function info(t){s>=r.INFOS&&console.log(`Info: ${t}`)};e.isArrayBuffer=function isArrayBuffer(t){return"object"==typeof t&&null!==t&&void 0!==t.byteLength};e.isArrayEqual=function isArrayEqual(t,e){if(t.length!==e.length)return!1;for(let i=0,r=t.length;i<r;i++)if(t[i]!==e[i])return!1;return!0};e.objectFromMap=function objectFromMap(t){const e=Object.create(null);for(const[i,r]of t)e[i]=r;return e};e.objectSize=function objectSize(t){return Object.keys(t).length};e.setVerbosityLevel=function setVerbosityLevel(t){Number.isInteger(t)&&(s=t)};e.shadow=shadow;e.string32=function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)};e.stringToBytes=stringToBytes;e.stringToPDFString=function stringToPDFString(t){if(t[0]>="ï"){let e;"þ"===t[0]&&"ÿ"===t[1]?e="utf-16be":"ÿ"===t[0]&&"þ"===t[1]?e="utf-16le":"ï"===t[0]&&"»"===t[1]&&"¿"===t[2]&&(e="utf-8");if(e)try{const i=new TextDecoder(e,{fatal:!0}),r=stringToBytes(t);return i.decode(r)}catch(t){warn(`stringToPDFString: "${t}".`)}}const e=[];for(let i=0,r=t.length;i<r;i++){const r=o[t.charCodeAt(i)];e.push(r?String.fromCharCode(r):t.charAt(i))}return e.join("")};e.stringToUTF8String=stringToUTF8String;e.unreachable=unreachable;e.utf8StringToString=function utf8StringToString(t){return unescape(encodeURIComponent(t))};e.warn=warn;if(!globalThis._pdfjsCompatibilityChecked){globalThis._pdfjsCompatibilityChecked=!0;i(2)}e.IDENTITY_MATRIX=[1,0,0,1,0,0];e.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];e.LINE_FACTOR=1.35;e.LINE_DESCENT_FACTOR=.35;e.BASELINE_FACTOR=.25925925925925924;e.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};e.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};e.AnnotationEditorPrefix="pdfjs_internal_editor_";e.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,INK:15};e.AnnotationEditorParamsType={FREETEXT_SIZE:1,FREETEXT_COLOR:2,FREETEXT_OPACITY:3,INK_COLOR:11,INK_THICKNESS:12,INK_OPACITY:13};e.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};e.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};e.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};e.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};e.AnnotationStateModelType={MARKED:"Marked",REVIEW:"Review"};e.AnnotationMarkedState={MARKED:"Marked",UNMARKED:"Unmarked"};e.AnnotationReviewState={ACCEPTED:"Accepted",REJECTED:"Rejected",CANCELLED:"Cancelled",COMPLETED:"Completed",NONE:"None"};e.AnnotationReplyType={GROUP:"Group",REPLY:"R"};e.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};e.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};e.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};e.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};e.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};e.PageActionEventType={O:"PageOpen",C:"PageClose"};const r={ERRORS:0,WARNINGS:1,INFOS:5};e.VerbosityLevel=r;e.CMapCompressionType={NONE:0,BINARY:1};e.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};e.UNSUPPORTED_FEATURES={forms:"forms",javaScript:"javaScript",signatures:"signatures",smask:"smask",shadingPattern:"shadingPattern",errorTilingPattern:"errorTilingPattern",errorExtGState:"errorExtGState",errorXObject:"errorXObject",errorFontLoadType3:"errorFontLoadType3",errorFontState:"errorFontState",errorFontMissing:"errorFontMissing",errorFontTranslate:"errorFontTranslate",errorColorSpace:"errorColorSpace",errorOperatorList:"errorOperatorList",errorFontToUnicode:"errorFontToUnicode",errorFontLoadNative:"errorFontLoadNative",errorFontBuildPath:"errorFontBuildPath",errorFontGetPath:"errorFontGetPath",errorMarkedContent:"errorMarkedContent",errorContentSubStream:"errorContentSubStream"};e.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let s=r.WARNINGS;function warn(t){s>=r.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function shadow(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];Object.defineProperty(t,e,{value:i,enumerable:!r,configurable:!0,writable:!1});return i}const a=function BaseExceptionClosure(){function BaseException(t,e){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();e.BaseException=a;e.PasswordException=class PasswordException extends a{constructor(t,e){super(t,"PasswordException");this.code=e}};e.UnknownErrorException=class UnknownErrorException extends a{constructor(t,e){super(t,"UnknownErrorException");this.details=e}};e.InvalidPDFException=class InvalidPDFException extends a{constructor(t){super(t,"InvalidPDFException")}};e.MissingPDFException=class MissingPDFException extends a{constructor(t){super(t,"MissingPDFException")}};e.UnexpectedResponseException=class UnexpectedResponseException extends a{constructor(t,e){super(t,"UnexpectedResponseException");this.status=e}};e.FormatError=class FormatError extends a{constructor(t){super(t,"FormatError")}};e.AbortException=class AbortException extends a{constructor(t){super(t,"AbortException")}};function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let r=0;r<e;++r)i[r]=255&t.charCodeAt(r);return i}e.FeatureTest=class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch(t){return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"==typeof navigator?shadow(this,"platform",{isWin:!1,isMac:!1}):shadow(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}};const n=[...Array(256).keys()].map((t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,i){return`#${n[t]}${n[e]}${n[i]}`}static scaleMinMax(t,e){let i;if(t[0]){if(t[0]<0){i=e[0];e[0]=e[1];e[1]=i}e[0]*=t[0];e[1]*=t[0];if(t[3]<0){i=e[2];e[2]=e[3];e[3]=i}e[2]*=t[3];e[3]*=t[3]}else{i=e[0];e[0]=e[2];e[2]=i;i=e[1];e[1]=e[3];e[3]=i;if(t[1]<0){i=e[2];e[2]=e[3];e[3]=i}e[2]*=t[1];e[3]*=t[1];if(t[2]<0){i=e[0];e[0]=e[1];e[1]=i}e[0]*=t[2];e[1]*=t[2]}e[0]+=t[4];e[1]+=t[4];e[2]+=t[5];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const i=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i]}static getAxialAlignedBoundingBox(t,e){const i=Util.applyTransform(t,e),r=Util.applyTransform(t.slice(2,4),e),s=Util.applyTransform([t[0],t[3]],e),a=Util.applyTransform([t[2],t[1]],e);return[Math.min(i[0],r[0],s[0],a[0]),Math.min(i[1],r[1],s[1],a[1]),Math.max(i[0],r[0],s[0],a[0]),Math.max(i[1],r[1],s[1],a[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],r=t[0]*e[1]+t[1]*e[3],s=t[2]*e[0]+t[3]*e[2],a=t[2]*e[1]+t[3]*e[3],n=(i+a)/2,o=Math.sqrt((i+a)**2-4*(i*a-s*r))/2,l=n+o||1,c=n-o||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),r=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>r)return null;const s=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return s>a?null:[i,s,r,a]}static bezierBoundingBox(t,e,i,r,s,a,n,o){const l=[],c=[[],[]];let d,h,u,p,f,g,m,v;for(let c=0;c<2;++c){if(0===c){h=6*t-12*i+6*s;d=-3*t+9*i-9*s+3*n;u=3*i-3*t}else{h=6*e-12*r+6*a;d=-3*e+9*r-9*a+3*o;u=3*r-3*e}if(Math.abs(d)<1e-12){if(Math.abs(h)<1e-12)continue;p=-u/h;0<p&&p<1&&l.push(p)}else{m=h*h-4*u*d;v=Math.sqrt(m);if(!(m<0)){f=(-h+v)/(2*d);0<f&&f<1&&l.push(f);g=(-h-v)/(2*d);0<g&&g<1&&l.push(g)}}}let _,b=l.length;const y=b;for(;b--;){p=l[b];_=1-p;c[0][b]=_*_*_*t+3*_*_*p*i+3*_*p*p*s+p*p*p*n;c[1][b]=_*_*_*e+3*_*_*p*r+3*_*p*p*a+p*p*p*o}c[0][y]=t;c[1][y]=e;c[0][y+1]=n;c[1][y+1]=o;c[0].length=c[1].length=y+2;return[Math.min(...c[0]),Math.min(...c[1]),Math.max(...c[0]),Math.max(...c[1])]}}e.Util=Util;const o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function stringToUTF8String(t){return decodeURIComponent(escape(t))}},(t,e,i)=>{"use strict";var r=i(3);!function checkNodeBtoa(){!globalThis.btoa&&r.isNodeJS&&(globalThis.btoa=function(t){return Buffer.from(t,"binary").toString("base64")})}();!function checkNodeAtob(){!globalThis.atob&&r.isNodeJS&&(globalThis.atob=function(t){return Buffer.from(t,"base64").toString("binary")})}();!function checkDOMMatrix(){!globalThis.DOMMatrix&&r.isNodeJS&&(globalThis.DOMMatrix=require("canvas").DOMMatrix)}();!function checkPath2D(){if(globalThis.Path2D||!r.isNodeJS)return;const{CanvasRenderingContext2D:t}=require("canvas"),{polyfillPath2D:e}=require("path2d-polyfill");globalThis.CanvasRenderingContext2D=t;e(globalThis)}();!function checkReadableStream(){!globalThis.ReadableStream&&r.isNodeJS&&(globalThis.ReadableStream=require("web-streams-polyfill/dist/ponyfill.js").ReadableStream)}();!function checkArrayAt(){Array.prototype.at||i(4)}();!function checkTypedArrayAt(){Uint8Array.prototype.at||i(78)}();!function checkStructuredClone(){globalThis.structuredClone||i(88)}()},(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.isNodeJS=void 0;const i=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);e.isNodeJS=i},(t,e,i)=>{i(5);var r=i(77);t.exports=r("Array","at")},(t,e,i)=>{"use strict";var r=i(6),s=i(43),a=i(67),n=i(65),o=i(72);r({target:"Array",proto:!0},{at:function at(t){var e=s(this),i=a(e),r=n(t),o=r>=0?r:i+r;return o<0||o>=i?void 0:e[o]}});o("at")},(t,e,i)=>{var r=i(7),s=i(8).f,a=i(47),n=i(51),o=i(41),l=i(59),c=i(71);t.exports=function(t,e){var i,d,h,u,p,f=t.target,g=t.global,m=t.stat;if(i=g?r:m?r[f]||o(f,{}):(r[f]||{}).prototype)for(d in e){u=e[d];h=t.dontCallGetSet?(p=s(i,d))&&p.value:i[d];if(!c(g?d:f+(m?".":"#")+d,t.forced)&&void 0!==h){if(typeof u==typeof h)continue;l(u,h)}(t.sham||h&&h.sham)&&a(u,"sham",!0);n(i,d,u,t)}}},t=>{var check=function(t){return t&&t.Math==Math&&t};t.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||function(){return this}()||Function("return this")()},(t,e,i)=>{var r=i(9),s=i(11),a=i(13),n=i(14),o=i(15),l=i(21),c=i(42),d=i(45),h=Object.getOwnPropertyDescriptor;e.f=r?h:function getOwnPropertyDescriptor(t,e){t=o(t);e=l(e);if(d)try{return h(t,e)}catch(t){}if(c(t,e))return n(!s(a.f,t,e),t[e])}},(t,e,i)=>{var r=i(10);t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},(t,e,i)=>{var r=i(12),s=Function.prototype.call;t.exports=r?s.bind(s):function(){return s.apply(s,arguments)}},(t,e,i)=>{var r=i(10);t.exports=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},(t,e)=>{"use strict";var i={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,s=r&&!i.call({1:2},1);e.f=s?function propertyIsEnumerable(t){var e=r(this,t);return!!e&&e.enumerable}:i},t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},(t,e,i)=>{var r=i(16),s=i(19);t.exports=function(t){return r(s(t))}},(t,e,i)=>{var r=i(17),s=i(10),a=i(18),n=Object,o=r("".split);t.exports=s((function(){return!n("z").propertyIsEnumerable(0)}))?function(t){return"String"==a(t)?o(t,""):n(t)}:n},(t,e,i)=>{var r=i(12),s=Function.prototype,a=s.call,n=r&&s.bind.bind(a,a);t.exports=r?n:function(t){return function(){return a.apply(t,arguments)}}},(t,e,i)=>{var r=i(17),s=r({}.toString),a=r("".slice);t.exports=function(t){return a(s(t),8,-1)}},(t,e,i)=>{var r=i(20),s=TypeError;t.exports=function(t){if(r(t))throw s("Can't call method on "+t);return t}},t=>{t.exports=function(t){return null==t}},(t,e,i)=>{var r=i(22),s=i(26);t.exports=function(t){var e=r(t,"string");return s(e)?e:e+""}},(t,e,i)=>{var r=i(11),s=i(23),a=i(26),n=i(33),o=i(36),l=i(37),c=TypeError,d=l("toPrimitive");t.exports=function(t,e){if(!s(t)||a(t))return t;var i,l=n(t,d);if(l){void 0===e&&(e="default");i=r(l,t,e);if(!s(i)||a(i))return i;throw c("Can't convert object to primitive value")}void 0===e&&(e="number");return o(t,e)}},(t,e,i)=>{var r=i(24),s=i(25),a=s.all;t.exports=s.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:r(t)||t===a}:function(t){return"object"==typeof t?null!==t:r(t)}},(t,e,i)=>{var r=i(25),s=r.all;t.exports=r.IS_HTMLDDA?function(t){return"function"==typeof t||t===s}:function(t){return"function"==typeof t}},t=>{var e="object"==typeof document&&document.all,i=void 0===e&&void 0!==e;t.exports={all:e,IS_HTMLDDA:i}},(t,e,i)=>{var r=i(27),s=i(24),a=i(28),n=i(29),o=Object;t.exports=n?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return s(e)&&a(e.prototype,o(t))}},(t,e,i)=>{var r=i(7),s=i(24),aFunction=function(t){return s(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?aFunction(r[t]):r[t]&&r[t][e]}},(t,e,i)=>{var r=i(17);t.exports=r({}.isPrototypeOf)},(t,e,i)=>{var r=i(30);t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},(t,e,i)=>{var r=i(31),s=i(10);t.exports=!!Object.getOwnPropertySymbols&&!s((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},(t,e,i)=>{var r,s,a=i(7),n=i(32),o=a.process,l=a.Deno,c=o&&o.versions||l&&l.version,d=c&&c.v8;d&&(s=(r=d.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1]));!s&&n&&(!(r=n.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=n.match(/Chrome\/(\d+)/))&&(s=+r[1]);t.exports=s},t=>{t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},(t,e,i)=>{var r=i(34),s=i(20);t.exports=function(t,e){var i=t[e];return s(i)?void 0:r(i)}},(t,e,i)=>{var r=i(24),s=i(35),a=TypeError;t.exports=function(t){if(r(t))return t;throw a(s(t)+" is not a function")}},t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},(t,e,i)=>{var r=i(11),s=i(24),a=i(23),n=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&s(i=t.toString)&&!a(o=r(i,t)))return o;if(s(i=t.valueOf)&&!a(o=r(i,t)))return o;if("string"!==e&&s(i=t.toString)&&!a(o=r(i,t)))return o;throw n("Can't convert object to primitive value")}},(t,e,i)=>{var r=i(7),s=i(38),a=i(42),n=i(44),o=i(30),l=i(29),c=r.Symbol,d=s("wks"),h=l?c.for||c:c&&c.withoutSetter||n;t.exports=function(t){a(d,t)||(d[t]=o&&a(c,t)?c[t]:h("Symbol."+t));return d[t]}},(t,e,i)=>{var r=i(39),s=i(40);(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.27.2",mode:r?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.27.2/LICENSE",source:"https://github.com/zloirock/core-js"})},t=>{t.exports=!1},(t,e,i)=>{var r=i(7),s=i(41),a="__core-js_shared__",n=r[a]||s(a,{});t.exports=n},(t,e,i)=>{var r=i(7),s=Object.defineProperty;t.exports=function(t,e){try{s(r,t,{value:e,configurable:!0,writable:!0})}catch(i){r[t]=e}return e}},(t,e,i)=>{var r=i(17),s=i(43),a=r({}.hasOwnProperty);t.exports=Object.hasOwn||function hasOwn(t,e){return a(s(t),e)}},(t,e,i)=>{var r=i(19),s=Object;t.exports=function(t){return s(r(t))}},(t,e,i)=>{var r=i(17),s=0,a=Math.random(),n=r(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+n(++s+a,36)}},(t,e,i)=>{var r=i(9),s=i(10),a=i(46);t.exports=!r&&!s((function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},(t,e,i)=>{var r=i(7),s=i(23),a=r.document,n=s(a)&&s(a.createElement);t.exports=function(t){return n?a.createElement(t):{}}},(t,e,i)=>{var r=i(9),s=i(48),a=i(14);t.exports=r?function(t,e,i){return s.f(t,e,a(1,i))}:function(t,e,i){t[e]=i;return t}},(t,e,i)=>{var r=i(9),s=i(45),a=i(49),n=i(50),o=i(21),l=TypeError,c=Object.defineProperty,d=Object.getOwnPropertyDescriptor,h="enumerable",u="configurable",p="writable";e.f=r?a?function defineProperty(t,e,i){n(t);e=o(e);n(i);if("function"==typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var r=d(t,e);if(r&&r[p]){t[e]=i.value;i={configurable:u in i?i[u]:r[u],enumerable:h in i?i[h]:r[h],writable:!1}}}return c(t,e,i)}:c:function defineProperty(t,e,i){n(t);e=o(e);n(i);if(s)try{return c(t,e,i)}catch(t){}if("get"in i||"set"in i)throw l("Accessors not supported");"value"in i&&(t[e]=i.value);return t}},(t,e,i)=>{var r=i(9),s=i(10);t.exports=r&&s((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},(t,e,i)=>{var r=i(23),s=String,a=TypeError;t.exports=function(t){if(r(t))return t;throw a(s(t)+" is not an object")}},(t,e,i)=>{var r=i(24),s=i(48),a=i(52),n=i(41);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,c=void 0!==o.name?o.name:e;r(i)&&a(i,c,o);if(o.global)l?t[e]=i:n(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=i:s.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},(t,e,i)=>{var r=i(17),s=i(10),a=i(24),n=i(42),o=i(9),l=i(53).CONFIGURABLE,c=i(54),d=i(55),h=d.enforce,u=d.get,p=String,f=Object.defineProperty,g=r("".slice),m=r("".replace),v=r([].join),_=o&&!s((function(){return 8!==f((function(){}),"length",{value:8}).length})),b=String(String).split("String"),y=t.exports=function(t,e,i){"Symbol("===g(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\)/,"$1")+"]");i&&i.getter&&(e="get "+e);i&&i.setter&&(e="set "+e);(!n(t,"name")||l&&t.name!==e)&&(o?f(t,"name",{value:e,configurable:!0}):t.name=e);_&&i&&n(i,"arity")&&t.length!==i.arity&&f(t,"length",{value:i.arity});try{i&&n(i,"constructor")&&i.constructor?o&&f(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=h(t);n(r,"source")||(r.source=v(b,"string"==typeof e?e:""));return t};Function.prototype.toString=y((function toString(){return a(this)&&u(this).source||c(this)}),"toString")},(t,e,i)=>{var r=i(9),s=i(42),a=Function.prototype,n=r&&Object.getOwnPropertyDescriptor,o=s(a,"name"),l=o&&"something"===function something(){}.name,c=o&&(!r||r&&n(a,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:c}},(t,e,i)=>{var r=i(17),s=i(24),a=i(40),n=r(Function.toString);s(a.inspectSource)||(a.inspectSource=function(t){return n(t)});t.exports=a.inspectSource},(t,e,i)=>{var r,s,a,n=i(56),o=i(7),l=i(23),c=i(47),d=i(42),h=i(40),u=i(57),p=i(58),f="Object already initialized",g=o.TypeError,m=o.WeakMap;if(n||h.state){var v=h.state||(h.state=new m);v.get=v.get;v.has=v.has;v.set=v.set;r=function(t,e){if(v.has(t))throw g(f);e.facade=t;v.set(t,e);return e};s=function(t){return v.get(t)||{}};a=function(t){return v.has(t)}}else{var _=u("state");p[_]=!0;r=function(t,e){if(d(t,_))throw g(f);e.facade=t;c(t,_,e);return e};s=function(t){return d(t,_)?t[_]:{}};a=function(t){return d(t,_)}}t.exports={set:r,get:s,has:a,enforce:function(t){return a(t)?s(t):r(t,{})},getterFor:function(t){return function(e){var i;if(!l(e)||(i=s(e)).type!==t)throw g("Incompatible receiver, "+t+" required");return i}}}},(t,e,i)=>{var r=i(7),s=i(24),a=r.WeakMap;t.exports=s(a)&&/native code/.test(String(a))},(t,e,i)=>{var r=i(38),s=i(44),a=r("keys");t.exports=function(t){return a[t]||(a[t]=s(t))}},t=>{t.exports={}},(t,e,i)=>{var r=i(42),s=i(60),a=i(8),n=i(48);t.exports=function(t,e,i){for(var o=s(e),l=n.f,c=a.f,d=0;d<o.length;d++){var h=o[d];r(t,h)||i&&r(i,h)||l(t,h,c(e,h))}}},(t,e,i)=>{var r=i(27),s=i(17),a=i(61),n=i(70),o=i(50),l=s([].concat);t.exports=r("Reflect","ownKeys")||function ownKeys(t){var e=a.f(o(t)),i=n.f;return i?l(e,i(t)):e}},(t,e,i)=>{var r=i(62),s=i(69).concat("length","prototype");e.f=Object.getOwnPropertyNames||function getOwnPropertyNames(t){return r(t,s)}},(t,e,i)=>{var r=i(17),s=i(42),a=i(15),n=i(63).indexOf,o=i(58),l=r([].push);t.exports=function(t,e){var i,r=a(t),c=0,d=[];for(i in r)!s(o,i)&&s(r,i)&&l(d,i);for(;e.length>c;)s(r,i=e[c++])&&(~n(d,i)||l(d,i));return d}},(t,e,i)=>{var r=i(15),s=i(64),a=i(67),createMethod=function(t){return function(e,i,n){var o,l=r(e),c=a(l),d=s(n,c);if(t&&i!=i){for(;c>d;)if((o=l[d++])!=o)return!0}else for(;c>d;d++)if((t||d in l)&&l[d]===i)return t||d||0;return!t&&-1}};t.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},(t,e,i)=>{var r=i(65),s=Math.max,a=Math.min;t.exports=function(t,e){var i=r(t);return i<0?s(i+e,0):a(i,e)}},(t,e,i)=>{var r=i(66);t.exports=function(t){var e=+t;return e!=e||0===e?0:r(e)}},t=>{var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function trunc(t){var r=+t;return(r>0?i:e)(r)}},(t,e,i)=>{var r=i(68);t.exports=function(t){return r(t.length)}},(t,e,i)=>{var r=i(65),s=Math.min;t.exports=function(t){return t>0?s(r(t),9007199254740991):0}},t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},(t,e)=>{e.f=Object.getOwnPropertySymbols},(t,e,i)=>{var r=i(10),s=i(24),a=/#|\.prototype\./,isForced=function(t,e){var i=o[n(t)];return i==c||i!=l&&(s(e)?r(e):!!e)},n=isForced.normalize=function(t){return String(t).replace(a,".").toLowerCase()},o=isForced.data={},l=isForced.NATIVE="N",c=isForced.POLYFILL="P";t.exports=isForced},(t,e,i)=>{var r=i(37),s=i(73),a=i(48).f,n=r("unscopables"),o=Array.prototype;null==o[n]&&a(o,n,{configurable:!0,value:s(null)});t.exports=function(t){o[n][t]=!0}},(t,e,i)=>{var r,s=i(50),a=i(74),n=i(69),o=i(58),l=i(76),c=i(46),d=i(57),h="prototype",u="script",p=d("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<"+u+">"+t+"</"+u+">"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag(""));t.close();var e=t.parentWindow.Object;t=null;return e},NullProtoObject=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}NullProtoObject="undefined"!=typeof document?document.domain&&r?NullProtoObjectViaActiveX(r):function(){var t,e=c("iframe"),i="java"+u+":";e.style.display="none";l.appendChild(e);e.src=String(i);(t=e.contentWindow.document).open();t.write(scriptTag("document.F=Object"));t.close();return t.F}():NullProtoObjectViaActiveX(r);for(var t=n.length;t--;)delete NullProtoObject[h][n[t]];return NullProtoObject()};o[p]=!0;t.exports=Object.create||function create(t,e){var i;if(null!==t){EmptyConstructor[h]=s(t);i=new EmptyConstructor;EmptyConstructor[h]=null;i[p]=t}else i=NullProtoObject();return void 0===e?i:a.f(i,e)}},(t,e,i)=>{var r=i(9),s=i(49),a=i(48),n=i(50),o=i(15),l=i(75);e.f=r&&!s?Object.defineProperties:function defineProperties(t,e){n(t);for(var i,r=o(e),s=l(e),c=s.length,d=0;c>d;)a.f(t,i=s[d++],r[i]);return t}},(t,e,i)=>{var r=i(62),s=i(69);t.exports=Object.keys||function keys(t){return r(t,s)}},(t,e,i)=>{var r=i(27);t.exports=r("document","documentElement")},(t,e,i)=>{var r=i(7),s=i(17);t.exports=function(t,e){return s(r[t].prototype[e])}},(t,e,i)=>{i(79)},(t,e,i)=>{"use strict";var r=i(80),s=i(67),a=i(65),n=r.aTypedArray;(0,r.exportTypedArrayMethod)("at",(function at(t){var e=n(this),i=s(e),r=a(t),o=r>=0?r:i+r;return o<0||o>=i?void 0:e[o]}))},(t,e,i)=>{"use strict";var r,s,a,n=i(81),o=i(9),l=i(7),c=i(24),d=i(23),h=i(42),u=i(82),p=i(35),f=i(47),g=i(51),m=i(48).f,v=i(28),_=i(84),b=i(86),y=i(37),A=i(44),S=i(55),P=S.enforce,x=S.get,w=l.Int8Array,E=w&&w.prototype,C=l.Uint8ClampedArray,k=C&&C.prototype,M=w&&_(w),T=E&&_(E),R=Object.prototype,I=l.TypeError,D=y("toStringTag"),O=A("TYPED_ARRAY_TAG"),L="TypedArrayConstructor",G=n&&!!b&&"Opera"!==u(l.opera),N=!1,j={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},U={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(t){var e=_(t);if(d(e)){var i=x(e);return i&&h(i,L)?i[L]:getTypedArrayConstructor(e)}},isTypedArray=function(t){if(!d(t))return!1;var e=u(t);return h(j,e)||h(U,e)};for(r in j)(a=(s=l[r])&&s.prototype)?P(a)[L]=s:G=!1;for(r in U)(a=(s=l[r])&&s.prototype)&&(P(a)[L]=s);if(!G||!c(M)||M===Function.prototype){M=function TypedArray(){throw I("Incorrect invocation")};if(G)for(r in j)l[r]&&b(l[r],M)}if(!G||!T||T===R){T=M.prototype;if(G)for(r in j)l[r]&&b(l[r].prototype,T)}G&&_(k)!==T&&b(k,T);if(o&&!h(T,D)){N=!0;m(T,D,{get:function(){return d(this)?this[O]:void 0}});for(r in j)l[r]&&f(l[r],O,r)}t.exports={NATIVE_ARRAY_BUFFER_VIEWS:G,TYPED_ARRAY_TAG:N&&O,aTypedArray:function(t){if(isTypedArray(t))return t;throw I("Target is not a typed array")},aTypedArrayConstructor:function(t){if(c(t)&&(!b||v(M,t)))return t;throw I(p(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,i,r){if(o){if(i)for(var s in j){var a=l[s];if(a&&h(a.prototype,t))try{delete a.prototype[t]}catch(i){try{a.prototype[t]=e}catch(t){}}}T[t]&&!i||g(T,t,i?e:G&&E[t]||e,r)}},exportTypedArrayStaticMethod:function(t,e,i){var r,s;if(o){if(b){if(i)for(r in j)if((s=l[r])&&h(s,t))try{delete s[t]}catch(t){}if(M[t]&&!i)return;try{return g(M,t,i?e:G&&M[t]||e)}catch(t){}}for(r in j)!(s=l[r])||s[t]&&!i||g(s,t,e)}},getTypedArrayConstructor:getTypedArrayConstructor,isView:function isView(t){if(!d(t))return!1;var e=u(t);return"DataView"===e||h(j,e)||h(U,e)},isTypedArray:isTypedArray,TypedArray:M,TypedArrayPrototype:T}},t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},(t,e,i)=>{var r=i(83),s=i(24),a=i(18),n=i(37)("toStringTag"),o=Object,l="Arguments"==a(function(){return arguments}());t.exports=r?a:function(t){var e,i,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=o(t),n))?i:l?a(e):"Object"==(r=a(e))&&s(e.callee)?"Arguments":r}},(t,e,i)=>{var r={};r[i(37)("toStringTag")]="z";t.exports="[object z]"===String(r)},(t,e,i)=>{var r=i(42),s=i(24),a=i(43),n=i(57),o=i(85),l=n("IE_PROTO"),c=Object,d=c.prototype;t.exports=o?c.getPrototypeOf:function(t){var e=a(t);if(r(e,l))return e[l];var i=e.constructor;return s(i)&&e instanceof i?i.prototype:e instanceof c?d:null}},(t,e,i)=>{var r=i(10);t.exports=!r((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},(t,e,i)=>{var r=i(17),s=i(50),a=i(87);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(i,[]);e=i instanceof Array}catch(t){}return function setPrototypeOf(i,r){s(i);a(r);e?t(i,r):i.__proto__=r;return i}}():void 0)},(t,e,i)=>{var r=i(24),s=String,a=TypeError;t.exports=function(t){if("object"==typeof t||r(t))return t;throw a("Can't set "+s(t)+" as a prototype")}},(t,e,i)=>{i(89);i(96);i(98);i(121);i(123);var r=i(134);t.exports=r.structuredClone},(t,e,i)=>{"use strict";var r=i(15),s=i(72),a=i(90),n=i(55),o=i(48).f,l=i(91),c=i(95),d=i(39),h=i(9),u="Array Iterator",p=n.set,f=n.getterFor(u);t.exports=l(Array,"Array",(function(t,e){p(this,{type:u,target:r(t),index:0,kind:e})}),(function(){var t=f(this),e=t.target,i=t.kind,r=t.index++;if(!e||r>=e.length){t.target=void 0;return c(void 0,!0)}return c("keys"==i?r:"values"==i?e[r]:[r,e[r]],!1)}),"values");var g=a.Arguments=a.Array;s("keys");s("values");s("entries");if(!d&&h&&"values"!==g.name)try{o(g,"name",{value:"values"})}catch(t){}},t=>{t.exports={}},(t,e,i)=>{"use strict";var r=i(6),s=i(11),a=i(39),n=i(53),o=i(24),l=i(92),c=i(84),d=i(86),h=i(94),u=i(47),p=i(51),f=i(37),g=i(90),m=i(93),v=n.PROPER,_=n.CONFIGURABLE,b=m.IteratorPrototype,y=m.BUGGY_SAFARI_ITERATORS,A=f("iterator"),S="keys",P="values",x="entries",returnThis=function(){return this};t.exports=function(t,e,i,n,f,m,w){l(i,e,n);var E,C,k,getIterationMethod=function(t){if(t===f&&D)return D;if(!y&&t in R)return R[t];switch(t){case S:return function keys(){return new i(this,t)};case P:return function values(){return new i(this,t)};case x:return function entries(){return new i(this,t)}}return function(){return new i(this)}},M=e+" Iterator",T=!1,R=t.prototype,I=R[A]||R["@@iterator"]||f&&R[f],D=!y&&I||getIterationMethod(f),O="Array"==e&&R.entries||I;if(O&&(E=c(O.call(new t)))!==Object.prototype&&E.next){a||c(E)===b||(d?d(E,b):o(E[A])||p(E,A,returnThis));h(E,M,!0,!0);a&&(g[M]=returnThis)}if(v&&f==P&&I&&I.name!==P)if(!a&&_)u(R,"name",P);else{T=!0;D=function values(){return s(I,this)}}if(f){C={values:getIterationMethod(P),keys:m?D:getIterationMethod(S),entries:getIterationMethod(x)};if(w)for(k in C)(y||T||!(k in R))&&p(R,k,C[k]);else r({target:e,proto:!0,forced:y||T},C)}a&&!w||R[A]===D||p(R,A,D,{name:f});g[e]=D;return C}},(t,e,i)=>{"use strict";var r=i(93).IteratorPrototype,s=i(73),a=i(14),n=i(94),o=i(90),returnThis=function(){return this};t.exports=function(t,e,i,l){var c=e+" Iterator";t.prototype=s(r,{next:a(+!l,i)});n(t,c,!1,!0);o[c]=returnThis;return t}},(t,e,i)=>{"use strict";var r,s,a,n=i(10),o=i(24),l=i(23),c=i(73),d=i(84),h=i(51),u=i(37),p=i(39),f=u("iterator"),g=!1;[].keys&&("next"in(a=[].keys())?(s=d(d(a)))!==Object.prototype&&(r=s):g=!0);!l(r)||n((function(){var t={};return r[f].call(t)!==t}))?r={}:p&&(r=c(r));o(r[f])||h(r,f,(function(){return this}));t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:g}},(t,e,i)=>{var r=i(48).f,s=i(42),a=i(37)("toStringTag");t.exports=function(t,e,i){t&&!i&&(t=t.prototype);t&&!s(t,a)&&r(t,a,{configurable:!0,value:e})}},t=>{t.exports=function(t,e){return{value:t,done:e}}},(t,e,i)=>{var r=i(83),s=i(51),a=i(97);r||s(Object.prototype,"toString",a,{unsafe:!0})},(t,e,i)=>{"use strict";var r=i(83),s=i(82);t.exports=r?{}.toString:function toString(){return"[object "+s(this)+"]"}},(t,e,i)=>{i(99)},(t,e,i)=>{"use strict";i(100)("Map",(function(t){return function Map(){return t(this,arguments.length?arguments[0]:void 0)}}),i(118))},(t,e,i)=>{"use strict";var r=i(6),s=i(7),a=i(17),n=i(71),o=i(51),l=i(101),c=i(108),d=i(115),h=i(24),u=i(20),p=i(23),f=i(10),g=i(116),m=i(94),v=i(117);t.exports=function(t,e,i){var _=-1!==t.indexOf("Map"),b=-1!==t.indexOf("Weak"),y=_?"set":"add",A=s[t],S=A&&A.prototype,P=A,x={},fixMethod=function(t){var e=a(S[t]);o(S,t,"add"==t?function add(t){e(this,0===t?0:t);return this}:"delete"==t?function(t){return!(b&&!p(t))&&e(this,0===t?0:t)}:"get"==t?function get(t){return b&&!p(t)?void 0:e(this,0===t?0:t)}:"has"==t?function has(t){return!(b&&!p(t))&&e(this,0===t?0:t)}:function set(t,i){e(this,0===t?0:t,i);return this})};if(n(t,!h(A)||!(b||S.forEach&&!f((function(){(new A).entries().next()}))))){P=i.getConstructor(e,t,_,y);l.enable()}else if(n(t,!0)){var w=new P,E=w[y](b?{}:-0,1)!=w,C=f((function(){w.has(1)})),k=g((function(t){new A(t)})),M=!b&&f((function(){for(var t=new A,e=5;e--;)t[y](e,e);return!t.has(-0)}));if(!k){(P=e((function(t,e){d(t,S);var i=v(new A,t,P);u(e)||c(e,i[y],{that:i,AS_ENTRIES:_});return i}))).prototype=S;S.constructor=P}if(C||M){fixMethod("delete");fixMethod("has");_&&fixMethod("get")}(M||E)&&fixMethod(y);b&&S.clear&&delete S.clear}x[t]=P;r({global:!0,constructor:!0,forced:P!=A},x);m(P,t);b||i.setStrong(P,t,_);return P}},(t,e,i)=>{var r=i(6),s=i(17),a=i(58),n=i(23),o=i(42),l=i(48).f,c=i(61),d=i(102),h=i(105),u=i(44),p=i(107),f=!1,g=u("meta"),m=0,setMetadata=function(t){l(t,g,{value:{objectID:"O"+m++,weakData:{}}})},v=t.exports={enable:function(){v.enable=function(){};f=!0;var t=c.f,e=s([].splice),i={};i[g]=1;if(t(i).length){c.f=function(i){for(var r=t(i),s=0,a=r.length;s<a;s++)if(r[s]===g){e(r,s,1);break}return r};r({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:d.f})}},fastKey:function(t,e){if(!n(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,g)){if(!h(t))return"F";if(!e)return"E";setMetadata(t)}return t[g].objectID},getWeakData:function(t,e){if(!o(t,g)){if(!h(t))return!0;if(!e)return!1;setMetadata(t)}return t[g].weakData},onFreeze:function(t){p&&f&&h(t)&&!o(t,g)&&setMetadata(t);return t}};a[g]=!0},(t,e,i)=>{var r=i(18),s=i(15),a=i(61).f,n=i(103),o="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function getOwnPropertyNames(t){return o&&"Window"==r(t)?function(t){try{return a(t)}catch(t){return n(o)}}(t):a(s(t))}},(t,e,i)=>{var r=i(64),s=i(67),a=i(104),n=Array,o=Math.max;t.exports=function(t,e,i){for(var l=s(t),c=r(e,l),d=r(void 0===i?l:i,l),h=n(o(d-c,0)),u=0;c<d;c++,u++)a(h,u,t[c]);h.length=u;return h}},(t,e,i)=>{"use strict";var r=i(21),s=i(48),a=i(14);t.exports=function(t,e,i){var n=r(e);n in t?s.f(t,n,a(0,i)):t[n]=i}},(t,e,i)=>{var r=i(10),s=i(23),a=i(18),n=i(106),o=Object.isExtensible,l=r((function(){o(1)}));t.exports=l||n?function isExtensible(t){return!!s(t)&&((!n||"ArrayBuffer"!=a(t))&&(!o||o(t)))}:o},(t,e,i)=>{var r=i(10);t.exports=r((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))},(t,e,i)=>{var r=i(10);t.exports=!r((function(){return Object.isExtensible(Object.preventExtensions({}))}))},(t,e,i)=>{var r=i(109),s=i(11),a=i(50),n=i(35),o=i(111),l=i(67),c=i(28),d=i(112),h=i(113),u=i(114),p=TypeError,Result=function(t,e){this.stopped=t;this.result=e},f=Result.prototype;t.exports=function(t,e,i){var g,m,v,_,b,y,A,S=i&&i.that,P=!(!i||!i.AS_ENTRIES),x=!(!i||!i.IS_RECORD),w=!(!i||!i.IS_ITERATOR),E=!(!i||!i.INTERRUPTED),C=r(e,S),stop=function(t){g&&u(g,"normal",t);return new Result(!0,t)},callFn=function(t){if(P){a(t);return E?C(t[0],t[1],stop):C(t[0],t[1])}return E?C(t,stop):C(t)};if(x)g=t.iterator;else if(w)g=t;else{if(!(m=h(t)))throw p(n(t)+" is not iterable");if(o(m)){for(v=0,_=l(t);_>v;v++)if((b=callFn(t[v]))&&c(f,b))return b;return new Result(!1)}g=d(t,m)}y=x?t.next:g.next;for(;!(A=s(y,g)).done;){try{b=callFn(A.value)}catch(t){u(g,"throw",t)}if("object"==typeof b&&b&&c(f,b))return b}return new Result(!1)}},(t,e,i)=>{var r=i(110),s=i(34),a=i(12),n=r(r.bind);t.exports=function(t,e){s(t);return void 0===e?t:a?n(t,e):function(){return t.apply(e,arguments)}}},(t,e,i)=>{var r=i(18),s=i(17);t.exports=function(t){if("Function"===r(t))return s(t)}},(t,e,i)=>{var r=i(37),s=i(90),a=r("iterator"),n=Array.prototype;t.exports=function(t){return void 0!==t&&(s.Array===t||n[a]===t)}},(t,e,i)=>{var r=i(11),s=i(34),a=i(50),n=i(35),o=i(113),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(s(i))return a(r(i,t));throw l(n(t)+" is not iterable")}},(t,e,i)=>{var r=i(82),s=i(33),a=i(20),n=i(90),o=i(37)("iterator");t.exports=function(t){if(!a(t))return s(t,o)||s(t,"@@iterator")||n[r(t)]}},(t,e,i)=>{var r=i(11),s=i(50),a=i(33);t.exports=function(t,e,i){var n,o;s(t);try{if(!(n=a(t,"return"))){if("throw"===e)throw i;return i}n=r(n,t)}catch(t){o=!0;n=t}if("throw"===e)throw i;if(o)throw n;s(n);return i}},(t,e,i)=>{var r=i(28),s=TypeError;t.exports=function(t,e){if(r(e,t))return t;throw s("Incorrect invocation")}},(t,e,i)=>{var r=i(37)("iterator"),s=!1;try{var a=0,n={next:function(){return{done:!!a++}},return:function(){s=!0}};n[r]=function(){return this};Array.from(n,(function(){throw 2}))}catch(t){}t.exports=function(t,e){if(!e&&!s)return!1;var i=!1;try{var a={};a[r]=function(){return{next:function(){return{done:i=!0}}}};t(a)}catch(t){}return i}},(t,e,i)=>{var r=i(24),s=i(23),a=i(86);t.exports=function(t,e,i){var n,o;a&&r(n=e.constructor)&&n!==i&&s(o=n.prototype)&&o!==i.prototype&&a(t,o);return t}},(t,e,i)=>{"use strict";var r=i(48).f,s=i(73),a=i(119),n=i(109),o=i(115),l=i(20),c=i(108),d=i(91),h=i(95),u=i(120),p=i(9),f=i(101).fastKey,g=i(55),m=g.set,v=g.getterFor;t.exports={getConstructor:function(t,e,i,d){var h=t((function(t,r){o(t,u);m(t,{type:e,index:s(null),first:void 0,last:void 0,size:0});p||(t.size=0);l(r)||c(r,t[d],{that:t,AS_ENTRIES:i})})),u=h.prototype,g=v(e),define=function(t,e,i){var r,s,a=g(t),n=getEntry(t,e);if(n)n.value=i;else{a.last=n={index:s=f(e,!0),key:e,value:i,previous:r=a.last,next:void 0,removed:!1};a.first||(a.first=n);r&&(r.next=n);p?a.size++:t.size++;"F"!==s&&(a.index[s]=n)}return t},getEntry=function(t,e){var i,r=g(t),s=f(e);if("F"!==s)return r.index[s];for(i=r.first;i;i=i.next)if(i.key==e)return i};a(u,{clear:function clear(){for(var t=g(this),e=t.index,i=t.first;i;){i.removed=!0;i.previous&&(i.previous=i.previous.next=void 0);delete e[i.index];i=i.next}t.first=t.last=void 0;p?t.size=0:this.size=0},delete:function(t){var e=this,i=g(e),r=getEntry(e,t);if(r){var s=r.next,a=r.previous;delete i.index[r.index];r.removed=!0;a&&(a.next=s);s&&(s.previous=a);i.first==r&&(i.first=s);i.last==r&&(i.last=a);p?i.size--:e.size--}return!!r},forEach:function forEach(t){for(var e,i=g(this),r=n(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:i.first;){r(e.value,e.key,this);for(;e&&e.removed;)e=e.previous}},has:function has(t){return!!getEntry(this,t)}});a(u,i?{get:function get(t){var e=getEntry(this,t);return e&&e.value},set:function set(t,e){return define(this,0===t?0:t,e)}}:{add:function add(t){return define(this,t=0===t?0:t,t)}});p&&r(u,"size",{get:function(){return g(this).size}});return h},setStrong:function(t,e,i){var r=e+" Iterator",s=v(e),a=v(r);d(t,e,(function(t,e){m(this,{type:r,target:t,state:s(t),kind:e,last:void 0})}),(function(){for(var t=a(this),e=t.kind,i=t.last;i&&i.removed;)i=i.previous;if(!t.target||!(t.last=i=i?i.next:t.state.first)){t.target=void 0;return h(void 0,!0)}return h("keys"==e?i.key:"values"==e?i.value:[i.key,i.value],!1)}),i?"entries":"values",!i,!0);u(e)}}},(t,e,i)=>{var r=i(51);t.exports=function(t,e,i){for(var s in e)r(t,s,e[s],i);return t}},(t,e,i)=>{"use strict";var r=i(27),s=i(48),a=i(37),n=i(9),o=a("species");t.exports=function(t){var e=r(t),i=s.f;n&&e&&!e[o]&&i(e,o,{configurable:!0,get:function(){return this}})}},(t,e,i)=>{i(122)},(t,e,i)=>{"use strict";i(100)("Set",(function(t){return function Set(){return t(this,arguments.length?arguments[0]:void 0)}}),i(118))},(t,e,i)=>{var r,s=i(39),a=i(6),n=i(7),o=i(27),l=i(17),c=i(10),d=i(44),h=i(24),u=i(124),p=i(20),f=i(23),g=i(26),m=i(108),v=i(50),_=i(82),b=i(42),y=i(104),A=i(47),S=i(67),P=i(125),x=i(126),w=i(128),E=i(129),C=i(130),k=i(31),M=i(131),T=i(132),R=i(133),I=n.Object,D=n.Array,O=n.Date,L=n.Error,G=n.EvalError,N=n.RangeError,j=n.ReferenceError,U=n.SyntaxError,B=n.TypeError,W=n.URIError,q=n.PerformanceMark,z=n.WebAssembly,H=z&&z.CompileError||L,V=z&&z.LinkError||L,X=z&&z.RuntimeError||L,$=o("DOMException"),Y=w.Map,K=w.has,J=w.get,Q=w.set,Z=E.Set,tt=E.add,et=o("Object","keys"),it=l([].push),rt=l((!0).valueOf),st=l(1..valueOf),nt=l("".valueOf),ot=l(O.prototype.getTime),lt=d("structuredClone"),ct="DataCloneError",dt="Transferring",checkBasicSemantic=function(t){return!c((function(){var e=new n.Set([7]),i=t(e),r=t(I(7));return i==e||!i.has(7)||"object"!=typeof r||7!=r}))&&t},checkErrorsCloning=function(t,e){return!c((function(){var i=new e,r=t({a:i,b:i});return!(r&&r.a===r.b&&r.a instanceof e&&r.a.stack===i.stack)}))},ht=n.structuredClone,ut=s||!checkErrorsCloning(ht,L)||!checkErrorsCloning(ht,$)||!(r=ht,!c((function(){var t=r(new n.AggregateError([1],lt,{cause:3}));return"AggregateError"!=t.name||1!=t.errors[0]||t.message!=lt||3!=t.cause}))),pt=!ht&&checkBasicSemantic((function(t){return new q(lt,{detail:t}).detail})),ft=checkBasicSemantic(ht)||pt,throwUncloneable=function(t){throw new $("Uncloneable type: "+t,ct)},throwUnpolyfillable=function(t,e){throw new $((e||"Cloning")+" of "+t+" cannot be properly polyfilled in this engine",ct)},structuredCloneInternal=function(t,e){g(t)&&throwUncloneable("Symbol");if(!f(t))return t;if(e){if(K(e,t))return J(e,t)}else e=new Y;var i,r,s,a,l,c,d,u,p,m,v=_(t),P=!1;switch(v){case"Array":s=D(S(t));P=!0;break;case"Object":s={};P=!0;break;case"Map":s=new Y;P=!0;break;case"Set":s=new Z;P=!0;break;case"RegExp":s=new RegExp(t.source,x(t));break;case"Error":switch(r=t.name){case"AggregateError":s=o("AggregateError")([]);break;case"EvalError":s=G();break;case"RangeError":s=N();break;case"ReferenceError":s=j();break;case"SyntaxError":s=U();break;case"TypeError":s=B();break;case"URIError":s=W();break;case"CompileError":s=H();break;case"LinkError":s=V();break;case"RuntimeError":s=X();break;default:s=L()}P=!0;break;case"DOMException":s=new $(t.message,t.name);P=!0;break;case"DataView":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":i=n[v];f(i)||throwUnpolyfillable(v);s=new i(structuredCloneInternal(t.buffer,e),t.byteOffset,"DataView"===v?t.byteLength:t.length);break;case"DOMQuad":try{s=new DOMQuad(structuredCloneInternal(t.p1,e),structuredCloneInternal(t.p2,e),structuredCloneInternal(t.p3,e),structuredCloneInternal(t.p4,e))}catch(e){ft?s=ft(t):throwUnpolyfillable(v)}break;case"FileList":if(a=function(){var t;try{t=new n.DataTransfer}catch(e){try{t=new n.ClipboardEvent("").clipboardData}catch(t){}}return t&&t.items&&t.files?t:null}()){for(l=0,c=S(t);l<c;l++)a.items.add(structuredCloneInternal(t[l],e));s=a.files}else ft?s=ft(t):throwUnpolyfillable(v);break;case"ImageData":try{s=new ImageData(structuredCloneInternal(t.data,e),t.width,t.height,{colorSpace:t.colorSpace})}catch(e){ft?s=ft(t):throwUnpolyfillable(v)}break;default:if(ft)s=ft(t);else switch(v){case"BigInt":s=I(t.valueOf());break;case"Boolean":s=I(rt(t));break;case"Number":s=I(st(t));break;case"String":s=I(nt(t));break;case"Date":s=new O(ot(t));break;case"ArrayBuffer":(i=n.DataView)||"function"==typeof t.slice||throwUnpolyfillable(v);try{if("function"==typeof t.slice)s=t.slice(0);else{c=t.byteLength;s=new ArrayBuffer(c);p=new i(t);m=new i(s);for(l=0;l<c;l++)m.setUint8(l,p.getUint8(l))}}catch(t){throw new $("ArrayBuffer is detached",ct)}break;case"SharedArrayBuffer":s=t;break;case"Blob":try{s=t.slice(0,t.size,t.type)}catch(t){throwUnpolyfillable(v)}break;case"DOMPoint":case"DOMPointReadOnly":i=n[v];try{s=i.fromPoint?i.fromPoint(t):new i(t.x,t.y,t.z,t.w)}catch(t){throwUnpolyfillable(v)}break;case"DOMRect":case"DOMRectReadOnly":i=n[v];try{s=i.fromRect?i.fromRect(t):new i(t.x,t.y,t.width,t.height)}catch(t){throwUnpolyfillable(v)}break;case"DOMMatrix":case"DOMMatrixReadOnly":i=n[v];try{s=i.fromMatrix?i.fromMatrix(t):new i(t)}catch(t){throwUnpolyfillable(v)}break;case"AudioData":case"VideoFrame":h(t.clone)||throwUnpolyfillable(v);try{s=t.clone()}catch(t){throwUncloneable(v)}break;case"File":try{s=new File([t],t.name,t)}catch(t){throwUnpolyfillable(v)}break;case"CropTarget":case"CryptoKey":case"FileSystemDirectoryHandle":case"FileSystemFileHandle":case"FileSystemHandle":case"GPUCompilationInfo":case"GPUCompilationMessage":case"ImageBitmap":case"RTCCertificate":case"WebAssembly.Module":throwUnpolyfillable(v);default:throwUncloneable(v)}}Q(e,t,s);if(P)switch(v){case"Array":case"Object":d=et(t);for(l=0,c=S(d);l<c;l++){u=d[l];y(s,u,structuredCloneInternal(t[u],e))}break;case"Map":t.forEach((function(t,i){Q(s,structuredCloneInternal(i,e),structuredCloneInternal(t,e))}));break;case"Set":t.forEach((function(t){tt(s,structuredCloneInternal(t,e))}));break;case"Error":A(s,"message",structuredCloneInternal(t.message,e));b(t,"cause")&&A(s,"cause",structuredCloneInternal(t.cause,e));"AggregateError"==r&&(s.errors=structuredCloneInternal(t.errors,e));case"DOMException":C&&A(s,"stack",structuredCloneInternal(t.stack,e))}return s},gt=ht&&!c((function(){if(T&&k>92||R&&k>94||M&&k>97)return!1;var t=new ArrayBuffer(8),e=ht(t,{transfer:[t]});return 0!=t.byteLength||8!=e.byteLength})),tryToTransfer=function(t,e){if(!f(t))throw B("Transfer option cannot be converted to a sequence");var i=[];m(t,(function(t){it(i,v(t))}));var r,s,a,o,l,c,d=0,p=S(i);if(gt){o=ht(i,{transfer:i});for(;d<p;)Q(e,i[d],o[d++])}else for(;d<p;){r=i[d++];if(K(e,r))throw new $("Duplicate transferable",ct);switch(s=_(r)){case"ImageBitmap":a=n.OffscreenCanvas;u(a)||throwUnpolyfillable(s,dt);try{(c=new a(r.width,r.height)).getContext("bitmaprenderer").transferFromImageBitmap(r);l=c.transferToImageBitmap()}catch(t){}break;case"AudioData":case"VideoFrame":h(r.clone)&&h(r.close)||throwUnpolyfillable(s,dt);try{l=r.clone();r.close()}catch(t){}break;case"ArrayBuffer":case"MediaSourceHandle":case"MessagePort":case"OffscreenCanvas":case"ReadableStream":case"TransformStream":case"WritableStream":throwUnpolyfillable(s,dt)}if(void 0===l)throw new $("This object cannot be transferred: "+s,ct);Q(e,r,l)}};a({global:!0,enumerable:!0,sham:!gt,forced:ut},{structuredClone:function structuredClone(t){var e,i=P(arguments.length,1)>1&&!p(arguments[1])?v(arguments[1]):void 0,r=i?i.transfer:void 0;if(void 0!==r){e=new Y;tryToTransfer(r,e)}return structuredCloneInternal(t,e)}})},(t,e,i)=>{var r=i(17),s=i(10),a=i(24),n=i(82),o=i(27),l=i(54),noop=function(){},c=[],d=o("Reflect","construct"),h=/^\s*(?:class|function)\b/,u=r(h.exec),p=!h.exec(noop),f=function isConstructor(t){if(!a(t))return!1;try{d(noop,c,t);return!0}catch(t){return!1}},g=function isConstructor(t){if(!a(t))return!1;switch(n(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!u(h,l(t))}catch(t){return!0}};g.sham=!0;t.exports=!d||s((function(){var t;return f(f.call)||!f(Object)||!f((function(){t=!0}))||t}))?g:f},t=>{var e=TypeError;t.exports=function(t,i){if(t<i)throw e("Not enough arguments");return t}},(t,e,i)=>{var r=i(11),s=i(42),a=i(28),n=i(127),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||s(t,"flags")||!a(o,t)?e:r(n,t)}},(t,e,i)=>{"use strict";var r=i(50);t.exports=function(){var t=r(this),e="";t.hasIndices&&(e+="d");t.global&&(e+="g");t.ignoreCase&&(e+="i");t.multiline&&(e+="m");t.dotAll&&(e+="s");t.unicode&&(e+="u");t.unicodeSets&&(e+="v");t.sticky&&(e+="y");return e}},(t,e,i)=>{var r=i(17),s=Map.prototype;t.exports={Map:Map,set:r(s.set),get:r(s.get),has:r(s.has),remove:r(s.delete),proto:s}},(t,e,i)=>{var r=i(17),s=Set.prototype;t.exports={Set:Set,add:r(s.add),has:r(s.has),remove:r(s.delete),proto:s,$has:s.has,$keys:s.keys}},(t,e,i)=>{var r=i(10),s=i(14);t.exports=!r((function(){var t=Error("a");if(!("stack"in t))return!0;Object.defineProperty(t,"stack",s(1,7));return 7!==t.stack}))},(t,e,i)=>{var r=i(132),s=i(133);t.exports=!r&&!s&&"object"==typeof window&&"object"==typeof document},t=>{t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},(t,e,i)=>{var r=i(18);t.exports="undefined"!=typeof process&&"process"==r(process)},(t,e,i)=>{var r=i(7);t.exports=r},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0});exports.build=exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0;exports.getDocument=getDocument;exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(136),_display_utils=__w_pdfjs_require__(139),_font_loader=__w_pdfjs_require__(142),_canvas=__w_pdfjs_require__(143),_worker_options=__w_pdfjs_require__(146),_is_node=__w_pdfjs_require__(3),_message_handler=__w_pdfjs_require__(147),_metadata=__w_pdfjs_require__(148),_optional_content_config=__w_pdfjs_require__(149),_transport_stream=__w_pdfjs_require__(150),_xfa_text=__w_pdfjs_require__(151);function _classPrivateFieldSet(t,e,i){_classApplyDescriptorSet(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}function _classPrivateFieldGet(t,e){return _classApplyDescriptorGet(t,_classExtractFieldDescriptor(t,e,"get"))}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}function _classApplyDescriptorGet(t,e){return e.get?e.get.call(t):e.value}const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100;let DefaultCanvasFactory=_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;let DefaultCMapReaderFactory=_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;let DefaultStandardFontDataFactory=_display_utils.DOMStandardFontDataFactory,createPDFNetworkStream;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;if(_is_node.isNodeJS){const{NodeCanvasFactory:t,NodeCMapReaderFactory:e,NodeStandardFontDataFactory:i}=__w_pdfjs_require__(152);exports.DefaultCanvasFactory=DefaultCanvasFactory=t;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory=e;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory=i}if(_is_node.isNodeJS){const{PDFNodeStream:t}=__w_pdfjs_require__(153);createPDFNetworkStream=e=>new t(e)}else{const{PDFNetworkStream:t}=__w_pdfjs_require__(156),{PDFFetchStream:e}=__w_pdfjs_require__(157);createPDFNetworkStream=i=>(0,_display_utils.isValidFetchUrl)(i.url)?new e(i):new t(i)}function getDocument(t){if("string"==typeof t||t instanceof URL)t={url:t};else if((0,_util.isArrayBuffer)(t))t={data:t};else if(t instanceof PDFDataRangeTransport){(0,_display_utils.deprecated)("`PDFDataRangeTransport`-instance, please use a parameter object with `range`-property instead.");t={range:t}}else if("object"!=typeof t)throw new Error("Invalid parameter in getDocument, need either string, URL, TypedArray, or parameter object.");if(!t.url&&!t.data&&!t.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const e=new PDFDocumentLoadingTask,i=t.url?getUrlProp(t.url):null,r=t.data?getDataProp(t.data):null,s=t.httpHeaders||null,a=!0===t.withCredentials,n=t.password??null,o=t.range instanceof PDFDataRangeTransport?t.range:null,l=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let c=t.worker instanceof PDFWorker?t.worker:null;const d=t.verbosity,h="string"!=typeof t.docBaseUrl||(0,_display_utils.isDataScheme)(t.docBaseUrl)?null:t.docBaseUrl,u="string"==typeof t.cMapUrl?t.cMapUrl:null,p=!1!==t.cMapPacked,f=t.CMapReaderFactory||DefaultCMapReaderFactory,g="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,m=t.StandardFontDataFactory||DefaultStandardFontDataFactory,v=!0!==t.stopAtErrors,_=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,b=!1!==t.isEvalSupported,y="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!_is_node.isNodeJS,A="boolean"==typeof t.disableFontFace?t.disableFontFace:_is_node.isNodeJS,S=!0===t.fontExtraProperties,P=!0===t.enableXfa,x=t.ownerDocument||globalThis.document,w=!0===t.disableRange,E=!0===t.disableStream,C=!0===t.disableAutoFetch,k=!0===t.pdfBug,M=o?o.length:t.length??NaN,T="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!_is_node.isNodeJS&&!A,R="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:f===_display_utils.DOMCMapReaderFactory&&m===_display_utils.DOMStandardFontDataFactory&&(0,_display_utils.isValidFetchUrl)(u,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(g,document.baseURI);(0,_util.setVerbosityLevel)(d);const I=R?null:{cMapReaderFactory:new f({baseUrl:u,isCompressed:p}),standardFontDataFactory:new m({baseUrl:g})};if(!c){const t={verbosity:d,port:_worker_options.GlobalWorkerOptions.workerPort};c=t.port?PDFWorker.fromPort(t):new PDFWorker(t);e._worker=c}const D=e.docId,O={docId:D,apiVersion:"3.4.120",data:r,password:n,disableAutoFetch:C,rangeChunkSize:l,length:M,docBaseUrl:h,enableXfa:P,evaluatorOptions:{maxImageSize:_,disableFontFace:A,ignoreErrors:v,isEvalSupported:b,isOffscreenCanvasSupported:y,fontExtraProperties:S,useSystemFonts:T,cMapUrl:R?u:null,standardFontDataUrl:R?g:null}},L={ignoreErrors:v,isEvalSupported:b,disableFontFace:A,fontExtraProperties:S,enableXfa:P,ownerDocument:x,disableAutoFetch:C,pdfBug:k,styleElement:null};c.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");const t=_fetchDocument(c,O),n=new Promise((function(t){let e;o?e=new _transport_stream.PDFDataTransportStream({length:M,initialData:o.initialData,progressiveDone:o.progressiveDone,contentDispositionFilename:o.contentDispositionFilename,disableRange:w,disableStream:E},o):r||(e=createPDFNetworkStream({url:i,length:M,httpHeaders:s,withCredentials:a,rangeChunkSize:l,disableRange:w,disableStream:E}));t(e)}));return Promise.all([t,n]).then((function(t){let[i,r]=t;if(e.destroyed)throw new Error("Loading aborted");const s=new _message_handler.MessageHandler(D,i,c.port),a=new WorkerTransport(s,e,r,L,I);e._transport=a;s.send("Ready",null)}))})).catch(e._capability.reject);return e}async function _fetchDocument(t,e){if(t.destroyed)throw new Error("Worker was destroyed");const i=await t.messageHandler.sendWithPromise("GetDocRequest",e,e.data?[e.data.buffer]:null);if(t.destroyed)throw new Error("Worker was destroyed");return i}function getUrlProp(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch(e){if(_is_node.isNodeJS&&"string"==typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(t){if(_is_node.isNodeJS&&"undefined"!=typeof Buffer&&t instanceof Buffer){(0,_display_utils.deprecated)("Please provide binary data as `Uint8Array`, rather than `Buffer`.");return new Uint8Array(t)}if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return(0,_util.stringToBytes)(t);if("object"==typeof t&&!isNaN(null==t?void 0:t.length)||(0,_util.isArrayBuffer)(t))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}class PDFDocumentLoadingTask{static#t=0;#e=null;constructor(){this._capability=(0,_util.createPromiseCapability)();this._transport=null;this._worker=null;this.docId="d"+PDFDocumentLoadingTask.#t++;this.destroyed=!1;this.onPassword=null;this.onProgress=null}get onUnsupportedFeature(){return this.#e}set onUnsupportedFeature(t){(0,_display_utils.deprecated)("The PDFDocumentLoadingTask onUnsupportedFeature property will be removed in the future.");this.#e=t}get promise(){return this._capability.promise}async destroy(){var t;this.destroyed=!0;await(null===(t=this._transport)||void 0===t?void 0:t.destroy());this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}}exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this.length=t;this.initialData=e;this.progressiveDone=i;this.contentDispositionFilename=r;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=(0,_util.createPromiseCapability)()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJavaScript(){return this._transport.getJavaScript()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]&&arguments[4];this._pageIndex=t;this._pageInfo=e;this._ownerDocument=r;this._transport=i;this._stats=s?new _display_utils.StatTimer:null;this._pdfBug=s;this.commonObjs=i.commonObjs;this.objs=new PDFObjects;this.cleanupAfterRender=!1;this.pendingCleanup=!1;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport(){let{scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:r=0,dontFlip:s=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:i,offsetY:r,dontFlip:s})}getAnnotations(){let{intent:t="display"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var t;return(null===(t=this._transport._htmlForXfa)||void 0===t?void 0:t.children[this._pageIndex])||null}render(t){var e,i;let{canvasContext:r,viewport:s,intent:a="display",annotationMode:n=_util.AnnotationMode.ENABLE,transform:o=null,canvasFactory:l=null,background:c=null,optionalContentConfigPromise:d=null,annotationCanvasMap:h=null,pageColors:u=null,printAnnotationStorage:p=null}=t;null===(e=this._stats)||void 0===e||e.time("Overall");const f=this._transport.getRenderingIntent(a,n,p);this.pendingCleanup=!1;d||(d=this._transport.getOptionalContentConfig());let g=this._intentStates.get(f.cacheKey);if(!g){g=Object.create(null);this._intentStates.set(f.cacheKey,g)}if(g.streamReaderCancelTimeout){clearTimeout(g.streamReaderCancelTimeout);g.streamReaderCancelTimeout=null}const m=l||new DefaultCanvasFactory({ownerDocument:this._ownerDocument}),v=!!(f.renderingIntent&_util.RenderingIntentFlag.PRINT);if(!g.displayReadyCapability){var _;g.displayReadyCapability=(0,_util.createPromiseCapability)();g.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};null===(_=this._stats)||void 0===_||_.time("Page Request");this._pumpOperatorList(f)}const complete=t=>{var e,i;g.renderTasks.delete(b);(this.cleanupAfterRender||v)&&(this.pendingCleanup=!0);this._tryCleanup();if(t){b.capability.reject(t);this._abortOperatorList({intentState:g,reason:t instanceof Error?t:new Error(t)})}else b.capability.resolve();null===(e=this._stats)||void 0===e||e.timeEnd("Rendering");null===(i=this._stats)||void 0===i||i.timeEnd("Overall")},b=new InternalRenderTask({callback:complete,params:{canvasContext:r,viewport:s,transform:o,background:c},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:h,operatorList:g.operatorList,pageIndex:this._pageIndex,canvasFactory:m,useRequestAnimationFrame:!v,pdfBug:this._pdfBug,pageColors:u});((i=g).renderTasks||(i.renderTasks=new Set)).add(b);const y=b.task;Promise.all([g.displayReadyCapability.promise,d]).then((t=>{var e;let[i,r]=t;if(this.pendingCleanup)complete();else{null===(e=this._stats)||void 0===e||e.time("Rendering");b.initializeGraphics({transparency:i,optionalContentConfig:r});b.operatorListChanged()}})).catch(complete);return y}getOperatorList(){let{intent:t="display",annotationMode:e=_util.AnnotationMode.ENABLE,printAnnotationStorage:i=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this._transport.getRenderingIntent(t,e,i,!0);let s,a=this._intentStates.get(r.cacheKey);if(!a){a=Object.create(null);this._intentStates.set(r.cacheKey,a)}if(!a.opListReadCapability){var n,o;s=Object.create(null);s.operatorListChanged=function operatorListChanged(){if(a.operatorList.lastChunk){a.opListReadCapability.resolve(a.operatorList);a.renderTasks.delete(s)}};a.opListReadCapability=(0,_util.createPromiseCapability)();((n=a).renderTasks||(n.renderTasks=new Set)).add(s);a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};null===(o=this._stats)||void 0===o||o.time("Page Request");this._pumpOperatorList(r)}return a.opListReadCapability.promise}streamTextContent(){let{disableCombineTextItems:t=!1,includeMarkedContent:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,combineTextItems:!0!==t,includeMarkedContent:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._transport._htmlForXfa)return this.getXfa().then((t=>_xfa_text.XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const r=e.getReader(),s={items:[],styles:Object.create(null)};!function pump(){r.read().then((function(e){let{value:i,done:r}=e;if(r)t(s);else{Object.assign(s.styles,i.styles);s.items.push(...i.items);pump()}}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const i of e.renderTasks){t.push(i.completed);i.cancel()}}this.objs.clear();this.pendingCleanup=!1;return Promise.all(t)}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup=!0;return this._tryCleanup(t)}_tryCleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.pendingCleanup)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();t&&this._stats&&(this._stats=new _display_utils.StatTimer);this.pendingCleanup=!1;return!0}_startRenderPage(t,e){var i,r;const s=this._intentStates.get(e);if(s){null===(i=this._stats)||void 0===i||i.timeEnd("Page Request");null===(r=s.displayReadyCapability)||void 0===r||r.resolve(t)}}_renderPageChunk(t,e){for(let i=0,r=t.length;i<r;i++){e.operatorList.fnArray.push(t.fnArray[i]);e.operatorList.argsArray.push(t.argsArray[i])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this._tryCleanup()}_pumpOperatorList(t){let{renderingIntent:e,cacheKey:i,annotationStorageMap:r}=t;const s=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:e,cacheKey:i,annotationStorage:r}).getReader(),a=this._intentStates.get(i);a.streamReader=s;const pump=()=>{s.read().then((t=>{let{value:e,done:i}=t;if(i)a.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(e,a);pump()}}),(t=>{a.streamReader=null;if(!this._transport.destroyed){if(a.operatorList){a.operatorList.lastChunk=!0;for(const t of a.renderTasks)t.operatorListChanged();this._tryCleanup()}if(a.displayReadyCapability)a.displayReadyCapability.reject(t);else{if(!a.opListReadCapability)throw t;a.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList(t){let{intentState:e,reason:i,force:r=!1}=t;if(e.streamReader){if(e.streamReaderCancelTimeout){clearTimeout(e.streamReaderCancelTimeout);e.streamReaderCancelTimeout=null}if(!r){if(e.renderTasks.size>0)return;if(i instanceof _display_utils.RenderingCancelledException){let t=RENDERING_CANCELLED_TIMEOUT;i.extraDelay>0&&i.extraDelay<1e3&&(t+=i.extraDelay);e.streamReaderCancelTimeout=setTimeout((()=>{e.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:e,reason:i,force:!0})}),t);return}}e.streamReader.cancel(new _util.AbortException(i.message)).catch((()=>{}));e.streamReader=null;if(!this._transport.destroyed){for(const[t,i]of this._intentStates)if(i===e){this._intentStates.delete(t);break}this.cleanup()}}}get stats(){return this._stats}}exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{#i=new Set;#r=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e)};this.#r.then((()=>{for(const t of this.#i)t.call(this,i)}))}addEventListener(t,e){this.#i.add(e)}removeEventListener(t,e){this.#i.delete(e)}terminate(){this.#i.clear()}}exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;if(_is_node.isNodeJS&&"function"==typeof require){PDFWorkerUtil.isWorkerDisabled=!0;PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js"}else if("object"==typeof document){var _document,_document$currentScri;const t=null===(_document=document)||void 0===_document||null===(_document$currentScri=_document.currentScript)||void 0===_document$currentScri?void 0:_document$currentScri.src;t&&(PDFWorkerUtil.fallbackWorkerSrc=t.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(t,e){let i;try{i=new URL(t);if(!i.origin||"null"===i.origin)return!1}catch(t){return!1}const r=new URL(e,i);return i.origin===r.origin};PDFWorkerUtil.createCDNWrapper=function(t){const e=`importScripts("${t}");`;return URL.createObjectURL(new Blob([e]))};class PDFWorker{static#s=new WeakMap;constructor(){let{name:t=null,port:e=null,verbosity:i=(0,_util.getVerbosityLevel)()}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e&&PDFWorker.#s.has(e))throw new Error("Cannot use more than one PDFWorker per port.");this.name=t;this.destroyed=!1;this.verbosity=i;this._readyCapability=(0,_util.createPromiseCapability)();this._port=null;this._webWorker=null;this._messageHandler=null;if(e){PDFWorker.#s.set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new _message_handler.MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this._readyCapability.resolve();this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:t}=PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,t)||(t=PDFWorkerUtil.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t),i=new _message_handler.MessageHandler("main","worker",e),terminateEarly=()=>{e.removeEventListener("error",onWorkerError);i.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},onWorkerError=()=>{this._webWorker||terminateEarly()};e.addEventListener("error",onWorkerError);i.on("test",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else if(t){this._messageHandler=i;this._port=e;this._webWorker=e;this._readyCapability.resolve();i.send("configure",{verbosity:this.verbosity})}else{this._setupFakeWorker();i.destroy();e.terminate()}}));i.on("ready",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else try{sendTest()}catch(t){this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};sendTest();return}catch(t){(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){if(!PDFWorkerUtil.isWorkerDisabled){(0,_util.warn)("Setting up fake worker.");PDFWorkerUtil.isWorkerDisabled=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const i="fake"+PDFWorkerUtil.fakeWorkerId++,r=new _message_handler.MessageHandler(i+"_worker",i,e);t.setup(r,e);const s=new _message_handler.MessageHandler(i,i+"_worker",e);this._messageHandler=s;this._readyCapability.resolve();s.send("configure",{verbosity:this.verbosity})})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}PDFWorker.#s.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}static fromPort(t){if(null==t||!t.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return this.#s.has(t.port)?this.#s.get(t.port):new PDFWorker(t)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(null!==PDFWorkerUtil.fallbackWorkerSrc){_is_node.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.');return PDFWorkerUtil.fallbackWorkerSrc}throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){try{var t;return(null===(t=globalThis.pdfjsWorker)||void 0===t?void 0:t.WorkerMessageHandler)||null}catch(t){return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_is_node.isNodeJS&&"function"==typeof require){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}await(0,_display_utils.loadScript)(this.workerSrc);return window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}}exports.PDFWorker=PDFWorker;var _methodPromises=new WeakMap,_pageCache=new WeakMap,_pagePromises=new WeakMap,_cacheSimpleMethod=new WeakSet;class WorkerTransport{constructor(t,e,i,r,s){_classPrivateMethodInitSpec(this,_cacheSimpleMethod);_classPrivateFieldInitSpec(this,_methodPromises,{writable:!0,value:new Map});_classPrivateFieldInitSpec(this,_pageCache,{writable:!0,value:new Map});_classPrivateFieldInitSpec(this,_pagePromises,{writable:!0,value:new Map});this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new _font_loader.FontLoader({onUnsupportedFeature:this._onUnsupportedFeature.bind(this),ownerDocument:r.ownerDocument,styleElement:r.styleElement});this._params=r;this.cMapReaderFactory=null==s?void 0:s.cMapReaderFactory;this.standardFontDataFactory=null==s?void 0:s.standardFontDataFactory;this.destroyed=!1;this.destroyCapability=null;this._passwordCapability=null;this._networkStream=i;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=(0,_util.createPromiseCapability)();this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_util.AnnotationMode.ENABLE,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=_util.RenderingIntentFlag.DISPLAY,a=null;switch(t){case"any":s=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":s=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case _util.AnnotationMode.DISABLE:s+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:s+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:s+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE;a=(s&_util.RenderingIntentFlag.PRINT&&i instanceof _annotation_storage.PrintAnnotationStorage?i:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${e}`)}r&&(s+=_util.RenderingIntentFlag.OPLIST);return{renderingIntent:s,cacheKey:`${s}_${_annotation_storage.AnnotationStorage.getHash(a)}`,annotationStorageMap:a}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=(0,_util.createPromiseCapability)();this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of _classPrivateFieldGet(this,_pageCache).values())t.push(e._destroy());_classPrivateFieldGet(this,_pageCache).clear();_classPrivateFieldGet(this,_pagePromises).clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();_classPrivateFieldGet(this,_methodPromises).clear();this._networkStream&&this._networkStream.cancelAllRequests(new _util.AbortException("Worker was terminated."));if(this.messageHandler){this.messageHandler.destroy();this.messageHandler=null}this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function(t){let{value:i,done:r}=t;if(r)e.close();else{(0,_util.assert)(i instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(i),1,[i])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(t=>{const i=(0,_util.createPromiseCapability)(),r=this._fullReader;r.headersReady.then((()=>{if(!r.isStreamingSupported||!r.isRangeSupported){if(this._lastProgress){var t;null===(t=e.onProgress)||void 0===t||t.call(e,this._lastProgress)}r.onProgress=t=>{var i;null===(i=e.onProgress)||void 0===i||i.call(e,{loaded:t.loaded,total:t.total})}}i.resolve({isStreamingSupported:r.isStreamingSupported,isRangeSupported:r.isRangeSupported,contentLength:r.contentLength})}),i.reject);return i.promise}));t.on("GetRangeReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);if(i){e.onPull=()=>{i.read().then((function(t){let{value:i,done:r}=t;if(r)e.close();else{(0,_util.assert)(i instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(i),1,[i])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{i.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(t=>{let{pdfInfo:i}=t;this._numPages=i.numPages;this._htmlForXfa=i.htmlForXfa;delete i.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(i,this))}));t.on("DocException",(function(t){let i;switch(t.name){case"PasswordException":i=new _util.PasswordException(t.message,t.code);break;case"InvalidPDFException":i=new _util.InvalidPDFException(t.message);break;case"MissingPDFException":i=new _util.MissingPDFException(t.message);break;case"UnexpectedResponseException":i=new _util.UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":i=new _util.UnknownErrorException(t.message,t.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}e._capability.reject(i)}));t.on("PasswordRequest",(t=>{this._passwordCapability=(0,_util.createPromiseCapability)();if(e.onPassword){const updatePassword=t=>{t instanceof Error?this._passwordCapability.reject(t):this._passwordCapability.resolve({password:t})};try{e.onPassword(updatePassword,t.code)}catch(t){this._passwordCapability.reject(t)}}else this._passwordCapability.reject(new _util.PasswordException(t.message,t.code));return this._passwordCapability.promise}));t.on("DataLoaded",(t=>{var i;null===(i=e.onProgress)||void 0===i||i.call(e,{loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;_classPrivateFieldGet(this,_pageCache).get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(e=>{var i;let[r,s,a]=e;if(!this.destroyed&&!this.commonObjs.has(r))switch(s){case"Font":const e=this._params;if("error"in a){const t=a.error;(0,_util.warn)(`Error during font loading: ${t}`);this.commonObjs.resolve(r,t);break}let n=null;e.pdfBug&&null!==(i=globalThis.FontInspector)&&void 0!==i&&i.enabled&&(n={registerFont(t,e){globalThis.FontInspector.fontAdded(t,e)}});const o=new _font_loader.FontFaceObject(a,{isEvalSupported:e.isEvalSupported,disableFontFace:e.disableFontFace,ignoreErrors:e.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:n});this.fontLoader.bind(o).catch((e=>t.sendWithPromise("FontFallback",{id:r}))).finally((()=>{!e.fontExtraProperties&&o.data&&(o.data=null);this.commonObjs.resolve(r,o)}));break;case"FontPath":case"Image":this.commonObjs.resolve(r,a);break;default:throw new Error(`Got unknown common object type ${s}`)}}));t.on("obj",(t=>{let[e,i,r,s]=t;if(this.destroyed)return;const a=_classPrivateFieldGet(this,_pageCache).get(i);if(!a.objs.has(e))switch(r){case"Image":a.objs.resolve(e,s);const t=8e6;if(s){let e;if(s.bitmap){const{width:t,height:i}=s;e=t*i*4}else{var n;e=(null===(n=s.data)||void 0===n?void 0:n.length)||0}e>t&&(a.cleanupAfterRender=!0)}break;case"Pattern":a.objs.resolve(e,s);break;default:throw new Error(`Got unknown object type ${r}`)}}));t.on("DocProgress",(t=>{var i;this.destroyed||null===(i=e.onProgress)||void 0===i||i.call(e,{loaded:t.loaded,total:t.total})}));t.on("UnsupportedFeature",this._onUnsupportedFeature.bind(this));t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))));t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}_onUnsupportedFeature(t){var e,i;let{featureId:r}=t;this.destroyed||null===(e=(i=this.loadingTask).onUnsupportedFeature)||void 0===e||e.call(i,r)}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var t;this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:this.annotationStorage.serializable,filename:(null===(t=this._fullReader)||void 0===t?void 0:t.filename)??null}).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=_classPrivateFieldGet(this,_pagePromises).get(e);if(i)return i;const r=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((t=>{if(this.destroyed)throw new Error("Transport destroyed");const i=new PDFPageProxy(e,t,this,this._params.ownerDocument,this._params.pdfBug);_classPrivateFieldGet(this,_pageCache).set(e,i);return i}));_classPrivateFieldGet(this,_pagePromises).set(e,r);return r}getPageIndex(t){return"object"!=typeof t||null===t||!Number.isInteger(t.num)||t.num<0||!Number.isInteger(t.gen)||t.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen})}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return _classPrivateMethodGet(this,_cacheSimpleMethod,_cacheSimpleMethod2).call(this,"GetFieldObjects")}hasJSActions(){return _classPrivateMethodGet(this,_cacheSimpleMethod,_cacheSimpleMethod2).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getJavaScript(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}getDocJSActions(){return this.messageHandler.sendWithPromise("GetDocJSActions",null)}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((t=>new _optional_content_config.OptionalContentConfig(t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=_classPrivateFieldGet(this,_methodPromises).get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>{var e,i;return{info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:(null===(e=this._fullReader)||void 0===e?void 0:e.filename)??null,contentLength:(null===(i=this._fullReader)||void 0===i?void 0:i.contentLength)??null}}));_classPrivateFieldGet(this,_methodPromises).set(t,i);return i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of _classPrivateFieldGet(this,_pageCache).values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();_classPrivateFieldGet(this,_methodPromises).clear()}}get loadingParams(){const{disableAutoFetch:t,enableXfa:e}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:t,enableXfa:e})}}function _cacheSimpleMethod2(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=_classPrivateFieldGet(this,_methodPromises).get(t);if(i)return i;const r=this.messageHandler.sendWithPromise(t,e);_classPrivateFieldGet(this,_methodPromises).set(t,r);return r}var _objs=new WeakMap,_ensureObj=new WeakSet;class PDFObjects{constructor(){_classPrivateMethodInitSpec(this,_ensureObj);_classPrivateFieldInitSpec(this,_objs,{writable:!0,value:Object.create(null)})}get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(e){const i=_classPrivateMethodGet(this,_ensureObj,_ensureObj2).call(this,t);i.capability.promise.then((()=>e(i.data)));return null}const i=_classPrivateFieldGet(this,_objs)[t];if(null==i||!i.capability.settled)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=_classPrivateFieldGet(this,_objs)[t];return(null==e?void 0:e.capability.settled)||!1}resolve(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=_classPrivateMethodGet(this,_ensureObj,_ensureObj2).call(this,t);i.data=e;i.capability.resolve()}clear(){for(const e in _classPrivateFieldGet(this,_objs)){var t;const{data:i}=_classPrivateFieldGet(this,_objs)[e];null==i||null===(t=i.bitmap)||void 0===t||t.close()}_classPrivateFieldSet(this,_objs,Object.create(null))}}function _ensureObj2(t){const e=_classPrivateFieldGet(this,_objs)[t];return e||(_classPrivateFieldGet(this,_objs)[t]={capability:(0,_util.createPromiseCapability)(),data:null})}class RenderTask{#a=null;constructor(t){this.#a=t;this.onContinue=null}get promise(){return this.#a.capability.promise}cancel(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.#a.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#a.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#a;return t.form||t.canvas&&(null==e?void 0:e.size)>0}}exports.RenderTask=RenderTask;class InternalRenderTask{static#n=new WeakSet;constructor(t){let{callback:e,params:i,objs:r,commonObjs:s,annotationCanvasMap:a,operatorList:n,pageIndex:o,canvasFactory:l,useRequestAnimationFrame:c=!1,pdfBug:d=!1,pageColors:h=null}=t;this.callback=e;this.params=i;this.objs=r;this.commonObjs=s;this.annotationCanvasMap=a;this.operatorListIdx=null;this.operatorList=n;this._pageIndex=o;this.canvasFactory=l;this._pdfBug=d;this.pageColors=h;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===c&&"undefined"!=typeof window;this.cancelled=!1;this.capability=(0,_util.createPromiseCapability)();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=i.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics(t){var e,i;let{transparency:r=!1,optionalContentConfig:s}=t;if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#n.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#n.add(this._canvas)}if(this._pdfBug&&null!==(e=globalThis.StepperManager)&&void 0!==e&&e.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:a,viewport:n,transform:o,background:l}=this.params;this.gfx=new _canvas.CanvasGraphics(a,this.commonObjs,this.objs,this.canvasFactory,{optionalContentConfig:s},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:o,viewport:n,transparency:r,background:l});this.operatorListIdx=0;this.graphicsReady=!0;null===(i=this.graphicsReadyCallback)||void 0===i||i.call(this)}cancel(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.running=!1;this.cancelled=!0;null===(t=this.gfx)||void 0===t||t.endDrawing();this._canvas&&InternalRenderTask.#n.delete(this._canvas);this.callback(e||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,"canvas",i))}operatorListChanged(){var t;if(this.graphicsReady){null===(t=this.stepper)||void 0===t||t.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();this._canvas&&InternalRenderTask.#n.delete(this._canvas);this.callback()}}}}}const version="3.4.120";exports.version=version;const build="af6414988";exports.build=build},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.PrintAnnotationStorage=e.AnnotationStorage=void 0;var r=i(1),s=i(137),a=i(141);function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldSet(t,e,i){!function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classPrivateFieldGet(t,e){return function _classApplyDescriptorGet(t,e){if(e.get)return e.get.call(t);return e.value}(t,_classExtractFieldDescriptor(t,e,"get"))}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}var n=new WeakMap,o=new WeakMap,l=new WeakSet;class AnnotationStorage{constructor(){!function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}(this,l);_classPrivateFieldInitSpec(this,n,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,o,{writable:!0,value:new Map});this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const i=_classPrivateFieldGet(this,o).get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return _classPrivateFieldGet(this,o).get(t)}remove(t){_classPrivateFieldGet(this,o).delete(t);0===_classPrivateFieldGet(this,o).size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of _classPrivateFieldGet(this,o).values())if(t instanceof s.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=_classPrivateFieldGet(this,o).get(t);let r=!1;if(void 0!==i){for(const[t,s]of Object.entries(e))if(i[t]!==s){r=!0;i[t]=s}}else{r=!0;_classPrivateFieldGet(this,o).set(t,e)}r&&function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}(this,l,_setModified2).call(this);e instanceof s.AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return _classPrivateFieldGet(this,o).has(t)}getAll(){return _classPrivateFieldGet(this,o).size>0?(0,r.objectFromMap)(_classPrivateFieldGet(this,o)):null}setAll(t){for(const[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return _classPrivateFieldGet(this,o).size}resetModified(){if(_classPrivateFieldGet(this,n)){_classPrivateFieldSet(this,n,!1);"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===_classPrivateFieldGet(this,o).size)return null;const t=new Map;for(const[e,i]of _classPrivateFieldGet(this,o)){const r=i instanceof s.AnnotationEditor?i.serialize():i;r&&t.set(e,r)}return t}static getHash(t){if(!t)return"";const e=new a.MurmurHash3_64;for(const[i,r]of t)e.update(`${i}:${JSON.stringify(r)}`);return e.hexdigest()}}e.AnnotationStorage=AnnotationStorage;function _setModified2(){if(!_classPrivateFieldGet(this,n)){_classPrivateFieldSet(this,n,!0);"function"==typeof this.onSetModified&&this.onSetModified()}}class PrintAnnotationStorage extends AnnotationStorage{#o=null;constructor(t){super();this.#o=structuredClone(t.serializable)}get print(){(0,r.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#o}}e.PrintAnnotationStorage=PrintAnnotationStorage},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationEditor=void 0;var r=i(138),s=i(1);class AnnotationEditor{#l=this.focusin.bind(this);#c=this.focusout.bind(this);#d=!1;#h=!1;#u=!1;_uiManager=null;#p=AnnotationEditor._zIndex++;static _colorManager=new r.ColorManager;static _zIndex=1;constructor(t){this.constructor===AnnotationEditor&&(0,s.unreachable)("Cannot initialize AnnotationEditor.");this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;const{rotation:e,rawDims:{pageWidth:i,pageHeight:r,pageX:a,pageY:n}}=this.parent.viewport;this.rotation=e;this.pageDimensions=[i,r];this.pageTranslation=[a,n];const[o,l]=this.parentDimensions;this.x=t.x/o;this.y=t.y/l;this.isAttachedToDOM=!1}static get _defaultLineColor(){return(0,s.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#p}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}this.parent=t}focusin(t){this.#d?this.#d=!1:this.parent.setSelected(this)}focusout(t){var e;if(!this.isAttachedToDOM)return;const i=t.relatedTarget;if(null==i||!i.closest(`#${this.id}`)){t.preventDefault();null!==(e=this.parent)&&void 0!==e&&e.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}dragstart(t){const e=this.parent.div.getBoundingClientRect();this.startX=t.clientX-e.x;this.startY=t.clientY-e.y;t.dataTransfer.setData("text/plain",this.id);t.dataTransfer.effectAllowed="move"}setAt(t,e,i,r){const[s,a]=this.parentDimensions;[i,r]=this.screenToPageTranslation(i,r);this.x=(t+i)/s;this.y=(e+r)/a;this.div.style.left=100*this.x+"%";this.div.style.top=100*this.y+"%"}translate(t,e){const[i,r]=this.parentDimensions;[t,e]=this.screenToPageTranslation(t,e);this.x+=t/i;this.y+=e/r;this.div.style.left=100*this.x+"%";this.div.style.top=100*this.y+"%"}screenToPageTranslation(t,e){switch(this.parentRotation){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return this._uiManager.viewParameters.rotation}get parentDimensions(){const{realScale:t}=this._uiManager.viewParameters,[e,i]=this.pageDimensions;return[e*t,i*t]}setDims(t,e){const[i,r]=this.parentDimensions;this.div.style.width=100*t/i+"%";this.div.style.height=100*e/r+"%"}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,r=i.endsWith("%"),s=e.endsWith("%");if(r&&s)return;const[a,n]=this.parentDimensions;r||(t.width=100*parseFloat(i)/a+"%");s||(t.height=100*parseFloat(e)/n+"%")}getInitialTranslation(){return[0,0]}render(){this.div=document.createElement("div");this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360);this.div.className=this.name;this.div.setAttribute("id",this.id);this.div.setAttribute("tabIndex",0);this.setInForeground();this.div.addEventListener("focusin",this.#l);this.div.addEventListener("focusout",this.#c);const[t,e]=this.getInitialTranslation();this.translate(t,e);(0,r.bindEvents)(this,this.div,["dragstart","pointerdown"]);return this.div}pointerdown(t){const{isMac:e}=s.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this);this.#d=!0}}getRect(t,e){const i=this.parentScale,[r,s]=this.pageDimensions,[a,n]=this.pageTranslation,o=t/i,l=e/i,c=this.x*r,d=this.y*s,h=this.width*r,u=this.height*s;switch(this.rotation){case 0:return[c+o+a,s-d-l-u+n,c+o+h+a,s-d-l+n];case 90:return[c+l+a,s-d+o+n,c+l+u+a,s-d+o+h+n];case 180:return[c-o-h+a,s-d+l+n,c-o+a,s-d+l+u+n];case 270:return[c-l-u+a,s-d-o-h+n,c-l+a,s-d-o+n];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,r,s,a]=t,n=s-i,o=a-r;switch(this.rotation){case 0:return[i,e-a,n,o];case 90:return[i,e-r,o,n];case 180:return[s,e-r,n,o];case 270:return[s,e-a,o,n];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#u=!0}disableEditMode(){this.#u=!1}isInEditMode(){return this.#u}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var t;null===(t=this.div)||void 0===t||t.addEventListener("focusin",this.#l)}serialize(){(0,s.unreachable)("An editor must be serializable")}static deserialize(t,e,i){const r=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});r.rotation=t.rotation;const[s,a]=r.pageDimensions,[n,o,l,c]=r.getRectInCurrentCoords(t.rect,a);r.x=n/s;r.y=o/a;r.width=l/s;r.height=c/a;return r}remove(){this.div.removeEventListener("focusin",this.#l);this.div.removeEventListener("focusout",this.#c);this.isEmpty()||this.commit();this.parent.remove(this)}select(){var t;null===(t=this.div)||void 0===t||t.classList.add("selectedEditor")}unselect(){var t;null===(t=this.div)||void 0===t||t.classList.remove("selectedEditor")}updateParams(t,e){}disableEditing(){}enableEditing(){}get propertiesToUpdate(){return{}}get contentDiv(){return this.div}get isEditing(){return this.#h}set isEditing(t){this.#h=t;if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}}e.AnnotationEditor=AnnotationEditor},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.KeyboardManager=e.CommandManager=e.ColorManager=e.AnnotationEditorUIManager=void 0;e.bindEvents=function bindEvents(t,e,i){for(const r of i)e.addEventListener(r,t[r].bind(t))};e.opacityToHex=function opacityToHex(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")};var r=i(1),s=i(139);function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _classPrivateFieldGet(t,e){return function _classApplyDescriptorGet(t,e){if(e.get)return e.get.call(t);return e.value}(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateFieldSet(t,e,i){!function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}class IdManager{#f=0;getId(){return`${r.AnnotationEditorPrefix}${this.#f++}`}}class CommandManager{#g=[];#m=!1;#v;#_=-1;constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:128;this.#v=t}add(t){let{cmd:e,undo:i,mustExec:r,type:s=NaN,overwriteIfSameType:a=!1,keepUndo:n=!1}=t;r&&e();if(this.#m)return;const o={cmd:e,undo:i,type:s};if(-1===this.#_){this.#g.length>0&&(this.#g.length=0);this.#_=0;this.#g.push(o);return}if(a&&this.#g[this.#_].type===s){n&&(o.undo=this.#g[this.#_].undo);this.#g[this.#_]=o;return}const l=this.#_+1;if(l===this.#v)this.#g.splice(0,1);else{this.#_=l;l<this.#g.length&&this.#g.splice(l)}this.#g.push(o)}undo(){if(-1!==this.#_){this.#m=!0;this.#g[this.#_].undo();this.#m=!1;this.#_-=1}}redo(){if(this.#_<this.#g.length-1){this.#_+=1;this.#m=!0;this.#g[this.#_].cmd();this.#m=!1}}hasSomethingToUndo(){return-1!==this.#_}hasSomethingToRedo(){return this.#_<this.#g.length-1}destroy(){this.#g=null}}e.CommandManager=CommandManager;var a=new WeakSet;class KeyboardManager{constructor(t){_classPrivateMethodInitSpec(this,a);this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=r.FeatureTest.platform;for(const[i,r]of t)for(const t of i){const i=t.startsWith("mac+");if(e&&i){this.callbacks.set(t.slice(4),r);this.allKeys.add(t.split("+").at(-1))}else if(!e&&!i){this.callbacks.set(t,r);this.allKeys.add(t.split("+").at(-1))}}}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(_classPrivateMethodGet(this,a,_serialize2).call(this,e));if(i){i.bind(t)();e.stopPropagation();e.preventDefault()}}}e.KeyboardManager=KeyboardManager;function _serialize2(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);(0,s.getColorValues)(t);return(0,r.shadow)(this,"_colors",t)}convert(t){const e=(0,s.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?r.Util.makeHexColor(...e):t}}e.ColorManager=ColorManager;var n=new WeakMap,o=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,g=new WeakMap,m=new WeakMap,v=new WeakMap,_=new WeakMap,b=new WeakMap,y=new WeakMap,A=new WeakMap,S=new WeakMap,P=new WeakMap,x=new WeakMap,w=new WeakMap,E=new WeakMap,C=new WeakMap,k=new WeakMap,M=new WeakSet,T=new WeakSet,R=new WeakSet,I=new WeakSet,D=new WeakSet,O=new WeakSet,L=new WeakSet,G=new WeakSet,N=new WeakSet,j=new WeakSet,U=new WeakSet;class AnnotationEditorUIManager{constructor(t,e,i){_classPrivateMethodInitSpec(this,U);_classPrivateMethodInitSpec(this,j);_classPrivateMethodInitSpec(this,N);_classPrivateMethodInitSpec(this,G);_classPrivateMethodInitSpec(this,L);_classPrivateMethodInitSpec(this,O);_classPrivateMethodInitSpec(this,D);_classPrivateMethodInitSpec(this,I);_classPrivateMethodInitSpec(this,R);_classPrivateMethodInitSpec(this,T);_classPrivateMethodInitSpec(this,M);_classPrivateFieldInitSpec(this,n,{writable:!0,value:null});_classPrivateFieldInitSpec(this,o,{writable:!0,value:new Map});_classPrivateFieldInitSpec(this,l,{writable:!0,value:new Map});_classPrivateFieldInitSpec(this,c,{writable:!0,value:null});_classPrivateFieldInitSpec(this,d,{writable:!0,value:new CommandManager});_classPrivateFieldInitSpec(this,h,{writable:!0,value:0});_classPrivateFieldInitSpec(this,u,{writable:!0,value:null});_classPrivateFieldInitSpec(this,p,{writable:!0,value:new Set});_classPrivateFieldInitSpec(this,f,{writable:!0,value:null});_classPrivateFieldInitSpec(this,g,{writable:!0,value:new IdManager});_classPrivateFieldInitSpec(this,m,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,v,{writable:!0,value:r.AnnotationEditorType.NONE});_classPrivateFieldInitSpec(this,_,{writable:!0,value:new Set});_classPrivateFieldInitSpec(this,b,{writable:!0,value:this.copy.bind(this)});_classPrivateFieldInitSpec(this,y,{writable:!0,value:this.cut.bind(this)});_classPrivateFieldInitSpec(this,A,{writable:!0,value:this.paste.bind(this)});_classPrivateFieldInitSpec(this,S,{writable:!0,value:this.keydown.bind(this)});_classPrivateFieldInitSpec(this,P,{writable:!0,value:this.onEditingAction.bind(this)});_classPrivateFieldInitSpec(this,x,{writable:!0,value:this.onPageChanging.bind(this)});_classPrivateFieldInitSpec(this,w,{writable:!0,value:this.onScaleChanging.bind(this)});_classPrivateFieldInitSpec(this,E,{writable:!0,value:this.onRotationChanging.bind(this)});_classPrivateFieldInitSpec(this,C,{writable:!0,value:{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1}});_classPrivateFieldInitSpec(this,k,{writable:!0,value:null});_classPrivateFieldSet(this,k,t);_classPrivateFieldSet(this,f,e);_classPrivateFieldGet(this,f)._on("editingaction",_classPrivateFieldGet(this,P));_classPrivateFieldGet(this,f)._on("pagechanging",_classPrivateFieldGet(this,x));_classPrivateFieldGet(this,f)._on("scalechanging",_classPrivateFieldGet(this,w));_classPrivateFieldGet(this,f)._on("rotationchanging",_classPrivateFieldGet(this,E));_classPrivateFieldSet(this,c,i);this.viewParameters={realScale:s.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}destroy(){_classPrivateMethodGet(this,T,_removeKeyboardManager2).call(this);_classPrivateFieldGet(this,f)._off("editingaction",_classPrivateFieldGet(this,P));_classPrivateFieldGet(this,f)._off("pagechanging",_classPrivateFieldGet(this,x));_classPrivateFieldGet(this,f)._off("scalechanging",_classPrivateFieldGet(this,w));_classPrivateFieldGet(this,f)._off("rotationchanging",_classPrivateFieldGet(this,E));for(const t of _classPrivateFieldGet(this,l).values())t.destroy();_classPrivateFieldGet(this,l).clear();_classPrivateFieldGet(this,o).clear();_classPrivateFieldGet(this,p).clear();_classPrivateFieldSet(this,n,null);_classPrivateFieldGet(this,_).clear();_classPrivateFieldGet(this,d).destroy()}onPageChanging(t){let{pageNumber:e}=t;_classPrivateFieldSet(this,h,e-1)}focusMainContainer(){_classPrivateFieldGet(this,k).focus()}addShouldRescale(t){_classPrivateFieldGet(this,p).add(t)}removeShouldRescale(t){_classPrivateFieldGet(this,p).delete(t)}onScaleChanging(t){let{scale:e}=t;this.commitOrRemove();this.viewParameters.realScale=e*s.PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of _classPrivateFieldGet(this,p))t.onScaleChanging()}onRotationChanging(t){let{pagesRotation:e}=t;this.commitOrRemove();this.viewParameters.rotation=e}addToAnnotationStorage(t){t.isEmpty()||!_classPrivateFieldGet(this,c)||_classPrivateFieldGet(this,c).has(t.id)||_classPrivateFieldGet(this,c).setValue(t.id,t)}copy(t){t.preventDefault();_classPrivateFieldGet(this,n)&&_classPrivateFieldGet(this,n).commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of _classPrivateFieldGet(this,_))t.isEmpty()||e.push(t.serialize());0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}paste(t){t.preventDefault();let e=t.clipboardData.getData("application/pdfjs");if(!e)return;try{e=JSON.parse(e)}catch(t){(0,r.warn)(`paste: "${t.message}".`);return}if(!Array.isArray(e))return;this.unselectAll();const i=_classPrivateFieldGet(this,l).get(_classPrivateFieldGet(this,h));try{const t=[];for(const r of e){const e=i.deserialize(r);if(!e)return;t.push(e)}const cmd=()=>{for(const e of t)_classPrivateMethodGet(this,N,_addEditorToLayer2).call(this,e);_classPrivateMethodGet(this,U,_selectEditors2).call(this,t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd:cmd,undo:undo,mustExec:!0})}catch(t){(0,r.warn)(`paste: "${t.message}".`)}}keydown(t){var e;null!==(e=this.getActive())&&void 0!==e&&e.shouldGetKeyboardEvents()||AnnotationEditorUIManager._keyboardManager.exec(this,t)}onEditingAction(t){["undo","redo","delete","selectAll"].includes(t.name)&&this[t.name]()}setEditingState(t){if(t){_classPrivateMethodGet(this,M,_addKeyboardManager2).call(this);_classPrivateMethodGet(this,R,_addCopyPasteListeners2).call(this);_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{isEditing:_classPrivateFieldGet(this,v)!==r.AnnotationEditorType.NONE,isEmpty:_classPrivateMethodGet(this,j,_isEmpty2).call(this),hasSomethingToUndo:_classPrivateFieldGet(this,d).hasSomethingToUndo(),hasSomethingToRedo:_classPrivateFieldGet(this,d).hasSomethingToRedo(),hasSelectedEditor:!1})}else{_classPrivateMethodGet(this,T,_removeKeyboardManager2).call(this);_classPrivateMethodGet(this,I,_removeCopyPasteListeners2).call(this);_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{isEditing:!1})}}registerEditorTypes(t){if(!_classPrivateFieldGet(this,u)){_classPrivateFieldSet(this,u,t);for(const t of _classPrivateFieldGet(this,u))_classPrivateMethodGet(this,O,_dispatchUpdateUI2).call(this,t.defaultPropertiesToUpdate)}}getId(){return _classPrivateFieldGet(this,g).getId()}get currentLayer(){return _classPrivateFieldGet(this,l).get(_classPrivateFieldGet(this,h))}get currentPageIndex(){return _classPrivateFieldGet(this,h)}addLayer(t){_classPrivateFieldGet(this,l).set(t.pageIndex,t);_classPrivateFieldGet(this,m)?t.enable():t.disable()}removeLayer(t){_classPrivateFieldGet(this,l).delete(t.pageIndex)}updateMode(t){_classPrivateFieldSet(this,v,t);if(t===r.AnnotationEditorType.NONE){this.setEditingState(!1);_classPrivateMethodGet(this,G,_disableAll2).call(this)}else{this.setEditingState(!0);_classPrivateMethodGet(this,L,_enableAll2).call(this);for(const e of _classPrivateFieldGet(this,l).values())e.updateMode(t)}}updateToolbar(t){t!==_classPrivateFieldGet(this,v)&&_classPrivateFieldGet(this,f).dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(_classPrivateFieldGet(this,u)){for(const i of _classPrivateFieldGet(this,_))i.updateParams(t,e);for(const i of _classPrivateFieldGet(this,u))i.updateDefaultParams(t,e)}}getEditors(t){const e=[];for(const i of _classPrivateFieldGet(this,o).values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return _classPrivateFieldGet(this,o).get(t)}addEditor(t){_classPrivateFieldGet(this,o).set(t.id,t)}removeEditor(t){var e;_classPrivateFieldGet(this,o).delete(t.id);this.unselect(t);null===(e=_classPrivateFieldGet(this,c))||void 0===e||e.remove(t.id)}setActiveEditor(t){if(_classPrivateFieldGet(this,n)!==t){_classPrivateFieldSet(this,n,t);t&&_classPrivateMethodGet(this,O,_dispatchUpdateUI2).call(this,t.propertiesToUpdate)}}toggleSelected(t){if(_classPrivateFieldGet(this,_).has(t)){_classPrivateFieldGet(this,_).delete(t);t.unselect();_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSelectedEditor:this.hasSelection})}else{_classPrivateFieldGet(this,_).add(t);t.select();_classPrivateMethodGet(this,O,_dispatchUpdateUI2).call(this,t.propertiesToUpdate);_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSelectedEditor:!0})}}setSelected(t){for(const e of _classPrivateFieldGet(this,_))e!==t&&e.unselect();_classPrivateFieldGet(this,_).clear();_classPrivateFieldGet(this,_).add(t);t.select();_classPrivateMethodGet(this,O,_dispatchUpdateUI2).call(this,t.propertiesToUpdate);_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSelectedEditor:!0})}isSelected(t){return _classPrivateFieldGet(this,_).has(t)}unselect(t){t.unselect();_classPrivateFieldGet(this,_).delete(t);_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==_classPrivateFieldGet(this,_).size}undo(){_classPrivateFieldGet(this,d).undo();_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSomethingToUndo:_classPrivateFieldGet(this,d).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:_classPrivateMethodGet(this,j,_isEmpty2).call(this)})}redo(){_classPrivateFieldGet(this,d).redo();_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:_classPrivateFieldGet(this,d).hasSomethingToRedo(),isEmpty:_classPrivateMethodGet(this,j,_isEmpty2).call(this)})}addCommands(t){_classPrivateFieldGet(this,d).add(t);_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:_classPrivateMethodGet(this,j,_isEmpty2).call(this)})}delete(){this.commitOrRemove();if(!this.hasSelection)return;const t=[..._classPrivateFieldGet(this,_)];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)_classPrivateMethodGet(this,N,_addEditorToLayer2).call(this,e)},mustExec:!0})}commitOrRemove(){var t;null===(t=_classPrivateFieldGet(this,n))||void 0===t||t.commitOrRemove()}selectAll(){for(const t of _classPrivateFieldGet(this,_))t.commit();_classPrivateMethodGet(this,U,_selectEditors2).call(this,_classPrivateFieldGet(this,o).values())}unselectAll(){if(_classPrivateFieldGet(this,n))_classPrivateFieldGet(this,n).commitOrRemove();else if(0!==_classPrivateFieldGet(this,_).size){for(const t of _classPrivateFieldGet(this,_))t.unselect();_classPrivateFieldGet(this,_).clear();_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSelectedEditor:!1})}}isActive(t){return _classPrivateFieldGet(this,n)===t}getActive(){return _classPrivateFieldGet(this,n)}getMode(){return _classPrivateFieldGet(this,v)}}e.AnnotationEditorUIManager=AnnotationEditorUIManager;function _addKeyboardManager2(){_classPrivateFieldGet(this,k).addEventListener("keydown",_classPrivateFieldGet(this,S))}function _removeKeyboardManager2(){_classPrivateFieldGet(this,k).removeEventListener("keydown",_classPrivateFieldGet(this,S))}function _addCopyPasteListeners2(){document.addEventListener("copy",_classPrivateFieldGet(this,b));document.addEventListener("cut",_classPrivateFieldGet(this,y));document.addEventListener("paste",_classPrivateFieldGet(this,A))}function _removeCopyPasteListeners2(){document.removeEventListener("copy",_classPrivateFieldGet(this,b));document.removeEventListener("cut",_classPrivateFieldGet(this,y));document.removeEventListener("paste",_classPrivateFieldGet(this,A))}function _dispatchUpdateStates2(t){Object.entries(t).some((t=>{let[e,i]=t;return _classPrivateFieldGet(this,C)[e]!==i}))&&_classPrivateFieldGet(this,f).dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(_classPrivateFieldGet(this,C),t)})}function _dispatchUpdateUI2(t){_classPrivateFieldGet(this,f).dispatch("annotationeditorparamschanged",{source:this,details:t})}function _enableAll2(){if(!_classPrivateFieldGet(this,m)){_classPrivateFieldSet(this,m,!0);for(const t of _classPrivateFieldGet(this,l).values())t.enable()}}function _disableAll2(){this.unselectAll();if(_classPrivateFieldGet(this,m)){_classPrivateFieldSet(this,m,!1);for(const t of _classPrivateFieldGet(this,l).values())t.disable()}}function _addEditorToLayer2(t){const e=_classPrivateFieldGet(this,l).get(t.pageIndex);e?e.addOrRebuild(t):this.addEditor(t)}function _isEmpty2(){if(0===_classPrivateFieldGet(this,o).size)return!0;if(1===_classPrivateFieldGet(this,o).size)for(const t of _classPrivateFieldGet(this,o).values())return t.isEmpty();return!1}function _selectEditors2(t){_classPrivateFieldGet(this,_).clear();for(const e of t)if(!e.isEmpty()){_classPrivateFieldGet(this,_).add(e);e.select()}_classPrivateMethodGet(this,D,_dispatchUpdateStates2).call(this,{hasSelectedEditor:!0})}!function _defineProperty(t,e,i){(e=function _toPropertyKey(t){var e=function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i;return t}(AnnotationEditorUIManager,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],AnnotationEditorUIManager.prototype.selectAll],[["ctrl+z","mac+meta+z"],AnnotationEditorUIManager.prototype.undo],[["ctrl+y","ctrl+shift+Z","mac+meta+shift+Z"],AnnotationEditorUIManager.prototype.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete"],AnnotationEditorUIManager.prototype.delete],[["Escape","mac+Escape"],AnnotationEditorUIManager.prototype.unselectAll]]))},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.StatTimer=e.RenderingCancelledException=e.PixelsPerInch=e.PageViewport=e.PDFDateString=e.DOMStandardFontDataFactory=e.DOMSVGFactory=e.DOMCanvasFactory=e.DOMCMapReaderFactory=e.AnnotationPrefix=void 0;e.deprecated=function deprecated(t){console.log("Deprecated API usage: "+t)};e.getColorValues=function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";document.body.append(e);for(const i of t.keys()){e.style.color=i;const r=window.getComputedStyle(e).color;t.set(i,getRGB(r))}e.remove()};e.getCurrentTransform=function getCurrentTransform(t){const{a:e,b:i,c:r,d:s,e:a,f:n}=t.getTransform();return[e,i,r,s,a,n]};e.getCurrentTransformInverse=function getCurrentTransformInverse(t){const{a:e,b:i,c:r,d:s,e:a,f:n}=t.getTransform().invertSelf();return[e,i,r,s,a,n]};e.getFilenameFromUrl=function getFilenameFromUrl(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1]||([t]=t.split(/[#?]/,1));return t.substring(t.lastIndexOf("/")+1)};e.getPdfFilenameFromUrl=function getPdfFilenameFromUrl(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!=typeof t)return e;if(isDataScheme(t)){(0,s.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,r=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let a=i.exec(r[1])||i.exec(r[2])||i.exec(r[3]);if(a){a=a[0];if(a.includes("%"))try{a=i.exec(decodeURIComponent(a))[0]}catch(t){}}return a||e};e.getRGB=getRGB;e.getXfaPageViewport=function getXfaPageViewport(t,e){let{scale:i=1,rotation:r=0}=e;const{width:s,height:a}=t.attributes.style,n=[0,0,parseInt(s),parseInt(a)];return new PageViewport({viewBox:n,scale:i,rotation:r})};e.isDataScheme=isDataScheme;e.isPdfFile=function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)};e.isValidFetchUrl=isValidFetchUrl;e.loadScript=function loadScript(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise(((i,r)=>{const s=document.createElement("script");s.src=t;s.onload=function(t){e&&s.remove();i(t)};s.onerror=function(){r(new Error(`Cannot load script at: ${s.src}`))};(document.head||document.documentElement).append(s)}))};e.setLayerDimensions=function setLayerDimensions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(e instanceof PageViewport){const{pageWidth:r,pageHeight:s}=e.rawDims,{style:a}=t,n=`calc(var(--scale-factor) * ${r}px)`,o=`calc(var(--scale-factor) * ${s}px)`;if(i&&e.rotation%180!=0){a.width=o;a.height=n}else{a.width=n;a.height=o}}r&&t.setAttribute("data-main-rotation",e.rotation)};var r=i(140),s=i(1);e.AnnotationPrefix="pdfjs_internal_id_";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}e.PixelsPerInch=PixelsPerInch;class DOMCanvasFactory extends r.BaseCanvasFactory{constructor(){let{ownerDocument:t=globalThis.document}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super();this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");i.width=t;i.height=e;return i}}e.DOMCanvasFactory=DOMCanvasFactory;async function fetchData(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(isValidFetchUrl(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);return e?new Uint8Array(await i.arrayBuffer()):(0,s.stringToBytes)(await i.text())}return new Promise(((i,r)=>{const a=new XMLHttpRequest;a.open("GET",t,!0);e&&(a.responseType="arraybuffer");a.onreadystatechange=()=>{if(a.readyState===XMLHttpRequest.DONE){if(200===a.status||0===a.status){let t;e&&a.response?t=new Uint8Array(a.response):!e&&a.responseText&&(t=(0,s.stringToBytes)(a.responseText));if(t){i(t);return}}r(new Error(a.statusText))}};a.send(null)}))}class DOMCMapReaderFactory extends r.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t,this.isCompressed).then((t=>({cMapData:t,compressionType:e})))}}e.DOMCMapReaderFactory=DOMCMapReaderFactory;class DOMStandardFontDataFactory extends r.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t,!0)}}e.DOMStandardFontDataFactory=DOMStandardFontDataFactory;class DOMSVGFactory extends r.BaseSVGFactory{_createSVG(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}}e.DOMSVGFactory=DOMSVGFactory;class PageViewport{constructor(t){let{viewBox:e,scale:i,rotation:r,offsetX:s=0,offsetY:a=0,dontFlip:n=!1}=t;this.viewBox=e;this.scale=i;this.rotation=r;this.offsetX=s;this.offsetY=a;const o=(e[2]+e[0])/2,l=(e[3]+e[1])/2;let c,d,h,u,p,f,g,m;r%=360;r<0&&(r+=360);switch(r){case 180:c=-1;d=0;h=0;u=1;break;case 90:c=0;d=1;h=1;u=0;break;case 270:c=0;d=-1;h=-1;u=0;break;case 0:c=1;d=0;h=0;u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(n){h=-h;u=-u}if(0===c){p=Math.abs(l-e[1])*i+s;f=Math.abs(o-e[0])*i+a;g=(e[3]-e[1])*i;m=(e[2]-e[0])*i}else{p=Math.abs(o-e[0])*i+s;f=Math.abs(l-e[1])*i+a;g=(e[2]-e[0])*i;m=(e[3]-e[1])*i}this.transform=[c*i,d*i,h*i,u*i,p-c*i*o-h*i*l,f-d*i*o-u*i*l];this.width=g;this.height=m}get rawDims(){const{viewBox:t}=this;return(0,s.shadow)(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone(){let{scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:r=this.offsetY,dontFlip:s=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new PageViewport({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:i,offsetY:r,dontFlip:s})}convertToViewportPoint(t,e){return s.Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=s.Util.applyTransform([t[0],t[1]],this.transform),i=s.Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return s.Util.applyInverseTransform([t,e],this.transform)}}e.PageViewport=PageViewport;class RenderingCancelledException extends s.BaseException{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;super(t,"RenderingCancelledException");this.type=e;this.extraDelay=i}}e.RenderingCancelledException=RenderingCancelledException;function isDataScheme(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}e.StatTimer=class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&(0,s.warn)(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||(0,s.warn)(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:r,end:s}of this.times)t.push(`${i.padEnd(e)} ${s-r}ms\n`);return t.join("")}};function isValidFetchUrl(t,e){try{const{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch(t){return!1}}let a;e.PDFDateString=class PDFDateString{static toDateObject(t){if(!t||"string"!=typeof t)return null;a||(a=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=a.exec(t);if(!e)return null;const i=parseInt(e[1],10);let r=parseInt(e[2],10);r=r>=1&&r<=12?r-1:0;let s=parseInt(e[3],10);s=s>=1&&s<=31?s:1;let n=parseInt(e[4],10);n=n>=0&&n<=23?n:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const c=e[7]||"Z";let d=parseInt(e[8],10);d=d>=0&&d<=23?d:0;let h=parseInt(e[9],10)||0;h=h>=0&&h<=59?h:0;if("-"===c){n+=d;o+=h}else if("+"===c){n-=d;o-=h}return new Date(Date.UTC(i,r,s,n,o,l))}};function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);(0,s.warn)(`Not a valid color format: "${t}"`);return[0,0,0]}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.BaseStandardFontDataFactory=e.BaseSVGFactory=e.BaseCanvasFactory=e.BaseCMapReaderFactory=void 0;var r=i(1);class BaseCanvasFactory{constructor(){this.constructor===BaseCanvasFactory&&(0,r.unreachable)("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d")}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){(0,r.unreachable)("Abstract method `_createCanvas` called.")}}e.BaseCanvasFactory=BaseCanvasFactory;class BaseCMapReaderFactory{constructor(t){let{baseUrl:e=null,isCompressed:i=!0}=t;this.constructor===BaseCMapReaderFactory&&(0,r.unreachable)("Cannot initialize BaseCMapReaderFactory.");this.baseUrl=e;this.isCompressed=i}async fetch(t){let{name:e}=t;if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!e)throw new Error("CMap name must be specified.");const i=this.baseUrl+e+(this.isCompressed?".bcmap":""),s=this.isCompressed?r.CMapCompressionType.BINARY:r.CMapCompressionType.NONE;return this._fetchData(i,s).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${i}`)}))}_fetchData(t,e){(0,r.unreachable)("Abstract method `_fetchData` called.")}}e.BaseCMapReaderFactory=BaseCMapReaderFactory;class BaseStandardFontDataFactory{constructor(t){let{baseUrl:e=null}=t;this.constructor===BaseStandardFontDataFactory&&(0,r.unreachable)("Cannot initialize BaseStandardFontDataFactory.");this.baseUrl=e}async fetch(t){let{filename:e}=t;if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!e)throw new Error("Font filename must be specified.");const i=`${this.baseUrl}${e}`;return this._fetchData(i).catch((t=>{throw new Error(`Unable to load font data at: ${i}`)}))}_fetchData(t){(0,r.unreachable)("Abstract method `_fetchData` called.")}}e.BaseStandardFontDataFactory=BaseStandardFontDataFactory;class BaseSVGFactory{constructor(){this.constructor===BaseSVGFactory&&(0,r.unreachable)("Cannot initialize BaseSVGFactory.")}create(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const r=this._createSVG("svg:svg");r.setAttribute("version","1.1");if(!i){r.setAttribute("width",`${t}px`);r.setAttribute("height",`${e}px`)}r.setAttribute("preserveAspectRatio","none");r.setAttribute("viewBox",`0 0 ${t} ${e}`);return r}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){(0,r.unreachable)("Abstract method `_createSVG` called.")}}e.BaseSVGFactory=BaseSVGFactory},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.MurmurHash3_64=void 0;var r=i(1);const s=3285377520,a=4294901760,n=65535;e.MurmurHash3_64=class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:s;this.h2=t?4294967295&t:s}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length);i=0;for(let r=0,s=t.length;r<s;r++){const s=t.charCodeAt(r);if(s<=255)e[i++]=s;else{e[i++]=s>>>8;e[i++]=255&s}}}else{if(!(0,r.isArrayBuffer)(t))throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");e=t.slice();i=e.byteLength}const s=i>>2,o=i-4*s,l=new Uint32Array(e.buffer,0,s);let c=0,d=0,h=this.h1,u=this.h2;const p=3432918353,f=461845907,g=11601,m=13715;for(let t=0;t<s;t++)if(1&t){c=l[t];c=c*p&a|c*g&n;c=c<<15|c>>>17;c=c*f&a|c*m&n;h^=c;h=h<<13|h>>>19;h=5*h+3864292196}else{d=l[t];d=d*p&a|d*g&n;d=d<<15|d>>>17;d=d*f&a|d*m&n;u^=d;u=u<<13|u>>>19;u=5*u+3864292196}c=0;switch(o){case 3:c^=e[4*s+2]<<16;case 2:c^=e[4*s+1]<<8;case 1:c^=e[4*s];c=c*p&a|c*g&n;c=c<<15|c>>>17;c=c*f&a|c*m&n;1&s?h^=c:u^=c}this.h1=h;this.h2=u}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&a|36045*t&n;e=4283543511*e&a|(2950163797*(e<<16|t>>>16)&a)>>>16;t^=e>>>1;t=444984403*t&a|60499*t&n;e=3301882366*e&a|(3120437893*(e<<16|t>>>16)&a)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.FontLoader=e.FontFaceObject=void 0;var r=i(1),s=i(3);e.FontLoader=class FontLoader{constructor(t){let{onUnsupportedFeature:e,ownerDocument:i=globalThis.document,styleElement:r=null}=t;this._onUnsupportedFeature=e;this._document=i;this.nativeFontFaces=[];this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.push(t);this._document.fonts.add(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.length=0;if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async bind(t){if(t.attached||t.missingFile)return;t.attached=!0;if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){this._onUnsupportedFeature({featureId:r.UNSUPPORTED_FEATURES.errorFontLoadNative});(0,r.warn)(`Failed to load font '${e.family}': '${i}'.`);t.disableFontFace=!0;throw i}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){var t;const e=!(null===(t=this._document)||void 0===t||!t.fonts);return(0,r.shadow)(this,"isFontLoadingAPISupported",e)}get isSyncFontLoadingSupported(){let t=!1;(s.isNodeJS||"undefined"!=typeof navigator&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0);return(0,r.shadow)(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function completeRequest(){(0,r.assert)(!i.done,"completeRequest() cannot be called twice.");i.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(i);return i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,r.shadow)(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,i,r){return t.substring(0,e)+r+t.substring(e+i)}let i,s;const a=this._document.createElement("canvas");a.width=1;a.height=1;const n=a.getContext("2d");let o=0;const l=`lt${Date.now()}${this.loadTestFontId++}`;let c=this._loadTestFont;c=spliceString(c,976,l.length,l);const d=1482184792;let h=int32(c,16);for(i=0,s=l.length-3;i<s;i+=4)h=h-d+int32(l,i)|0;i<l.length&&(h=h-d+int32(l+"XXX",i)|0);c=spliceString(c,16,4,(0,r.string32)(h));const u=`@font-face {font-family:"${l}";src:${`url(data:font/opentype;base64,${btoa(c)});`}}`;this.insertRule(u);const p=this._document.createElement("div");p.style.visibility="hidden";p.style.width=p.style.height="10px";p.style.position="absolute";p.style.top=p.style.left="0px";for(const e of[t.loadedName,l]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;p.append(t)}this._document.body.append(p);!function isFontReady(t,e){if(++o>30){(0,r.warn)("Load test font never loaded.");e();return}n.font="30px "+t;n.fillText(".",0,20);n.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(l,(()=>{p.remove();e.complete()}))}};e.FontFaceObject=class FontFaceObject{constructor(t,e){let{isEvalSupported:i=!0,disableFontFace:r=!1,ignoreErrors:s=!1,onUnsupportedFeature:a,fontRegistry:n=null}=e;this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.isEvalSupported=!1!==i;this.disableFontFace=!0===r;this.ignoreErrors=!0===s;this._onUnsupportedFeature=a;this.fontRegistry=n}createNativeFontFace(){var t;if(!this.data||this.disableFontFace)return null;let e;if(this.cssFontInfo){const t={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(t.style=`oblique ${this.cssFontInfo.italicAngle}deg`);e=new FontFace(this.cssFontInfo.fontFamily,this.data,t)}else e=new FontFace(this.loadedName,this.data,{});null===(t=this.fontRegistry)||void 0===t||t.registerFont(this);return e}createFontFaceRule(){var t;if(!this.data||this.disableFontFace)return null;const e=(0,r.bytesToString)(this.data),i=`url(data:${this.mimetype};base64,${btoa(e)});`;let s;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);s=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${i}}`}else s=`@font-face {font-family:"${this.loadedName}";src:${i}}`;null===(t=this.fontRegistry)||void 0===t||t.registerFont(this,i);return s}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let i;try{i=t.get(this.loadedName+"_path_"+e)}catch(t){if(!this.ignoreErrors)throw t;this._onUnsupportedFeature({featureId:r.UNSUPPORTED_FEATURES.errorFontGetPath});(0,r.warn)(`getPathGenerator - ignoring character: "${t}".`);return this.compiledGlyphs[e]=function(t,e){}}if(this.isEvalSupported&&r.FeatureTest.isEvalSupported){const t=[];for(const e of i){const i=void 0!==e.args?e.args.join(","):"";t.push("c.",e.cmd,"(",i,");\n")}return this.compiledGlyphs[e]=new Function("c","size",t.join(""))}return this.compiledGlyphs[e]=function(t,e){for(const r of i){"scale"===r.cmd&&(r.args=[e,-e]);t[r.cmd].apply(t,r.args)}}}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.CanvasGraphics=void 0;var r=i(1),s=i(139),a=i(144),n=i(145);function _classPrivateMethodInitSpec(t,e){!function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}(t,e);e.add(t)}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}const o=4096,l=1e3,c=16;class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,i){let r;if(void 0!==this.cache[t]){r=this.cache[t];this.canvasFactory.reset(r,e,i)}else{r=this.canvasFactory.create(e,i);this.cache[t]=r}return r}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,i,r,a,n,o,l,c,d){const[h,u,p,f,g,m]=(0,s.getCurrentTransform)(t);if(0===u&&0===p){const s=o*h+g,v=Math.round(s),_=l*f+m,b=Math.round(_),y=(o+c)*h+g,A=Math.abs(Math.round(y)-v)||1,S=(l+d)*f+m,P=Math.abs(Math.round(S)-b)||1;t.setTransform(Math.sign(h),0,0,Math.sign(f),v,b);t.drawImage(e,i,r,a,n,0,0,A,P);t.setTransform(h,u,p,f,g,m);return[A,P]}if(0===h&&0===f){const s=l*p+g,v=Math.round(s),_=o*u+m,b=Math.round(_),y=(l+d)*p+g,A=Math.abs(Math.round(y)-v)||1,S=(o+c)*u+m,P=Math.abs(Math.round(S)-b)||1;t.setTransform(0,Math.sign(u),Math.sign(p),0,v,b);t.drawImage(e,i,r,a,n,0,0,P,A);t.setTransform(h,u,p,f,g,m);return[P,A]}t.drawImage(e,i,r,a,n,o,l,c,d);return[Math.hypot(h,u)*c,Math.hypot(p,f)*d]}class CanvasExtraState{constructor(t,e){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=r.IDENTITY_MATRIX;this.textMatrixScale=1;this.fontMatrix=r.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=r.TextRenderingMode.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.transferMaps=null;this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();return t}setCurrentPoint(t,e){this.x=t;this.y=e}updatePathMinMax(t,e,i){[e,i]=r.Util.applyTransform([e,i],t);this.minX=Math.min(this.minX,e);this.minY=Math.min(this.minY,i);this.maxX=Math.max(this.maxX,e);this.maxY=Math.max(this.maxY,i)}updateRectMinMax(t,e){const i=r.Util.applyTransform(e,t),s=r.Util.applyTransform(e.slice(2),t);this.minX=Math.min(this.minX,i[0],s[0]);this.minY=Math.min(this.minY,i[1],s[1]);this.maxX=Math.max(this.maxX,i[0],s[0]);this.maxY=Math.max(this.maxY,i[1],s[1])}updateScalingPathMinMax(t,e){r.Util.scaleMinMax(t,e);this.minX=Math.min(this.minX,e[0]);this.maxX=Math.max(this.maxX,e[1]);this.minY=Math.min(this.minY,e[2]);this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,i,s,a,n,o,l,c,d){const h=r.Util.bezierBoundingBox(e,i,s,a,n,o,l,c);if(d){d[0]=Math.min(d[0],h[0],h[2]);d[1]=Math.max(d[1],h[0],h[2]);d[2]=Math.min(d[2],h[1],h[3]);d[3]=Math.max(d[3],h[1],h[3])}else this.updateRectMinMax(t,h)}getPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=[this.minX,this.minY,this.maxX,this.maxY];if(t===a.PathType.STROKE){e||(0,r.unreachable)("Stroke bounding box must include transform.");const t=r.Util.singularValueDecompose2dScale(e),s=t[0]*this.lineWidth/2,a=t[1]*this.lineWidth/2;i[0]-=s;i[1]-=a;i[2]+=s;i[3]+=a}return i}updateClipFromPath(){const t=r.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t;this.minX=1/0;this.minY=1/0;this.maxX=0;this.maxY=0}getClippedPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return r.Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if("undefined"!=typeof ImageData&&e instanceof ImageData){t.putImageData(e,0,0);return}const s=e.height,a=e.width,n=s%c,o=(s-n)/c,l=0===n?o:o+1,d=t.createImageData(a,c);let h,u=0;const p=e.data,f=d.data;let g,m,v,_,b,y,A,S;if(i)switch(i.length){case 1:b=i[0];y=i[0];A=i[0];S=i[0];break;case 4:b=i[0];y=i[1];A=i[2];S=i[3]}if(e.kind===r.ImageKind.GRAYSCALE_1BPP){const e=p.byteLength,i=new Uint32Array(f.buffer,0,f.byteLength>>2),s=i.length,_=a+7>>3;let b=4294967295,y=r.FeatureTest.isLittleEndian?4278190080:255;S&&255===S[0]&&0===S[255]&&([b,y]=[y,b]);for(g=0;g<l;g++){v=g<o?c:n;h=0;for(m=0;m<v;m++){const t=e-u;let r=0;const s=t>_?a:8*t-7,n=-8&s;let o=0,l=0;for(;r<n;r+=8){l=p[u++];i[h++]=128&l?b:y;i[h++]=64&l?b:y;i[h++]=32&l?b:y;i[h++]=16&l?b:y;i[h++]=8&l?b:y;i[h++]=4&l?b:y;i[h++]=2&l?b:y;i[h++]=1&l?b:y}for(;r<s;r++){if(0===o){l=p[u++];o=128}i[h++]=l&o?b:y;o>>=1}}for(;h<s;)i[h++]=0;t.putImageData(d,0,g*c)}}else if(e.kind===r.ImageKind.RGBA_32BPP){const e=!!(b||y||A);m=0;_=a*c*4;for(g=0;g<o;g++){f.set(p.subarray(u,u+_));u+=_;if(e)for(let t=0;t<_;t+=4){b&&(f[t+0]=b[f[t+0]]);y&&(f[t+1]=y[f[t+1]]);A&&(f[t+2]=A[f[t+2]])}t.putImageData(d,0,m);m+=c}if(g<l){_=a*n*4;f.set(p.subarray(u,u+_));if(e)for(let t=0;t<_;t+=4){b&&(f[t+0]=b[f[t+0]]);y&&(f[t+1]=y[f[t+1]]);A&&(f[t+2]=A[f[t+2]])}t.putImageData(d,0,m)}}else{if(e.kind!==r.ImageKind.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);{const e=!!(b||y||A);v=c;_=a*v;for(g=0;g<l;g++){if(g>=o){v=n;_=a*v}h=0;for(m=_;m--;){f[h++]=p[u++];f[h++]=p[u++];f[h++]=p[u++];f[h++]=255}if(e)for(let t=0;t<h;t+=4){b&&(f[t+0]=b[f[t+0]]);y&&(f[t+1]=y[f[t+1]]);A&&(f[t+2]=A[f[t+2]])}t.putImageData(d,0,g*c)}}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const i=e.height,r=e.width,s=i%c,a=(i-s)/c,o=0===s?a:a+1,l=t.createImageData(r,c);let d=0;const h=e.data,u=l.data;for(let e=0;e<o;e++){const i=e<a?c:s;({srcPos:d}=(0,n.applyMaskImageData)({src:h,srcPos:d,dest:u,width:r,height:i}));t.putImageData(l,0,e*c)}}function copyCtxState(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"];for(const r of i)void 0!==t[r]&&(e[r]=t[r]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t,e){t.strokeStyle=t.fillStyle=e||"#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}}function composeSMaskBackdrop(t,e,i,r){const s=t.length;for(let a=3;a<s;a+=4){const s=t[a];if(0===s){t[a-3]=e;t[a-2]=i;t[a-1]=r}else if(s<255){const n=255-s;t[a-3]=t[a-3]*s+e*n>>8;t[a-2]=t[a-2]*s+i*n>>8;t[a-1]=t[a-1]*s+r*n>>8}}}function composeSMaskAlpha(t,e,i){const r=t.length;for(let s=3;s<r;s+=4){const r=i?i[t[s]]:t[s];e[s]=e[s]*r*.00392156862745098|0}}function composeSMaskLuminosity(t,e,i){const r=t.length;for(let s=3;s<r;s+=4){const r=77*t[s-3]+152*t[s-2]+28*t[s-1];e[s]=i?e[s]*i[r>>8]>>8:e[s]*r>>16}}function composeSMask(t,e,i,r){const s=r[0],a=r[1],n=r[2]-s,o=r[3]-a;if(0!==n&&0!==o){!function genericComposeSMask(t,e,i,r,s,a,n,o,l,c,d){const h=!!a,u=h?a[0]:0,p=h?a[1]:0,f=h?a[2]:0;let g;g="Luminosity"===s?composeSMaskLuminosity:composeSMaskAlpha;const m=Math.min(r,Math.ceil(1048576/i));for(let s=0;s<r;s+=m){const a=Math.min(m,r-s),v=t.getImageData(o-c,s+(l-d),i,a),_=e.getImageData(o,s+l,i,a);h&&composeSMaskBackdrop(v.data,u,p,f);g(v.data,_.data,n);e.putImageData(_,o,s+l)}}(e.context,i,n,o,e.subtype,e.backdrop,e.transferMap,s,a,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(i.canvas,0,0);t.restore()}}function getImageSmoothingEnabled(t,e){const i=r.Util.singularValueDecompose2dScale(t);i[0]=Math.fround(i[0]);i[1]=Math.fround(i[1]);const a=Math.fround((globalThis.devicePixelRatio||1)*s.PixelsPerInch.PDF_TO_CSS_UNITS);return void 0!==e?e:i[0]<=a||i[1]<=a}const d=["butt","round","square"],h=["miter","round","bevel"],u={},p={};var f=new WeakSet;class CanvasGraphics{constructor(t,e,i,r,s,a,n){let{optionalContentConfig:o,markedContentStack:l=null}=s;_classPrivateMethodInitSpec(this,f);this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=i;this.canvasFactory=r;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=l||[];this.optionalContentConfig=o;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=a;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.backgroundColor=(null==n?void 0:n.background)||null;this.foregroundColor=(null==n?void 0:n.foreground)||null;this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing(t){let{transform:e,viewport:i,transparency:r=!1,background:a=null}=t;const n=this.ctx.canvas.width,o=this.ctx.canvas.height,l=a||"#ffffff";this.ctx.save();if(this.foregroundColor&&this.backgroundColor){this.ctx.fillStyle=this.foregroundColor;const t=this.foregroundColor=this.ctx.fillStyle;this.ctx.fillStyle=this.backgroundColor;const e=this.backgroundColor=this.ctx.fillStyle;let i=!0,r=l;this.ctx.fillStyle=l;r=this.ctx.fillStyle;i="string"==typeof r&&/^#[0-9A-Fa-f]{6}$/.test(r);if("#000000"===t&&"#ffffff"===e||t===e||!i)this.foregroundColor=this.backgroundColor=null;else{const[i,a,n]=(0,s.getRGB)(r),newComp=t=>(t/=255)<=.03928?t/12.92:((t+.055)/1.055)**2.4,o=Math.round(.2126*newComp(i)+.7152*newComp(a)+.0722*newComp(n));this.selectColor=(i,r,s)=>{const a=.2126*newComp(i)+.7152*newComp(r)+.0722*newComp(s);return Math.round(a)===o?e:t}}}this.ctx.fillStyle=this.backgroundColor||l;this.ctx.fillRect(0,0,n,o);this.ctx.restore();if(r){const t=this.cachedCanvases.getCanvas("transparent",n,o);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...(0,s.getCurrentTransform)(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx,this.foregroundColor);if(e){this.ctx.transform(...e);this.outputScaleX=e[0];this.outputScaleY=e[0]}this.ctx.transform(...i.transform);this.viewportScale=i.scale;this.baseTransform=(0,s.getCurrentTransform)(this.ctx)}executeOperatorList(t,e,i,s){const a=t.argsArray,n=t.fnArray;let o=e||0;const l=a.length;if(l===o)return o;const c=l-o>10&&"function"==typeof i,d=c?Date.now()+15:0;let h=0;const u=this.commonObjs,p=this.objs;let f;for(;;){if(void 0!==s&&o===s.nextBreakPoint){s.breakIt(o,i);return o}f=n[o];if(f!==r.OPS.dependency)this[f].apply(this,a[o]);else for(const t of a[o]){const e=t.startsWith("g_")?u:p;if(!e.has(t)){e.get(t,i);return o}}o++;if(o===l)return o;if(c&&++h>10){if(Date.now()>d){i();return o}h=0}}}endDrawing(){_classPrivateMethodGet(this,f,_restoreInitialState2).call(this);this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear()}_scaleImage(t,e){const i=t.width,r=t.height;let s,a,n=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,c=r,d="prescale1";for(;n>2&&l>1||o>2&&c>1;){let e=l,i=c;if(n>2&&l>1){e=Math.ceil(l/2);n/=l/e}if(o>2&&c>1){i=Math.ceil(c/2);o/=c/i}s=this.cachedCanvases.getCanvas(d,e,i);a=s.context;a.clearRect(0,0,e,i);a.drawImage(t,0,0,l,c,0,0,e,i);t=s.canvas;l=e;c=i;d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:c}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:n}=t,o=this.current.fillColor,l=this.current.patternFill,c=(0,s.getCurrentTransform)(e);let d,h,u,p;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;h=JSON.stringify(l?c:[c.slice(0,4),o]);d=this._cachedBitmapsMap.get(e);if(!d){d=new Map;this._cachedBitmapsMap.set(e,d)}const i=d.get(h);if(i&&!l){return{canvas:i,offsetX:Math.round(Math.min(c[0],c[2])+c[4]),offsetY:Math.round(Math.min(c[1],c[3])+c[5])}}u=i}if(!u){p=this.cachedCanvases.getCanvas("maskCanvas",i,n);putBinaryImageMask(p.context,t)}let f=r.Util.transform(c,[1/i,0,0,-1/n,0,0]);f=r.Util.transform(f,[1,0,0,1,0,-n]);const g=r.Util.applyTransform([0,0],f),m=r.Util.applyTransform([i,n],f),v=r.Util.normalizeRect([g[0],g[1],m[0],m[1]]),_=Math.round(v[2]-v[0])||1,b=Math.round(v[3]-v[1])||1,y=this.cachedCanvases.getCanvas("fillCanvas",_,b),A=y.context,S=Math.min(g[0],m[0]),P=Math.min(g[1],m[1]);A.translate(-S,-P);A.transform(...f);if(!u){u=this._scaleImage(p.canvas,(0,s.getCurrentTransformInverse)(A));u=u.img;d&&l&&d.set(h,u)}A.imageSmoothingEnabled=getImageSmoothingEnabled((0,s.getCurrentTransform)(A),t.interpolate);drawImageAtIntegerCoords(A,u,0,0,u.width,u.height,0,0,i,n);A.globalCompositeOperation="source-in";const x=r.Util.transform((0,s.getCurrentTransformInverse)(A),[1,0,0,1,-S,-P]);A.fillStyle=l?o.getPattern(e,this,x,a.PathType.FILL):o;A.fillRect(0,0,i,n);if(d&&!l){this.cachedCanvases.delete("fillCanvas");d.set(h,y.canvas)}return{canvas:y.canvas,offsetX:Math.round(S),offsetY:Math.round(P)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking=null);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=d[t]}setLineJoin(t){this.ctx.lineJoin=h[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;if(void 0!==i.setLineDash){i.setLineDash(t);i.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i;this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.current.transferMaps=i}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,r=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;this.ctx=r.context;const a=this.ctx;a.setTransform(...(0,s.getCurrentTransform)(this.suspendedCtx));copyCtxState(this.suspendedCtx,a);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function ctxSave(){e.save();this.__originalSave()};t.restore=function ctxRestore(){e.restore();this.__originalRestore()};t.translate=function ctxTranslate(t,i){e.translate(t,i);this.__originalTranslate(t,i)};t.scale=function ctxScale(t,i){e.scale(t,i);this.__originalScale(t,i)};t.transform=function ctxTransform(t,i,r,s,a,n){e.transform(t,i,r,s,a,n);this.__originalTransform(t,i,r,s,a,n)};t.setTransform=function ctxSetTransform(t,i,r,s,a,n){e.setTransform(t,i,r,s,a,n);this.__originalSetTransform(t,i,r,s,a,n)};t.resetTransform=function ctxResetTransform(){e.resetTransform();this.__originalResetTransform()};t.rotate=function ctxRotate(t){e.rotate(t);this.__originalRotate(t)};t.clip=function ctxRotate(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,i){e.moveTo(t,i);this.__originalMoveTo(t,i)};t.lineTo=function(t,i){e.lineTo(t,i);this.__originalLineTo(t,i)};t.bezierCurveTo=function(t,i,r,s,a,n){e.bezierCurveTo(t,i,r,s,a,n);this.__originalBezierCurveTo(t,i,r,s,a,n)};t.rect=function(t,i,r,s){e.rect(t,i,r,s);this.__originalRect(t,i,r,s)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(a,this.suspendedCtx);this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask;composeSMask(this.suspendedCtx,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}save(){if(this.inSMaskMode){copyCtxState(this.ctx,this.suspendedCtx);this.suspendedCtx.save()}else this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode();if(0!==this.stateStack.length){this.current=this.stateStack.pop();if(this.inSMaskMode){this.suspendedCtx.restore();copyCtxState(this.suspendedCtx,this.ctx)}else this.ctx.restore();this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null}}transform(t,e,i,r,s,a){this.ctx.transform(t,e,i,r,s,a);this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){const a=this.ctx,n=this.current;let o,l,c=n.x,d=n.y;const h=(0,s.getCurrentTransform)(a),u=0===h[0]&&0===h[3]||0===h[1]&&0===h[2],p=u?i.slice(0):null;for(let i=0,s=0,f=t.length;i<f;i++)switch(0|t[i]){case r.OPS.rectangle:c=e[s++];d=e[s++];const t=e[s++],i=e[s++],f=c+t,g=d+i;a.moveTo(c,d);if(0===t||0===i)a.lineTo(f,g);else{a.lineTo(f,d);a.lineTo(f,g);a.lineTo(c,g)}u||n.updateRectMinMax(h,[c,d,f,g]);a.closePath();break;case r.OPS.moveTo:c=e[s++];d=e[s++];a.moveTo(c,d);u||n.updatePathMinMax(h,c,d);break;case r.OPS.lineTo:c=e[s++];d=e[s++];a.lineTo(c,d);u||n.updatePathMinMax(h,c,d);break;case r.OPS.curveTo:o=c;l=d;c=e[s+4];d=e[s+5];a.bezierCurveTo(e[s],e[s+1],e[s+2],e[s+3],c,d);n.updateCurvePathMinMax(h,o,l,e[s],e[s+1],e[s+2],e[s+3],c,d,p);s+=6;break;case r.OPS.curveTo2:o=c;l=d;a.bezierCurveTo(c,d,e[s],e[s+1],e[s+2],e[s+3]);n.updateCurvePathMinMax(h,o,l,c,d,e[s],e[s+1],e[s+2],e[s+3],p);c=e[s+2];d=e[s+3];s+=4;break;case r.OPS.curveTo3:o=c;l=d;c=e[s+2];d=e[s+3];a.bezierCurveTo(e[s],e[s+1],c,d,c,d);n.updateCurvePathMinMax(h,o,l,e[s],e[s+1],c,d,c,d,p);s+=4;break;case r.OPS.closePath:a.closePath()}u&&n.updateScalingPathMinMax(h,p);n.setCurrentPoint(c,d)}closePath(){this.ctx.closePath()}stroke(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof i&&null!=i&&i.getPattern){e.save();e.strokeStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),a.PathType.STROKE);this.rescaleAndStroke(!1);e.restore()}else this.rescaleAndStroke(!0);t&&this.consumePath(this.current.getClippedPathBoundingBox());e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const e=this.ctx,i=this.current.fillColor;let r=!1;if(this.current.patternFill){e.save();e.fillStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),a.PathType.FILL);r=!0}const n=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==n)if(this.pendingEOFill){e.fill("evenodd");this.pendingEOFill=!1}else e.fill();r&&e.restore();t&&this.consumePath(n)}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=u}eoClip(){this.pendingClip=p}beginText(){this.current.textMatrix=r.IDENTITY_MATRIX;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save();e.beginPath();for(const i of t){e.setTransform(...i.transform);e.translate(i.x,i.y);i.addToPath(e,i.fontSize)}e.restore();e.clip();e.beginPath();delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);s.fontMatrix=i.fontMatrix||r.FONT_IDENTITY_MATRIX;0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||(0,r.warn)("Invalid font matrix for font "+t);if(e<0){e=-e;s.fontDirection=-1}else s.fontDirection=1;this.current.font=i;this.current.fontSize=e;if(i.isType3Font)return;const a=i.loadedName||"sans-serif";let n="normal";i.black?n="900":i.bold&&(n="bold");const o=i.italic?"italic":"normal",l=`"${a}", ${i.fallbackName}`;let c=e;e<16?c=16:e>100&&(c=100);this.current.fontSizeScale=e/c;this.ctx.font=`${o} ${n} ${c}px ${l}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t,e,i,r,s,a){this.current.textMatrix=[t,e,i,r,s,a];this.current.textMatrixScale=Math.hypot(t,e);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,i,a){const n=this.ctx,o=this.current,l=o.font,c=o.textRenderingMode,d=o.fontSize/o.fontSizeScale,h=c&r.TextRenderingMode.FILL_STROKE_MASK,u=!!(c&r.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!l.missingFile;let f;(l.disableFontFace||u||p)&&(f=l.getPathGenerator(this.commonObjs,t));if(l.disableFontFace||p){n.save();n.translate(e,i);n.beginPath();f(n,d);a&&n.setTransform(...a);h!==r.TextRenderingMode.FILL&&h!==r.TextRenderingMode.FILL_STROKE||n.fill();h!==r.TextRenderingMode.STROKE&&h!==r.TextRenderingMode.FILL_STROKE||n.stroke();n.restore()}else{h!==r.TextRenderingMode.FILL&&h!==r.TextRenderingMode.FILL_STROKE||n.fillText(t,e,i);h!==r.TextRenderingMode.STROKE&&h!==r.TextRenderingMode.FILL_STROKE||n.strokeText(t,e,i)}if(u){(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,s.getCurrentTransform)(n),x:e,y:i,fontSize:d,addToPath:f})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return(0,r.shadow)(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const n=e.fontSize;if(0===n)return;const o=this.ctx,l=e.fontSizeScale,c=e.charSpacing,d=e.wordSpacing,h=e.fontDirection,u=e.textHScale*h,p=t.length,f=i.vertical,g=f?1:-1,m=i.defaultVMetrics,v=n*e.fontMatrix[0],_=e.textRenderingMode===r.TextRenderingMode.FILL&&!i.disableFontFace&&!e.patternFill;o.save();o.transform(...e.textMatrix);o.translate(e.x,e.y+e.textRise);h>0?o.scale(u,-1):o.scale(u,1);let b;if(e.patternFill){o.save();const t=e.fillColor.getPattern(o,this,(0,s.getCurrentTransformInverse)(o),a.PathType.FILL);b=(0,s.getCurrentTransform)(o);o.restore();o.fillStyle=t}let y=e.lineWidth;const A=e.textMatrixScale;if(0===A||0===y){const t=e.textRenderingMode&r.TextRenderingMode.FILL_STROKE_MASK;t!==r.TextRenderingMode.STROKE&&t!==r.TextRenderingMode.FILL_STROKE||(y=this.getSinglePixelWidth())}else y/=A;if(1!==l){o.scale(l,l);y/=l}o.lineWidth=y;if(i.isInvalidPDFjsFont){const i=[];let r=0;for(const e of t){i.push(e.unicode);r+=e.width}o.fillText(i.join(""),0,0);e.x+=r*v*u;o.restore();this.compose();return}let S,P=0;for(S=0;S<p;++S){const e=t[S];if("number"==typeof e){P+=g*e*n/1e3;continue}let r=!1;const s=(e.isSpace?d:0)+c,a=e.fontChar,u=e.accent;let p,y,A,x=e.width;if(f){const t=e.vmetric||m,i=-(e.vmetric?t[1]:.5*x)*v,r=t[2]*v;x=t?-t[0]:x;p=i/l;y=(P+r)/l}else{p=P/l;y=0}if(i.remeasure&&x>0){const t=1e3*o.measureText(a).width/n*l;if(x<t&&this.isFontSubpixelAAEnabled){const e=x/t;r=!0;o.save();o.scale(e,1);p/=e}else x!==t&&(p+=(x-t)/2e3*n/l)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(_&&!u)o.fillText(a,p,y);else{this.paintChar(a,p,y,b);if(u){const t=p+n*u.offset.x/l,e=y-n*u.offset.y/l;this.paintChar(u.fontChar,t,e,b)}}A=f?x*v-s*h:x*v+s*h;P+=A;r&&o.restore()}f?e.y-=P:e.x+=P*u;o.restore();this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,a=i.fontSize,n=i.fontDirection,o=s.vertical?1:-1,l=i.charSpacing,c=i.wordSpacing,d=i.textHScale*n,h=i.fontMatrix||r.FONT_IDENTITY_MATRIX,u=t.length;let p,f,g,m;if(!(i.textRenderingMode===r.TextRenderingMode.INVISIBLE)&&0!==a){this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null;e.save();e.transform(...i.textMatrix);e.translate(i.x,i.y);e.scale(d,n);for(p=0;p<u;++p){f=t[p];if("number"==typeof f){m=o*f*a/1e3;this.ctx.translate(m,0);i.x+=m*d;continue}const n=(f.isSpace?c:0)+l,u=s.charProcOperatorList[f.operatorListId];if(!u){(0,r.warn)(`Type3 character "${f.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=f;this.save();e.scale(a,a);e.transform(...h);this.executeOperatorList(u);this.restore()}g=r.Util.applyTransform([f.width,0],h)[0]*a+n;e.translate(g,0);i.x+=g*d}e.restore();this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,r,s,a){this.ctx.rect(i,r,s-i,a-r);this.ctx.clip();this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=t[1],r=this.baseTransform||(0,s.getCurrentTransform)(this.ctx),n={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new a.TilingPattern(t,i,this.ctx,n,r)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,i){var s;const a=(null===(s=this.selectColor)||void 0===s?void 0:s.call(this,t,e,i))||r.Util.makeHexColor(t,e,i);this.ctx.strokeStyle=a;this.current.strokeColor=a}setFillRGBColor(t,e,i){var s;const a=(null===(s=this.selectColor)||void 0===s?void 0:s.call(this,t,e,i))||r.Util.makeHexColor(t,e,i);this.ctx.fillStyle=a;this.current.fillColor=a;this.current.patternFill=!1}_getPattern(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.cachedPatterns.has(t))e=this.cachedPatterns.get(t);else{e=(0,a.getShadingPattern)(this.objs.get(t));this.cachedPatterns.set(t,e)}i&&(e.matrix=i);return e}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),a.PathType.SHADING);const n=(0,s.getCurrentTransformInverse)(e);if(n){const t=e.canvas,i=t.width,s=t.height,a=r.Util.applyTransform([0,0],n),o=r.Util.applyTransform([0,s],n),l=r.Util.applyTransform([i,0],n),c=r.Util.applyTransform([i,s],n),d=Math.min(a[0],o[0],l[0],c[0]),h=Math.min(a[1],o[1],l[1],c[1]),u=Math.max(a[0],o[0],l[0],c[0]),p=Math.max(a[1],o[1],l[1],c[1]);this.ctx.fillRect(d,h,u-d,p-h)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){(0,r.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,r.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);Array.isArray(t)&&6===t.length&&this.transform(...t);this.baseTransform=(0,s.getCurrentTransform)(this.ctx);if(e){const t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i);this.current.updateRectMinMax((0,s.getCurrentTransform)(this.ctx),e);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||(0,r.info)("TODO: Support non-isolated groups.");t.knockout&&(0,r.warn)("Knockout groups not supported.");const i=(0,s.getCurrentTransform)(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let a=r.Util.getAxialAlignedBoundingBox(t.bbox,(0,s.getCurrentTransform)(e));const n=[0,0,e.canvas.width,e.canvas.height];a=r.Util.intersect(a,n)||[0,0,0,0];const l=Math.floor(a[0]),c=Math.floor(a[1]);let d=Math.max(Math.ceil(a[2])-l,1),h=Math.max(Math.ceil(a[3])-c,1),u=1,p=1;if(d>o){u=d/o;d=o}if(h>o){p=h/o;h=o}this.current.startNewPathAndClipBox([0,0,d,h]);let f="groupAt"+this.groupLevel;t.smask&&(f+="_smask_"+this.smaskCounter++%2);const g=this.cachedCanvases.getCanvas(f,d,h),m=g.context;m.scale(1/u,1/p);m.translate(-l,-c);m.transform(...i);if(t.smask)this.smaskStack.push({canvas:g.canvas,context:m,offsetX:l,offsetY:c,scaleX:u,scaleY:p,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(l,c);e.scale(u,p);e.save()}copyCtxState(e,m);this.ctx=m;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();this.ctx=i;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=(0,s.getCurrentTransform)(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const i=r.Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(i)}}beginAnnotation(t,e,i,a,n){_classPrivateMethodGet(this,f,_restoreInitialState2).call(this);resetCtxToDefault(this.ctx,this.foregroundColor);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(Array.isArray(e)&&4===e.length){const a=e[2]-e[0],o=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0];i[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=a;e[3]=o;const[n,l]=r.Util.singularValueDecompose2dScale((0,s.getCurrentTransform)(this.ctx)),{viewportScale:c}=this,d=Math.ceil(a*this.outputScaleX*c),h=Math.ceil(o*this.outputScaleY*c);this.annotationCanvas=this.canvasFactory.create(d,h);const{canvas:u,context:p}=this.annotationCanvas;this.annotationCanvasMap.set(t,u);this.annotationCanvas.savedCtx=this.ctx;this.ctx=p;this.ctx.setTransform(n,0,0,-l,0,o*l);resetCtxToDefault(this.ctx,this.foregroundColor)}else{resetCtxToDefault(this.ctx,this.foregroundColor);this.ctx.rect(e[0],e[1],a,o);this.ctx.clip();this.endPath()}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...i);this.transform(...a)}endAnnotation(){if(this.annotationCanvas){this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,r=this.processingType3;if(r){void 0===r.compiled&&(r.compiled=function compileType3Glyph(t){const{width:e,height:i}=t;if(e>l||i>l)return null;const r=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),s=e+1;let a,n,o,c=new Uint8Array(s*(i+1));const d=e+7&-8;let h=new Uint8Array(d*i),u=0;for(const e of t.data){let t=128;for(;t>0;){h[u++]=e&t?0:255;t>>=1}}let p=0;u=0;if(0!==h[u]){c[0]=1;++p}for(n=1;n<e;n++){if(h[u]!==h[u+1]){c[n]=h[u]?2:1;++p}u++}if(0!==h[u]){c[n]=2;++p}for(a=1;a<i;a++){u=a*d;o=a*s;if(h[u-d]!==h[u]){c[o]=h[u]?1:8;++p}let t=(h[u]?4:0)+(h[u-d]?8:0);for(n=1;n<e;n++){t=(t>>2)+(h[u+1]?4:0)+(h[u-d+1]?8:0);if(r[t]){c[o+n]=r[t];++p}u++}if(h[u-d]!==h[u]){c[o+n]=h[u]?2:4;++p}if(p>1e3)return null}u=d*(i-1);o=a*s;if(0!==h[u]){c[o]=8;++p}for(n=1;n<e;n++){if(h[u]!==h[u+1]){c[o+n]=h[u]?4:8;++p}u++}if(0!==h[u]){c[o+n]=4;++p}if(p>1e3)return null;const f=new Int32Array([0,s,-1,0,-s,0,0,0,1]),g=new Path2D;for(a=0;p&&a<=i;a++){let t=a*s;const i=t+e;for(;t<i&&!c[t];)t++;if(t===i)continue;g.moveTo(t%s,a);const r=t;let n=c[t];do{const e=f[n];do{t+=e}while(!c[t]);const i=c[t];if(5!==i&&10!==i){n=i;c[t]=0}else{n=i&51*n>>4;c[t]&=n>>2|n<<2}g.lineTo(t%s,t/s|0);c[t]||--p}while(r!==t);--a}h=null;c=null;return function(t){t.save();t.scale(1/e,-1/i);t.translate(0,-i);t.fill(g);t.beginPath();t.restore()}}(t));if(r.compiled){r.compiled(i);return}}const s=this._createMaskCanvas(t),a=s.canvas;i.save();i.setTransform(1,0,0,1,0,0);i.drawImage(a,s.offsetX,s.offsetY);i.restore();this.compose()}paintImageMaskXObjectRepeat(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;if(!this.contentVisible)return;t=this.getObject(t.data,t);const l=this.ctx;l.save();const c=(0,s.getCurrentTransform)(l);l.transform(e,i,a,n,0,0);const d=this._createMaskCanvas(t);l.setTransform(1,0,0,1,d.offsetX-c[4],d.offsetY-c[5]);for(let t=0,s=o.length;t<s;t+=2){const s=r.Util.transform(c,[e,i,a,n,o[t],o[t+1]]),[h,u]=r.Util.applyTransform([0,0],s);l.drawImage(d.canvas,h,u)}l.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,r=this.current.patternFill;for(const n of t){const{data:t,width:o,height:l,transform:c}=n,d=this.cachedCanvases.getCanvas("maskCanvas",o,l),h=d.context;h.save();putBinaryImageMask(h,this.getObject(t,n));h.globalCompositeOperation="source-in";h.fillStyle=r?i.getPattern(h,this,(0,s.getCurrentTransformInverse)(e),a.PathType.FILL):i;h.fillRect(0,0,o,l);h.restore();e.save();e.transform(...c);e.scale(1,-1);drawImageAtIntegerCoords(e,d.canvas,0,0,o,l,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,r.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const a=this.getObject(t);if(!a){(0,r.warn)("Dependent image isn't ready yet");return}const n=a.width,o=a.height,l=[];for(let t=0,r=s.length;t<r;t+=2)l.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:n,h:o});this.paintInlineImageXObjectGroup(a,l)}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,r=this.ctx;this.save();r.scale(1/e,-1/i);let a;if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const r=this.cachedCanvases.getCanvas("inlineImage",e,i);putBinaryImageData(r.context,t,this.current.transferMaps);a=r.canvas}const n=this._scaleImage(a,(0,s.getCurrentTransformInverse)(r));r.imageSmoothingEnabled=getImageSmoothingEnabled((0,s.getCurrentTransform)(r),t.interpolate);drawImageAtIntegerCoords(r,n.img,0,0,n.paintWidth,n.paintHeight,0,-i,e,i);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx,r=t.width,s=t.height,a=this.cachedCanvases.getCanvas("inlineImage",r,s);putBinaryImageData(a.context,t,this.current.transferMaps);for(const t of e){i.save();i.transform(...t.transform);i.scale(1,-1);drawImageAtIntegerCoords(i,a.canvas,t.x,t.y,t.w,t.h,0,-1,1,1);i.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(t);const i=this.ctx;if(this.pendingClip){e||(this.pendingClip===p?i.clip("evenodd"):i.clip());this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox);i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=(0,s.getCurrentTransform)(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),r=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,r)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(!this._cachedScaleForStroking){const{lineWidth:t}=this.current,e=(0,s.getCurrentTransform)(this.ctx);let i,r;if(0===e[1]&&0===e[2]){const s=Math.abs(e[0]),a=Math.abs(e[3]);if(0===t){i=1/s;r=1/a}else{const e=s*t,n=a*t;i=e<1?1/e:1;r=n<1?1/n:1}}else{const s=Math.abs(e[0]*e[3]-e[2]*e[1]),a=Math.hypot(e[0],e[1]),n=Math.hypot(e[2],e[3]);if(0===t){i=n/s;r=a/s}else{const e=t*s;i=n>e?n/e:1;r=a>e?a/e:1}}this._cachedScaleForStroking=[i,r]}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:i}=this.current,[r,a]=this.getScaleForStroking();e.lineWidth=i||1;if(1===r&&1===a){e.stroke();return}let n,o,l;if(t){n=(0,s.getCurrentTransform)(e);o=e.getLineDash().slice();l=e.lineDashOffset}e.scale(r,a);const c=Math.max(r,a);e.setLineDash(e.getLineDash().map((t=>t/c)));e.lineDashOffset/=c;e.stroke();if(t){e.setTransform(...n);e.setLineDash(o);e.lineDashOffset=l}}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}e.CanvasGraphics=CanvasGraphics;function _restoreInitialState2(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}for(const t in r.OPS)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[r.OPS[t]]=CanvasGraphics.prototype[t])},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.TilingPattern=e.PathType=void 0;e.getShadingPattern=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)};var r=i(1),s=i(139);const a={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};e.PathType=a;function applyBoundingBox(t,e){if(!e)return;const i=e[2]-e[0],r=e[3]-e[1],s=new Path2D;s.rect(e[0],e[1],i,r);t.clip(s)}class BaseShadingPattern{constructor(){this.constructor===BaseShadingPattern&&(0,r.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,r.unreachable)("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,n){let o;if(n===a.STROKE||n===a.FILL){const a=e.current.getClippedPathBoundingBox(n,(0,s.getCurrentTransform)(t))||[0,0,0,0],l=Math.ceil(a[2]-a[0])||1,c=Math.ceil(a[3]-a[1])||1,d=e.cachedCanvases.getCanvas("pattern",l,c,!0),h=d.context;h.clearRect(0,0,h.canvas.width,h.canvas.height);h.beginPath();h.rect(0,0,h.canvas.width,h.canvas.height);h.translate(-a[0],-a[1]);i=r.Util.transform(i,[1,0,0,1,a[0],a[1]]);h.transform(...e.baseTransform);this.matrix&&h.transform(...this.matrix);applyBoundingBox(h,this._bbox);h.fillStyle=this._createGradient(h);h.fill();o=t.createPattern(d.canvas,"no-repeat");const u=new DOMMatrix(i);o.setTransform(u)}else{applyBoundingBox(t,this._bbox);o=this._createGradient(t)}return o}}function drawTriangle(t,e,i,r,s,a,n,o){const l=e.coords,c=e.colors,d=t.data,h=4*t.width;let u;if(l[i+1]>l[r+1]){u=i;i=r;r=u;u=a;a=n;n=u}if(l[r+1]>l[s+1]){u=r;r=s;s=u;u=n;n=o;o=u}if(l[i+1]>l[r+1]){u=i;i=r;r=u;u=a;a=n;n=u}const p=(l[i]+e.offsetX)*e.scaleX,f=(l[i+1]+e.offsetY)*e.scaleY,g=(l[r]+e.offsetX)*e.scaleX,m=(l[r+1]+e.offsetY)*e.scaleY,v=(l[s]+e.offsetX)*e.scaleX,_=(l[s+1]+e.offsetY)*e.scaleY;if(f>=_)return;const b=c[a],y=c[a+1],A=c[a+2],S=c[n],P=c[n+1],x=c[n+2],w=c[o],E=c[o+1],C=c[o+2],k=Math.round(f),M=Math.round(_);let T,R,I,D,O,L,G,N;for(let t=k;t<=M;t++){if(t<m){let e;e=t<f?0:(f-t)/(f-m);T=p-(p-g)*e;R=b-(b-S)*e;I=y-(y-P)*e;D=A-(A-x)*e}else{let e;e=t>_?1:m===_?0:(m-t)/(m-_);T=g-(g-v)*e;R=S-(S-w)*e;I=P-(P-E)*e;D=x-(x-C)*e}let e;e=t<f?0:t>_?1:(f-t)/(f-_);O=p-(p-v)*e;L=b-(b-w)*e;G=y-(y-E)*e;N=A-(A-C)*e;const i=Math.round(Math.min(T,O)),r=Math.round(Math.max(T,O));let s=h*t+4*i;for(let t=i;t<=r;t++){e=(T-t)/(T-O);e<0?e=0:e>1&&(e=1);d[s++]=R-(R-L)*e|0;d[s++]=I-(I-G)*e|0;d[s++]=D-(D-N)*e|0;d[s++]=255}}}function drawFigure(t,e,i){const r=e.coords,s=e.colors;let a,n;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(r.length/o)-1,c=o-1;for(a=0;a<l;a++){let e=a*o;for(let a=0;a<c;a++,e++){drawTriangle(t,i,r[e],r[e+1],r[e+o],s[e],s[e+1],s[e+o]);drawTriangle(t,i,r[e+o+1],r[e+1],r[e+o],s[e+o+1],s[e+1],s[e+o])}}break;case"triangles":for(a=0,n=r.length;a<n;a+=3)drawTriangle(t,i,r[a],r[a+1],r[a+2],s[a],s[a+1],s[a+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[7];this._background=t[8];this.matrix=null}_createMeshCanvas(t,e,i){const r=Math.floor(this._bounds[0]),s=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-r,n=Math.ceil(this._bounds[3])-s,o=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(n*t[1]*1.1)),3e3),c=a/o,d=n/l,h={coords:this._coords,colors:this._colors,offsetX:-r,offsetY:-s,scaleX:1/c,scaleY:1/d},u=o+4,p=l+4,f=i.getCanvas("mesh",u,p,!1),g=f.context,m=g.createImageData(o,l);if(e){const t=m.data;for(let i=0,r=t.length;i<r;i+=4){t[i]=e[0];t[i+1]=e[1];t[i+2]=e[2];t[i+3]=255}}for(const t of this._figures)drawFigure(m,t,h);g.putImageData(m,2,2);return{canvas:f.canvas,offsetX:r-2*c,offsetY:s-2*d,scaleX:c,scaleY:d}}getPattern(t,e,i,n){applyBoundingBox(t,this._bbox);let o;if(n===a.SHADING)o=r.Util.singularValueDecompose2dScale((0,s.getCurrentTransform)(t));else{o=r.Util.singularValueDecompose2dScale(e.baseTransform);if(this.matrix){const t=r.Util.singularValueDecompose2dScale(this.matrix);o=[o[0]*t[0],o[1]*t[1]]}}const l=this._createMeshCanvas(o,n===a.SHADING?null:this._background,e.cachedCanvases);if(n!==a.SHADING){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(l.offsetX,l.offsetY);t.scale(l.scaleX,l.scaleY);return t.createPattern(l.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const n=1,o=2;class TilingPattern{static get MAX_PATTERN_SIZE(){return(0,r.shadow)(this,"MAX_PATTERN_SIZE",3e3)}constructor(t,e,i,r,s){this.operatorList=t[2];this.matrix=t[3]||[1,0,0,1,0,0];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.ctx=i;this.canvasGraphicsFactory=r;this.baseTransform=s}createPatternCanvas(t){const e=this.operatorList,i=this.bbox,a=this.xstep,n=this.ystep,o=this.paintType,l=this.tilingType,c=this.color,d=this.canvasGraphicsFactory;(0,r.info)("TilingType: "+l);const h=i[0],u=i[1],p=i[2],f=i[3],g=r.Util.singularValueDecompose2dScale(this.matrix),m=r.Util.singularValueDecompose2dScale(this.baseTransform),v=[g[0]*m[0],g[1]*m[1]],_=this.getSizeAndScale(a,this.ctx.canvas.width,v[0]),b=this.getSizeAndScale(n,this.ctx.canvas.height,v[1]),y=t.cachedCanvases.getCanvas("pattern",_.size,b.size,!0),A=y.context,S=d.createCanvasGraphics(A);S.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(S,o,c);let P=h,x=u,w=p,E=f;if(h<0){P=0;w+=Math.abs(h)}if(u<0){x=0;E+=Math.abs(u)}A.translate(-_.scale*P,-b.scale*x);S.transform(_.scale,0,0,b.scale,0,0);A.save();this.clipBbox(S,P,x,w,E);S.baseTransform=(0,s.getCurrentTransform)(S.ctx);S.executeOperatorList(e);S.endDrawing();return{canvas:y.canvas,scaleX:_.scale,scaleY:b.scale,offsetX:P,offsetY:x}}getSizeAndScale(t,e,i){t=Math.abs(t);const r=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let s=Math.ceil(t*i);s>=r?s=r:i=s/t;return{scale:i,size:s}}clipBbox(t,e,i,r,a){const n=r-e,o=a-i;t.ctx.rect(e,i,n,o);t.current.updateRectMinMax((0,s.getCurrentTransform)(t.ctx),[e,i,r,a]);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,a=t.current;switch(e){case n:const t=this.ctx;s.fillStyle=t.fillStyle;s.strokeStyle=t.strokeStyle;a.fillColor=t.fillStyle;a.strokeColor=t.strokeStyle;break;case o:const l=r.Util.makeHexColor(i[0],i[1],i[2]);s.fillStyle=l;s.strokeStyle=l;a.fillColor=l;a.strokeColor=l;break;default:throw new r.FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,i,s){let n=i;if(s!==a.SHADING){n=r.Util.transform(n,e.baseTransform);this.matrix&&(n=r.Util.transform(n,this.matrix))}const o=this.createPatternCanvas(e);let l=new DOMMatrix(n);l=l.translate(o.offsetX,o.offsetY);l=l.scale(1/o.scaleX,1/o.scaleY);const c=t.createPattern(o.canvas,"repeat");c.setTransform(l);return c}}e.TilingPattern=TilingPattern},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.applyMaskImageData=function applyMaskImageData(t){let{src:e,srcPos:i=0,dest:s,destPos:a=0,width:n,height:o,inverseDecode:l=!1}=t;const c=r.FeatureTest.isLittleEndian?4278190080:255,[d,h]=l?[0,c]:[c,0],u=n>>3,p=7&n,f=e.length;s=new Uint32Array(s.buffer);for(let t=0;t<o;t++){for(const t=i+u;i<t;i++){const t=i<f?e[i]:255;s[a++]=128&t?h:d;s[a++]=64&t?h:d;s[a++]=32&t?h:d;s[a++]=16&t?h:d;s[a++]=8&t?h:d;s[a++]=4&t?h:d;s[a++]=2&t?h:d;s[a++]=1&t?h:d}if(0===p)continue;const t=i<f?e[i++]:255;for(let e=0;e<p;e++)s[a++]=t&1<<7-e?h:d}return{srcPos:i,destPos:a}};var r=i(1)},(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.GlobalWorkerOptions=void 0;const i=Object.create(null);e.GlobalWorkerOptions=i;i.workerPort=null;i.workerSrc=""},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.MessageHandler=void 0;var r=i(1);const s=1,a=2,n=1,o=2,l=3,c=4,d=5,h=6,u=7,p=8;function wrapReason(t){t instanceof Error||"object"==typeof t&&null!==t||(0,r.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new r.AbortException(t.message);case"MissingPDFException":return new r.MissingPDFException(t.message);case"PasswordException":return new r.PasswordException(t.message,t.code);case"UnexpectedResponseException":return new r.UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new r.UnknownErrorException(t.message,t.details);default:return new r.UnknownErrorException(t.message,t.toString())}}e.MessageHandler=class MessageHandler{constructor(t,e,i){this.sourceName=t;this.targetName=e;this.comObj=i;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream){this._processStreamMessage(e);return}if(e.callback){const t=e.callbackId,i=this.callbackCapabilities[t];if(!i)throw new Error(`Cannot resolve callback ${t}`);delete this.callbackCapabilities[t];if(e.callback===s)i.resolve(e.data);else{if(e.callback!==a)throw new Error("Unexpected callback case");i.reject(wrapReason(e.reason))}return}const r=this.actionHandler[e.action];if(!r)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,n=e.sourceName;new Promise((function(t){t(r(e.data))})).then((function(r){i.postMessage({sourceName:t,targetName:n,callback:s,callbackId:e.callbackId,data:r})}),(function(r){i.postMessage({sourceName:t,targetName:n,callback:a,callbackId:e.callbackId,reason:wrapReason(r)})}))}else e.streamId?this._createStreamSink(e):r(e.data)};i.addEventListener("message",this._onComObjOnMessage)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,a=(0,r.createPromiseCapability)();this.callbackCapabilities[s]=a;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){a.reject(t)}return a.promise}sendWithStream(t,e,i,s){const a=this.streamId++,o=this.sourceName,l=this.targetName,c=this.comObj;return new ReadableStream({start:i=>{const n=(0,r.createPromiseCapability)();this.streamControllers[a]={controller:i,startCall:n,pullCall:null,cancelCall:null,isClosed:!1};c.postMessage({sourceName:o,targetName:l,action:t,streamId:a,data:e,desiredSize:i.desiredSize},s);return n.promise},pull:t=>{const e=(0,r.createPromiseCapability)();this.streamControllers[a].pullCall=e;c.postMessage({sourceName:o,targetName:l,stream:h,streamId:a,desiredSize:t.desiredSize});return e.promise},cancel:t=>{(0,r.assert)(t instanceof Error,"cancel must have a valid reason");const e=(0,r.createPromiseCapability)();this.streamControllers[a].cancelCall=e;this.streamControllers[a].isClosed=!0;c.postMessage({sourceName:o,targetName:l,stream:n,streamId:a,reason:wrapReason(t)});return e.promise}},i)}_createStreamSink(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,a=this.comObj,n=this,o=this.actionHandler[t.action],h={enqueue(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2?arguments[2]:void 0;if(this.isCancelled)return;const l=this.desiredSize;this.desiredSize-=n;if(l>0&&this.desiredSize<=0){this.sinkCapability=(0,r.createPromiseCapability)();this.ready=this.sinkCapability.promise}a.postMessage({sourceName:i,targetName:s,stream:c,streamId:e,chunk:t},o)},close(){if(!this.isCancelled){this.isCancelled=!0;a.postMessage({sourceName:i,targetName:s,stream:l,streamId:e});delete n.streamSinks[e]}},error(t){(0,r.assert)(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;a.postMessage({sourceName:i,targetName:s,stream:d,streamId:e,reason:wrapReason(t)})}},sinkCapability:(0,r.createPromiseCapability)(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};h.sinkCapability.resolve();h.ready=h.sinkCapability.promise;this.streamSinks[e]=h;new Promise((function(e){e(o(t.data,h))})).then((function(){a.postMessage({sourceName:i,targetName:s,stream:p,streamId:e,success:!0})}),(function(t){a.postMessage({sourceName:i,targetName:s,stream:p,streamId:e,reason:wrapReason(t)})}))}_processStreamMessage(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,a=this.comObj,f=this.streamControllers[e],g=this.streamSinks[e];switch(t.stream){case p:t.success?f.startCall.resolve():f.startCall.reject(wrapReason(t.reason));break;case u:t.success?f.pullCall.resolve():f.pullCall.reject(wrapReason(t.reason));break;case h:if(!g){a.postMessage({sourceName:i,targetName:s,stream:u,streamId:e,success:!0});break}g.desiredSize<=0&&t.desiredSize>0&&g.sinkCapability.resolve();g.desiredSize=t.desiredSize;new Promise((function(t){t(g.onPull&&g.onPull())})).then((function(){a.postMessage({sourceName:i,targetName:s,stream:u,streamId:e,success:!0})}),(function(t){a.postMessage({sourceName:i,targetName:s,stream:u,streamId:e,reason:wrapReason(t)})}));break;case c:(0,r.assert)(f,"enqueue should have stream controller");if(f.isClosed)break;f.controller.enqueue(t.chunk);break;case l:(0,r.assert)(f,"close should have stream controller");if(f.isClosed)break;f.isClosed=!0;f.controller.close();this._deleteStreamController(f,e);break;case d:(0,r.assert)(f,"error should have stream controller");f.controller.error(wrapReason(t.reason));this._deleteStreamController(f,e);break;case o:t.success?f.cancelCall.resolve():f.cancelCall.reject(wrapReason(t.reason));this._deleteStreamController(f,e);break;case n:if(!g)break;new Promise((function(e){e(g.onCancel&&g.onCancel(wrapReason(t.reason)))})).then((function(){a.postMessage({sourceName:i,targetName:s,stream:o,streamId:e,success:!0})}),(function(t){a.postMessage({sourceName:i,targetName:s,stream:o,streamId:e,reason:wrapReason(t)})}));g.sinkCapability.reject(wrapReason(t.reason));g.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async _deleteStreamController(t,e){await Promise.allSettled([t.startCall&&t.startCall.promise,t.pullCall&&t.pullCall.promise,t.cancelCall&&t.cancelCall.promise]);delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.Metadata=void 0;var r=i(1);e.Metadata=class Metadata{#b;#y;constructor(t){let{parsedData:e,rawData:i}=t;this.#b=e;this.#y=i}getRaw(){return this.#y}get(t){return this.#b.get(t)??null}getAll(){return(0,r.objectFromMap)(this.#b)}has(t){return this.#b.has(t)}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.OptionalContentConfig=void 0;var r=i(1),s=i(141);function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}function _classPrivateFieldGet(t,e){return function _classApplyDescriptorGet(t,e){if(e.get)return e.get.call(t);return e.value}(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateFieldSet(t,e,i){!function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}const a=Symbol("INTERNAL");class OptionalContentGroup{#A=!0;constructor(t,e){this.name=t;this.intent=e}get visible(){return this.#A}_setVisible(t,e){t!==a&&(0,r.unreachable)("Internal method `_setVisible` called.");this.#A=e}}var n=new WeakMap,o=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakSet;e.OptionalContentConfig=class OptionalContentConfig{constructor(t){!function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}(this,d);_classPrivateFieldInitSpec(this,n,{writable:!0,value:null});_classPrivateFieldInitSpec(this,o,{writable:!0,value:new Map});_classPrivateFieldInitSpec(this,l,{writable:!0,value:null});_classPrivateFieldInitSpec(this,c,{writable:!0,value:null});this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;_classPrivateFieldSet(this,c,t.order);for(const e of t.groups)_classPrivateFieldGet(this,o).set(e.id,new OptionalContentGroup(e.name,e.intent));if("OFF"===t.baseState)for(const t of _classPrivateFieldGet(this,o).values())t._setVisible(a,!1);for(const e of t.on)_classPrivateFieldGet(this,o).get(e)._setVisible(a,!0);for(const e of t.off)_classPrivateFieldGet(this,o).get(e)._setVisible(a,!1);_classPrivateFieldSet(this,l,this.getHash())}}isVisible(t){if(0===_classPrivateFieldGet(this,o).size)return!0;if(!t){(0,r.warn)("Optional content group not defined.");return!0}if("OCG"===t.type){if(!_classPrivateFieldGet(this,o).has(t.id)){(0,r.warn)(`Optional content group not found: ${t.id}`);return!0}return _classPrivateFieldGet(this,o).get(t.id).visible}if("OCMD"===t.type){if(t.expression)return _classPrivateMethodGet(this,d,_evaluateVisibilityExpression2).call(this,t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,o).has(e)){(0,r.warn)(`Optional content group not found: ${e}`);return!0}if(_classPrivateFieldGet(this,o).get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,o).has(e)){(0,r.warn)(`Optional content group not found: ${e}`);return!0}if(!_classPrivateFieldGet(this,o).get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,o).has(e)){(0,r.warn)(`Optional content group not found: ${e}`);return!0}if(!_classPrivateFieldGet(this,o).get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!_classPrivateFieldGet(this,o).has(e)){(0,r.warn)(`Optional content group not found: ${e}`);return!0}if(_classPrivateFieldGet(this,o).get(e).visible)return!1}return!0}(0,r.warn)(`Unknown optional content policy ${t.policy}.`);return!0}(0,r.warn)(`Unknown group type ${t.type}.`);return!0}setVisibility(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(_classPrivateFieldGet(this,o).has(t)){_classPrivateFieldGet(this,o).get(t)._setVisible(a,!!e);_classPrivateFieldSet(this,n,null)}else(0,r.warn)(`Optional content group not found: ${t}`)}get hasInitialVisibility(){return this.getHash()===_classPrivateFieldGet(this,l)}getOrder(){return _classPrivateFieldGet(this,o).size?_classPrivateFieldGet(this,c)?_classPrivateFieldGet(this,c).slice():[..._classPrivateFieldGet(this,o).keys()]:null}getGroups(){return _classPrivateFieldGet(this,o).size>0?(0,r.objectFromMap)(_classPrivateFieldGet(this,o)):null}getGroup(t){return _classPrivateFieldGet(this,o).get(t)||null}getHash(){if(null!==_classPrivateFieldGet(this,n))return _classPrivateFieldGet(this,n);const t=new s.MurmurHash3_64;for(const[e,i]of _classPrivateFieldGet(this,o))t.update(`${e}:${i.visible}`);return _classPrivateFieldSet(this,n,t.hexdigest())}};function _evaluateVisibilityExpression2(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let a;if(Array.isArray(e))a=_classPrivateMethodGet(this,d,_evaluateVisibilityExpression2).call(this,e);else{if(!_classPrivateFieldGet(this,o).has(e)){(0,r.warn)(`Optional content group not found: ${e}`);return!0}a=_classPrivateFieldGet(this,o).get(e).visible}switch(i){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return"And"===i}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.PDFDataTransportStream=void 0;var r=i(1),s=i(139);e.PDFDataTransportStream=class PDFDataTransportStream{constructor(t,e){let{length:i,initialData:s,progressiveDone:a=!1,contentDispositionFilename:n=null,disableRange:o=!1,disableStream:l=!1}=t;(0,r.assert)(e,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');this._queuedChunks=[];this._progressiveDone=a;this._contentDispositionFilename=n;if((null==s?void 0:s.length)>0){const t=s instanceof Uint8Array&&s.byteLength===s.buffer.byteLength?s.buffer:new Uint8Array(s).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=e;this._isStreamingSupported=!l;this._isRangeSupported=!o;this._contentLength=i;this._fullRequestReader=null;this._rangeReaders=[];this._pdfDataRangeTransport.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));this._pdfDataRangeTransport.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));this._pdfDataRangeTransport.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));this._pdfDataRangeTransport.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));this._pdfDataRangeTransport.transportReady()}_onReceiveData(t){let{begin:e,chunk:i}=t;const s=i instanceof Uint8Array&&i.byteLength===i.buffer.byteLength?i.buffer:new Uint8Array(i).buffer;if(void 0===e)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const t=this._rangeReaders.some((function(t){if(t._begin!==e)return!1;t._enqueue(s);return!0}));(0,r.assert)(t,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var t;return(null===(t=this._fullRequestReader)||void 0===t?void 0:t._loaded)??0}_onProgress(t){if(void 0===t.total){var e,i;null===(e=this._rangeReaders[0])||void 0===e||null===(i=e.onProgress)||void 0===i||i.call(e,{loaded:t.loaded})}else{var r,s;null===(r=this._fullRequestReader)||void 0===r||null===(s=r.onProgress)||void 0===s||s.call(r,{loaded:t.loaded,total:t.total})}}_onProgressiveDone(){var t;null===(t=this._fullRequestReader)||void 0===t||t.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){(0,r.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(i);return i}cancelAllRequests(t){var e;null===(e=this._fullRequestReader)||void 0===e||e.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}};class PDFDataTransportStreamReader{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this._stream=t;this._done=i||!1;this._filename=(0,s.isPdfFile)(r)?r:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,r.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,i){this._stream=t;this._begin=e;this._end=i;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,r.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}},(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.XfaText=void 0;class XfaText{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};!function walk(t){var i;if(!t)return;let r=null;const s=t.name;if("#text"===s)r=t.value;else{if(!XfaText.shouldBuildText(s))return;null!=t&&null!==(i=t.attributes)&&void 0!==i&&i.textContent?r=t.attributes.textContent:t.value&&(r=t.value)}null!==r&&e.push({str:r});if(t.children)for(const e of t.children)walk(e)}(t);return i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}e.XfaText=XfaText},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.NodeStandardFontDataFactory=e.NodeCanvasFactory=e.NodeCMapReaderFactory=void 0;var r=i(140);const fetchData=function(t){return new Promise(((e,i)=>{require("fs").readFile(t,((t,r)=>{!t&&r?e(new Uint8Array(r)):i(new Error(t))}))}))};class NodeCanvasFactory extends r.BaseCanvasFactory{_createCanvas(t,e){return require("canvas").createCanvas(t,e)}}e.NodeCanvasFactory=NodeCanvasFactory;class NodeCMapReaderFactory extends r.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t).then((t=>({cMapData:t,compressionType:e})))}}e.NodeCMapReaderFactory=NodeCMapReaderFactory;class NodeStandardFontDataFactory extends r.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t)}}e.NodeStandardFontDataFactory=NodeStandardFontDataFactory},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.PDFNodeStream=void 0;var r=i(1),s=i(154);const a=require("fs"),n=require("http"),o=require("https"),l=require("url"),c=/^file:\/\/\/[a-zA-Z]:\//;e.PDFNodeStream=class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrl(t){const e=l.parse(t);if("file:"===e.protocol||e.host)return e;if(/^[a-z]:[/\\]/i.test(t))return l.parse(`file:///${t}`);e.host||(e.protocol="file:");return e}(t.url);this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol;this.isFsUrl="file:"===this.url.protocol;this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return(null===(t=this._fullRequestReader)||void 0===t?void 0:t._loaded)??0}getFullReader(){(0,r.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=this.isFsUrl?new PDFNodeStreamFsFullReader(this):new PDFNodeStreamFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=this.isFsUrl?new PDFNodeStreamFsRangeReader(this,t,e):new PDFNodeStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){var e;null===(e=this._fullRequestReader)||void 0===e||e.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class BaseFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=(0,r.createPromiseCapability)();this._headersCapability=(0,r.createPromiseCapability)()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const e=this._readableStream.read();if(null===e){this._readCapability=(0,r.createPromiseCapability)();return this.read()}this._loaded+=e.length;null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(e).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new r.AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class BaseRangeReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=(0,r.createPromiseCapability)();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const e=this._readableStream.read();if(null===e){this._readCapability=(0,r.createPromiseCapability)();return this.read()}this._loaded+=e.length;null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded});return{value:new Uint8Array(e).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}function createRequestOptions(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class PDFNodeStreamFullReader extends BaseFullReader{constructor(t){super(t);const handleResponse=e=>{if(404===e.statusCode){const t=new r.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t;this._headersCapability.reject(t);return}this._headersCapability.resolve();this._setReadableStream(e);const getResponseHeader=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:i,suggestedLength:a}=(0,s.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=i;this._contentLength=a||this._contentLength;this._filename=(0,s.extractFilenameFromHeader)(getResponseHeader)};this._request=null;"http:"===this._url.protocol?this._request=n.request(createRequestOptions(this._url,t.httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,t.httpHeaders),handleResponse);this._request.on("error",(t=>{this._storedError=t;this._headersCapability.reject(t)}));this._request.end()}}class PDFNodeStreamRangeReader extends BaseRangeReader{constructor(t,e,i){super(t);this._httpHeaders={};for(const e in t.httpHeaders){const i=t.httpHeaders[e];void 0!==i&&(this._httpHeaders[e]=i)}this._httpHeaders.Range=`bytes=${e}-${i-1}`;const handleResponse=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new r.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t}};this._request=null;"http:"===this._url.protocol?this._request=n.request(createRequestOptions(this._url,this._httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,this._httpHeaders),handleResponse);this._request.on("error",(t=>{this._storedError=t}));this._request.end()}}class PDFNodeStreamFsFullReader extends BaseFullReader{constructor(t){super(t);let e=decodeURIComponent(this._url.path);c.test(this._url.href)&&(e=e.replace(/^\//,""));a.lstat(e,((t,i)=>{if(t){"ENOENT"===t.code&&(t=new r.MissingPDFException(`Missing PDF "${e}".`));this._storedError=t;this._headersCapability.reject(t)}else{this._contentLength=i.size;this._setReadableStream(a.createReadStream(e));this._headersCapability.resolve()}}))}}class PDFNodeStreamFsRangeReader extends BaseRangeReader{constructor(t,e,i){super(t);let r=decodeURIComponent(this._url.path);c.test(this._url.href)&&(r=r.replace(/^\//,""));this._setReadableStream(a.createReadStream(r,{start:e,end:i-1}))}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.createResponseStatusError=function createResponseStatusError(t,e){if(404===t||0===t&&e.startsWith("file:"))return new r.MissingPDFException('Missing PDF "'+e+'".');return new r.UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)};e.extractFilenameFromHeader=function extractFilenameFromHeader(t){const e=t("Content-Disposition");if(e){let t=(0,s.getFilenameFromContentDispositionHeader)(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch(t){}if((0,a.isPdfFile)(t))return t}return null};e.validateRangeRequestCapabilities=function validateRangeRequestCapabilities(t){let{getResponseHeader:e,isHttp:i,rangeChunkSize:r,disableRange:s}=t;const a={allowRangeRequests:!1,suggestedLength:void 0},n=parseInt(e("Content-Length"),10);if(!Number.isInteger(n))return a;a.suggestedLength=n;if(n<=2*r)return a;if(s||!i)return a;if("bytes"!==e("Accept-Ranges"))return a;if("identity"!==(e("Content-Encoding")||"identity"))return a;a.allowRangeRequests=!0;return a};e.validateResponseStatus=function validateResponseStatus(t){return 200===t||206===t};var r=i(1),s=i(155),a=i(139)},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.getFilenameFromContentDispositionHeader=function getFilenameFromContentDispositionHeader(t){let e=!0,i=toParamRegExp("filename\\*","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}i=function rfc2231getparam(t){const e=[];let i;const r=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=r.exec(t));){let[,t,r,s]=i;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[r,s]}const s=[];for(let t=0;t<e.length&&t in e;++t){let[i,r]=e[t];r=rfc2616unquote(r);if(i){r=unescape(r);0===t&&(r=rfc5987decode(r))}s.push(r)}return s.join("")}(t);if(i){return fixupEncoding(rfc2047decode(i))}i=toParamRegExp("filename","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),a=(0,r.stringToBytes)(i);i=s.decode(a);e=!1}catch(t){}}return i}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');if(-1!==i){e[t]=e[t].slice(0,i);e.length=t+1}e[t]=e[t].replace(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");if(-1===e)return t;return textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,r){if("q"===i||"Q"===i)return textdecode(e,r=(r=r.replace(/_/g," ")).replace(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{r=atob(r)}catch(t){}return textdecode(e,r)}))}return""};var r=i(1)},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.PDFNetworkStream=void 0;var r=i(1),s=i(154);class NetworkManager{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=t;this.isHttp=/^https?:/i.test(t);this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null);this.withCredentials=e.withCredentials||!1;this.getXhr=e.getXhr||function NetworkManager_getXhr(){return new XMLHttpRequest};this.currXhrId=0;this.pendingRequests=Object.create(null)}requestRange(t,e,i){const r={begin:t,end:e};for(const t in i)r[t]=i[t];return this.request(r)}requestFull(t){return this.request(t)}request(t){const e=this.getXhr(),i=this.currXhrId++,r=this.pendingRequests[i]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const t in this.httpHeaders){const i=this.httpHeaders[t];void 0!==i&&e.setRequestHeader(t,i)}if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);r.expectedStatus=206}else r.expectedStatus=200;e.responseType="arraybuffer";t.onError&&(e.onerror=function(i){t.onError(e.status)});e.onreadystatechange=this.onStateChange.bind(this,i);e.onprogress=this.onProgress.bind(this,i);r.onHeadersReceived=t.onHeadersReceived;r.onDone=t.onDone;r.onError=t.onError;r.onProgress=t.onProgress;e.send(null);return i}onProgress(t,e){var i;const r=this.pendingRequests[t];r&&(null===(i=r.onProgress)||void 0===i||i.call(r,e))}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived){i.onHeadersReceived();delete i.onHeadersReceived}if(4!==s.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===s.status&&this.isHttp){var a;null===(a=i.onError)||void 0===a||a.call(i,s.status);return}const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus){var o;null===(o=i.onError)||void 0===o||o.call(i,s.status);return}const l=function getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:(0,r.stringToBytes)(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);i.onDone({begin:parseInt(e[1],10),chunk:l})}else if(l)i.onDone({begin:0,chunk:l});else{var c;null===(c=i.onError)||void 0===c||c.call(i,s.status)}}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}e.PDFNetworkStream=class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials});this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){(0,r.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const i=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);i.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){var e;null===(e=this._fullRequestReader)||void 0===e||e.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;const i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url;this._fullRequestId=t.requestFull(i);this._headersReceivedCapability=(0,r.createPromiseCapability)();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),getResponseHeader=t=>e.getResponseHeader(t),{allowRangeRequests:i,suggestedLength:r}=(0,s.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});i&&(this._isRangeSupported=!0);this._contentLength=r||this._contentLength;this._filename=(0,s.extractFilenameFromHeader)(getResponseHeader);this._isRangeSupported&&this._manager.abortRequest(t);this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=(0,s.createResponseStatusError)(t,this._url);this._headersReceivedCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){var e;null===(e=this.onProgress)||void 0===e||e.call(this,{loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,r.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersReceivedCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,i){this._manager=t;const r={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url;this._requestId=t.requestRange(e,i,r);this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_close(){var t;null===(t=this.onClosed)||void 0===t||t.call(this,this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError=(0,s.createResponseStatusError)(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){if(!this.isStreamingSupported){var e;null===(e=this.onProgress)||void 0===e||e.call(this,{loaded:t.loaded})}}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,r.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.PDFFetchStream=void 0;var r=i(1),s=i(154);function createFetchOptions(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function createHeaders(t){const e=new Headers;for(const i in t){const r=t[i];void 0!==r&&e.append(i,r)}return e}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;(0,r.warn)(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}e.PDFFetchStream=class PDFFetchStream{constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return(null===(t=this._fullRequestReader)||void 0===t?void 0:t._loaded)??0}getFullReader(){(0,r.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){var e;null===(e=this._fullRequestReader)||void 0===e||e.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=(0,r.createPromiseCapability)();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._headers=createHeaders(this._stream.httpHeaders);const i=e.url;fetch(i,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,s.validateResponseStatus)(t.status))throw(0,s.createResponseStatusError)(t.status,i);this._reader=t.body.getReader();this._headersCapability.resolve();const getResponseHeader=e=>t.headers.get(e),{allowRangeRequests:e,suggestedLength:a}=(0,s.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=e;this._contentLength=a||this._contentLength;this._filename=(0,s.extractFilenameFromHeader)(getResponseHeader);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new r.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;await this._headersCapability.promise;const{value:e,done:i}=await this._reader.read();if(i)return{value:e,done:i};this._loaded+=e.byteLength;null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(e),done:!1}}cancel(t){var e;null===(e=this._reader)||void 0===e||e.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,i){this._stream=t;this._reader=null;this._loaded=0;const a=t.source;this._withCredentials=a.withCredentials||!1;this._readCapability=(0,r.createPromiseCapability)();this._isStreamingSupported=!a.disableStream;this._abortController=new AbortController;this._headers=createHeaders(this._stream.httpHeaders);this._headers.append("Range",`bytes=${e}-${i-1}`);const n=a.url;fetch(n,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,s.validateResponseStatus)(t.status))throw(0,s.createResponseStatusError)(t.status,n);this._readCapability.resolve();this._reader=t.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;await this._readCapability.promise;const{value:e,done:i}=await this._reader.read();if(i)return{value:e,done:i};this._loaded+=e.byteLength;null===(t=this.onProgress)||void 0===t||t.call(this,{loaded:this._loaded});return{value:getArrayBuffer(e),done:!1}}cancel(t){var e;null===(e=this._reader)||void 0===e||e.cancel(t);this._abortController.abort()}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.TextLayerRenderTask=void 0;e.renderTextLayer=function renderTextLayer(t){if(!t.textContentSource&&(t.textContent||t.textContentStream)){(0,s.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead.");t.textContentSource=t.textContent||t.textContentStream}const e=new TextLayerRenderTask(t);e._render();return e};e.updateTextLayer=function updateTextLayer(t){let{container:e,viewport:i,textDivs:r,textDivProperties:a,isOffscreenCanvasSupported:n,mustRotate:o=!0,mustRescale:l=!0}=t;o&&(0,s.setLayerDimensions)(e,{rotation:i.rotation});if(l){const t=getCtx(0,n),e={prevFontSize:null,prevFontFamily:null,div:null,scale:i.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:t};for(const t of r){e.properties=a.get(t);e.div=t;layout(e)}}};var r=i(1),s=i(139);const a=1e5,n=30,o=.8,l=new Map;function getCtx(t,e){let i;if(e&&r.FeatureTest.isOffscreenCanvasSupported)i=new OffscreenCanvas(t,t).getContext("2d",{alpha:!1});else{const e=document.createElement("canvas");e.width=e.height=t;i=e.getContext("2d",{alpha:!1})}return i}function appendText(t,e,i){const s=document.createElement("span"),a={angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,fontSize:0};t._textDivs.push(s);const c=r.Util.transform(t._transform,e.transform);let d=Math.atan2(c[1],c[0]);const h=i[e.fontName];h.vertical&&(d+=Math.PI/2);const u=Math.hypot(c[2],c[3]),p=u*function getAscent(t,e){const i=l.get(t);if(i)return i;const r=getCtx(n,e);r.font=`${n}px ${t}`;const s=r.measureText("");let a=s.fontBoundingBoxAscent,c=Math.abs(s.fontBoundingBoxDescent);if(a){const e=a/(a+c);l.set(t,e);r.canvas.width=r.canvas.height=0;return e}r.strokeStyle="red";r.clearRect(0,0,n,n);r.strokeText("g",0,0);let d=r.getImageData(0,0,n,n).data;c=0;for(let t=d.length-1-3;t>=0;t-=4)if(d[t]>0){c=Math.ceil(t/4/n);break}r.clearRect(0,0,n,n);r.strokeText("A",0,n);d=r.getImageData(0,0,n,n).data;a=0;for(let t=0,e=d.length;t<e;t+=4)if(d[t]>0){a=n-Math.floor(t/4/n);break}r.canvas.width=r.canvas.height=0;if(a){const e=a/(a+c);l.set(t,e);return e}l.set(t,o);return o}(h.fontFamily,t._isOffscreenCanvasSupported);let f,g;if(0===d){f=c[4];g=c[5]-p}else{f=c[4]+p*Math.sin(d);g=c[5]-p*Math.cos(d)}const m="calc(var(--scale-factor)*",v=s.style;if(t._container===t._rootContainer){v.left=`${(100*f/t._pageWidth).toFixed(2)}%`;v.top=`${(100*g/t._pageHeight).toFixed(2)}%`}else{v.left=`${m}${f.toFixed(2)}px)`;v.top=`${m}${g.toFixed(2)}px)`}v.fontSize=`${m}${u.toFixed(2)}px)`;v.fontFamily=h.fontFamily;a.fontSize=u;s.setAttribute("role","presentation");s.textContent=e.str;s.dir=e.dir;t._fontInspectorEnabled&&(s.dataset.fontName=e.fontName);0!==d&&(a.angle=d*(180/Math.PI));let _=!1;if(e.str.length>1)_=!0;else if(" "!==e.str&&e.transform[0]!==e.transform[3]){const t=Math.abs(e.transform[0]),i=Math.abs(e.transform[3]);t!==i&&Math.max(t,i)/Math.min(t,i)>1.5&&(_=!0)}_&&(a.canvasWidth=h.vertical?e.height:e.width);t._textDivProperties.set(s,a);t._isReadableStream&&t._layoutText(s)}function layout(t){const{div:e,scale:i,properties:r,ctx:s,prevFontSize:a,prevFontFamily:n}=t,{style:o}=e;let l="";if(0!==r.canvasWidth&&r.hasText){const{fontFamily:c}=o,{canvasWidth:d,fontSize:h}=r;if(a!==h||n!==c){s.font=`${h*i}px ${c}`;t.prevFontSize=h;t.prevFontFamily=c}const{width:u}=s.measureText(e.textContent);u>0&&(l=`scaleX(${d*i/u})`)}0!==r.angle&&(l=`rotate(${r.angle}deg) ${l}`);l.length>0&&(o.transform=l)}class TextLayerRenderTask{constructor(t){var e;let{textContentSource:i,container:a,viewport:n,textDivs:o,textDivProperties:l,textContentItemsStr:c,isOffscreenCanvasSupported:d}=t;this._textContentSource=i;this._isReadableStream=i instanceof ReadableStream;this._container=this._rootContainer=a;this._textDivs=o||[];this._textContentItemsStr=c||[];this._isOffscreenCanvasSupported=d;this._fontInspectorEnabled=!(null===(e=globalThis.FontInspector)||void 0===e||!e.enabled);this._reader=null;this._textDivProperties=l||new WeakMap;this._canceled=!1;this._capability=(0,r.createPromiseCapability)();this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:n.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:getCtx(0,d)};const{pageWidth:h,pageHeight:u,pageX:p,pageY:f}=n.rawDims;this._transform=[1,0,0,-1,-p,f+u];this._pageWidth=h;this._pageHeight=u;(0,s.setLayerDimensions)(a,n);this._capability.promise.finally((()=>{this._layoutTextParams=null})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0;if(this._reader){this._reader.cancel(new r.AbortException("TextLayer task cancelled.")).catch((()=>{}));this._reader=null}this._capability.reject(new r.AbortException("TextLayer task cancelled."))}_processItems(t,e){for(const i of t)if(void 0!==i.str){this._textContentItemsStr.push(i.str);appendText(this,i,e)}else if("beginMarkedContentProps"===i.type||"beginMarkedContent"===i.type){const t=this._container;this._container=document.createElement("span");this._container.classList.add("markedContent");null!==i.id&&this._container.setAttribute("id",`${i.id}`);t.append(this._container)}else"endMarkedContent"===i.type&&(this._container=this._container.parentNode)}_layoutText(t){const e=this._layoutTextParams.properties=this._textDivProperties.get(t);this._layoutTextParams.div=t;layout(this._layoutTextParams);e.hasText&&this._container.append(t);if(e.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this._container.append(t)}}_render(){const t=(0,r.createPromiseCapability)();let e=Object.create(null);if(this._isReadableStream){const pump=()=>{this._reader.read().then((i=>{let{value:r,done:s}=i;if(s)t.resolve();else{Object.assign(e,r.styles);this._processItems(r.items,e);pump()}}),t.reject)};this._reader=this._textContentSource.getReader();pump()}else{if(!this._textContentSource)throw new Error('No "textContentSource" parameter specified.');{const{items:e,styles:i}=this._textContentSource;this._processItems(e,i);t.resolve()}}t.promise.then((()=>{e=null;!function render(t){if(t._canceled)return;const e=t._textDivs,i=t._capability;if(e.length>a)i.resolve();else{if(!t._isReadableStream)for(const i of e)t._layoutText(i);i.resolve()}}(this)}),this._capability.reject)}}e.TextLayerRenderTask=TextLayerRenderTask},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationEditorLayer=void 0;var r=i(1),s=i(138),a=i(160),n=i(161),o=i(139);function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}function _classPrivateFieldGet(t,e){return function _classApplyDescriptorGet(t,e){if(e.get)return e.get.call(t);return e.value}(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateFieldSet(t,e,i){!function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}var l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,g=new WeakMap,m=new WeakSet,v=new WeakSet,_=new WeakSet,b=new WeakSet;class AnnotationEditorLayer{constructor(t){_classPrivateMethodInitSpec(this,b);_classPrivateMethodInitSpec(this,_);_classPrivateMethodInitSpec(this,v);_classPrivateMethodInitSpec(this,m);_classPrivateFieldInitSpec(this,l,{writable:!0,value:void 0});_classPrivateFieldInitSpec(this,c,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,d,{writable:!0,value:this.pointerup.bind(this)});_classPrivateFieldInitSpec(this,h,{writable:!0,value:this.pointerdown.bind(this)});_classPrivateFieldInitSpec(this,u,{writable:!0,value:new Map});_classPrivateFieldInitSpec(this,p,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,f,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,g,{writable:!0,value:void 0});if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;a.FreeTextEditor.initialize(t.l10n);n.InkEditor.initialize(t.l10n)}t.uiManager.registerEditorTypes([a.FreeTextEditor,n.InkEditor]);_classPrivateFieldSet(this,g,t.uiManager);this.pageIndex=t.pageIndex;this.div=t.div;_classPrivateFieldSet(this,l,t.accessibilityManager);_classPrivateFieldGet(this,g).addLayer(this)}get isEmpty(){return 0===_classPrivateFieldGet(this,u).size}updateToolbar(t){_classPrivateFieldGet(this,g).updateToolbar(t)}updateMode(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_classPrivateFieldGet(this,g).getMode();_classPrivateMethodGet(this,b,_cleanup2).call(this);if(t===r.AnnotationEditorType.INK){this.addInkEditorIfNeeded(!1);this.disableClick()}else this.enableClick();_classPrivateFieldGet(this,g).unselectAll();if(t!==r.AnnotationEditorType.NONE){this.div.classList.toggle("freeTextEditing",t===r.AnnotationEditorType.FREETEXT);this.div.classList.toggle("inkEditing",t===r.AnnotationEditorType.INK);this.div.hidden=!1}}addInkEditorIfNeeded(t){if(!t&&_classPrivateFieldGet(this,g).getMode()!==r.AnnotationEditorType.INK)return;if(!t)for(const t of _classPrivateFieldGet(this,u).values())if(t.isEmpty()){t.setInBackground();return}_classPrivateMethodGet(this,_,_createAndAddNewEditor2).call(this,{offsetX:0,offsetY:0}).setInBackground()}setEditingState(t){_classPrivateFieldGet(this,g).setEditingState(t)}addCommands(t){_classPrivateFieldGet(this,g).addCommands(t)}enable(){this.div.style.pointerEvents="auto";for(const t of _classPrivateFieldGet(this,u).values())t.enableEditing()}disable(){this.div.style.pointerEvents="none";for(const t of _classPrivateFieldGet(this,u).values())t.disableEditing();_classPrivateMethodGet(this,b,_cleanup2).call(this);this.isEmpty&&(this.div.hidden=!0)}setActiveEditor(t){_classPrivateFieldGet(this,g).getActive()!==t&&_classPrivateFieldGet(this,g).setActiveEditor(t)}enableClick(){this.div.addEventListener("pointerdown",_classPrivateFieldGet(this,h));this.div.addEventListener("pointerup",_classPrivateFieldGet(this,d))}disableClick(){this.div.removeEventListener("pointerdown",_classPrivateFieldGet(this,h));this.div.removeEventListener("pointerup",_classPrivateFieldGet(this,d))}attach(t){_classPrivateFieldGet(this,u).set(t.id,t)}detach(t){var e;_classPrivateFieldGet(this,u).delete(t.id);null===(e=_classPrivateFieldGet(this,l))||void 0===e||e.removePointerInTextLayer(t.contentDiv)}remove(t){_classPrivateFieldGet(this,g).removeEditor(t);this.detach(t);t.div.style.display="none";setTimeout((()=>{t.div.style.display="";t.div.remove();t.isAttachedToDOM=!1;document.activeElement===document.body&&_classPrivateFieldGet(this,g).focusMainContainer()}),0);_classPrivateFieldGet(this,f)||this.addInkEditorIfNeeded(!1)}add(t){_classPrivateMethodGet(this,m,_changeParent2).call(this,t);_classPrivateFieldGet(this,g).addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}this.moveEditorInDOM(t);t.onceAdded();_classPrivateFieldGet(this,g).addToAnnotationStorage(t)}moveEditorInDOM(t){var e;null===(e=_classPrivateFieldGet(this,l))||void 0===e||e.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?t.rebuild():this.add(t)}addANewEditor(t){this.addCommands({cmd:()=>{this.addOrRebuild(t)},undo:()=>{t.remove()},mustExec:!0})}addUndoableEditor(t){this.addCommands({cmd:()=>{this.addOrRebuild(t)},undo:()=>{t.remove()},mustExec:!1})}getNextId(){return _classPrivateFieldGet(this,g).getId()}deserialize(t){switch(t.annotationType){case r.AnnotationEditorType.FREETEXT:return a.FreeTextEditor.deserialize(t,this,_classPrivateFieldGet(this,g));case r.AnnotationEditorType.INK:return n.InkEditor.deserialize(t,this,_classPrivateFieldGet(this,g))}return null}setSelected(t){_classPrivateFieldGet(this,g).setSelected(t)}toggleSelected(t){_classPrivateFieldGet(this,g).toggleSelected(t)}isSelected(t){return _classPrivateFieldGet(this,g).isSelected(t)}unselect(t){_classPrivateFieldGet(this,g).unselect(t)}pointerup(t){const{isMac:e}=r.FeatureTest.platform;if(!(0!==t.button||t.ctrlKey&&e)&&t.target===this.div&&_classPrivateFieldGet(this,p)){_classPrivateFieldSet(this,p,!1);_classPrivateFieldGet(this,c)?_classPrivateMethodGet(this,_,_createAndAddNewEditor2).call(this,t):_classPrivateFieldSet(this,c,!0)}}pointerdown(t){const{isMac:e}=r.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;_classPrivateFieldSet(this,p,!0);const i=_classPrivateFieldGet(this,g).getActive();_classPrivateFieldSet(this,c,!i||i.isEmpty())}drop(t){const e=t.dataTransfer.getData("text/plain"),i=_classPrivateFieldGet(this,g).getEditor(e);if(!i)return;t.preventDefault();t.dataTransfer.dropEffect="move";_classPrivateMethodGet(this,m,_changeParent2).call(this,i);const r=this.div.getBoundingClientRect(),s=t.clientX-r.x,a=t.clientY-r.y;i.translate(s-i.startX,a-i.startY);this.moveEditorInDOM(i);i.div.focus()}dragover(t){t.preventDefault()}destroy(){var t;(null===(t=_classPrivateFieldGet(this,g).getActive())||void 0===t?void 0:t.parent)===this&&_classPrivateFieldGet(this,g).setActiveEditor(null);for(const t of _classPrivateFieldGet(this,u).values()){var e;null===(e=_classPrivateFieldGet(this,l))||void 0===e||e.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;_classPrivateFieldGet(this,u).clear();_classPrivateFieldGet(this,g).removeLayer(this)}render(t){let{viewport:e}=t;this.viewport=e;(0,o.setLayerDimensions)(this.div,e);(0,s.bindEvents)(this,this.div,["dragover","drop"]);for(const t of _classPrivateFieldGet(this,g).getEditors(this.pageIndex))this.add(t);this.updateMode()}update(t){let{viewport:e}=t;_classPrivateFieldGet(this,g).commitOrRemove();this.viewport=e;(0,o.setLayerDimensions)(this.div,{rotation:e.rotation});this.updateMode()}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}}e.AnnotationEditorLayer=AnnotationEditorLayer;function _changeParent2(t){var e;if(t.parent!==this){this.attach(t);null===(e=t.parent)||void 0===e||e.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}function _createNewEditor2(t){switch(_classPrivateFieldGet(this,g).getMode()){case r.AnnotationEditorType.FREETEXT:return new a.FreeTextEditor(t);case r.AnnotationEditorType.INK:return new n.InkEditor(t)}return null}function _createAndAddNewEditor2(t){const e=this.getNextId(),i=_classPrivateMethodGet(this,v,_createNewEditor2).call(this,{parent:this,id:e,x:t.offsetX,y:t.offsetY,uiManager:_classPrivateFieldGet(this,g)});i&&this.add(i);return i}function _cleanup2(){_classPrivateFieldSet(this,f,!0);for(const t of _classPrivateFieldGet(this,u).values())t.isEmpty()&&t.remove();_classPrivateFieldSet(this,f,!1)}!function _defineProperty(t,e,i){(e=function _toPropertyKey(t){var e=function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i;return t}(AnnotationEditorLayer,"_initialized",!1)},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.FreeTextEditor=void 0;var r=i(1),s=i(138),a=i(137);function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}function _defineProperty(t,e,i){(e=function _toPropertyKey(t){var e=function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i;return t}function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldGet(t,e){return function _classApplyDescriptorGet(t,e){if(e.get)return e.get.call(t);return e.value}(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}function _classPrivateFieldSet(t,e,i){!function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}var n=new WeakMap,o=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,g=new WeakSet,m=new WeakSet,v=new WeakSet,_=new WeakSet;class FreeTextEditor extends a.AnnotationEditor{constructor(t){super({...t,name:"freeTextEditor"});_classPrivateMethodInitSpec(this,_);_classPrivateMethodInitSpec(this,v);_classPrivateMethodInitSpec(this,m);_classPrivateMethodInitSpec(this,g);_classPrivateFieldInitSpec(this,n,{writable:!0,value:this.editorDivBlur.bind(this)});_classPrivateFieldInitSpec(this,o,{writable:!0,value:this.editorDivFocus.bind(this)});_classPrivateFieldInitSpec(this,l,{writable:!0,value:this.editorDivInput.bind(this)});_classPrivateFieldInitSpec(this,c,{writable:!0,value:this.editorDivKeydown.bind(this)});_classPrivateFieldInitSpec(this,d,{writable:!0,value:void 0});_classPrivateFieldInitSpec(this,h,{writable:!0,value:""});_classPrivateFieldInitSpec(this,u,{writable:!0,value:`${this.id}-editor`});_classPrivateFieldInitSpec(this,p,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,f,{writable:!0,value:void 0});_classPrivateFieldSet(this,d,t.color||FreeTextEditor._defaultColor||a.AnnotationEditor._defaultLineColor);_classPrivateFieldSet(this,f,t.fontSize||FreeTextEditor._defaultFontSize)}static initialize(t){this._l10nPromise=new Map(["free_text2_default_content","editor_free_text2_aria_label"].map((e=>[e,t.get(e)])));const e=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(e.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case r.AnnotationEditorParamsType.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case r.AnnotationEditorParamsType.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case r.AnnotationEditorParamsType.FREETEXT_SIZE:_classPrivateMethodGet(this,g,_updateFontSize2).call(this,e);break;case r.AnnotationEditorParamsType.FREETEXT_COLOR:_classPrivateMethodGet(this,m,_updateColor2).call(this,e)}}static get defaultPropertiesToUpdate(){return[[r.AnnotationEditorParamsType.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[r.AnnotationEditorParamsType.FREETEXT_COLOR,FreeTextEditor._defaultColor||a.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[r.AnnotationEditorParamsType.FREETEXT_SIZE,_classPrivateFieldGet(this,f)],[r.AnnotationEditorParamsType.FREETEXT_COLOR,_classPrivateFieldGet(this,d)]]}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+_classPrivateFieldGet(this,f))*t]}rebuild(){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}enableEditMode(){if(!this.isInEditMode()){this.parent.setEditingState(!1);this.parent.updateToolbar(r.AnnotationEditorType.FREETEXT);super.enableEditMode();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this.div.draggable=!1;this.div.removeAttribute("aria-activedescendant");this.editorDiv.addEventListener("keydown",_classPrivateFieldGet(this,c));this.editorDiv.addEventListener("focus",_classPrivateFieldGet(this,o));this.editorDiv.addEventListener("blur",_classPrivateFieldGet(this,n));this.editorDiv.addEventListener("input",_classPrivateFieldGet(this,l))}}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",_classPrivateFieldGet(this,u));this.div.draggable=!0;this.editorDiv.removeEventListener("keydown",_classPrivateFieldGet(this,c));this.editorDiv.removeEventListener("focus",_classPrivateFieldGet(this,o));this.editorDiv.removeEventListener("blur",_classPrivateFieldGet(this,n));this.editorDiv.removeEventListener("input",_classPrivateFieldGet(this,l));this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freeTextEditing")}}focusin(t){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}onceAdded(){if(!this.width){this.enableEditMode();this.editorDiv.focus()}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;this.parent.setEditingState(!0);this.parent.div.classList.add("freeTextEditing");super.remove()}commit(){if(this.isInEditMode()){super.commit();if(!_classPrivateFieldGet(this,p)){_classPrivateFieldSet(this,p,!0);this.parent.addUndoableEditor(this)}this.disableEditMode();_classPrivateFieldSet(this,h,_classPrivateMethodGet(this,v,_extractText2).call(this).trimEnd());_classPrivateMethodGet(this,_,_setEditorDimensions2).call(this)}}shouldGetKeyboardEvents(){return this.isInEditMode()}dblclick(t){this.enableEditMode();this.editorDiv.focus()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enableEditMode();this.editorDiv.focus()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",_classPrivateFieldGet(this,u));this.enableEditing();FreeTextEditor._l10nPromise.get("editor_free_text2_aria_label").then((t=>{var e;return null===(e=this.editorDiv)||void 0===e?void 0:e.setAttribute("aria-label",t)}));FreeTextEditor._l10nPromise.get("free_text2_default_content").then((t=>{var e;return null===(e=this.editorDiv)||void 0===e?void 0:e.setAttribute("default-content",t)}));this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;i.fontSize=`calc(${_classPrivateFieldGet(this,f)}px * var(--scale-factor))`;i.color=_classPrivateFieldGet(this,d);this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);(0,s.bindEvents)(this,this.div,["dblclick","keydown"]);if(this.width){const[i,r]=this.parentDimensions;this.setAt(t*i,e*r,this.width*i,this.height*r);for(const t of _classPrivateFieldGet(this,h).split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}this.div.draggable=!0;this.editorDiv.contentEditable=!1}else{this.div.draggable=!1;this.editorDiv.contentEditable=!0}return this.div}get contentDiv(){return this.editorDiv}static deserialize(t,e,i){const s=super.deserialize(t,e,i);_classPrivateFieldSet(s,f,t.fontSize);_classPrivateFieldSet(s,d,r.Util.makeHexColor(...t.color));_classPrivateFieldSet(s,h,t.value);return s}serialize(){if(this.isEmpty())return null;const t=FreeTextEditor._internalPadding*this.parentScale,e=this.getRect(t,t),i=a.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:_classPrivateFieldGet(this,d));return{annotationType:r.AnnotationEditorType.FREETEXT,color:i,fontSize:_classPrivateFieldGet(this,f),value:_classPrivateFieldGet(this,h),pageIndex:this.pageIndex,rect:e,rotation:this.rotation}}}e.FreeTextEditor=FreeTextEditor;function _updateFontSize2(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`;this.translate(0,-(t-_classPrivateFieldGet(this,f))*this.parentScale);_classPrivateFieldSet(this,f,t);_classPrivateMethodGet(this,_,_setEditorDimensions2).call(this)},e=_classPrivateFieldGet(this,f);this.addCommands({cmd:()=>{setFontsize(t)},undo:()=>{setFontsize(e)},mustExec:!0,type:r.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}function _updateColor2(t){const e=_classPrivateFieldGet(this,d);this.addCommands({cmd:()=>{_classPrivateFieldSet(this,d,this.editorDiv.style.color=t)},undo:()=>{_classPrivateFieldSet(this,d,this.editorDiv.style.color=e)},mustExec:!0,type:r.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}function _extractText2(){const t=this.editorDiv.getElementsByTagName("div");if(0===t.length)return this.editorDiv.innerText;const e=[];for(const i of t)e.push(i.innerText.replace(/\r\n?|\n/,""));return e.join("\n")}function _setEditorDimensions2(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,r=e.style.display;e.style.display="hidden";t.div.append(this.div);i=e.getBoundingClientRect();e.remove();e.style.display=r}this.width=i.width/t;this.height=i.height/e}_defineProperty(FreeTextEditor,"_freeTextDefaultContent","");_defineProperty(FreeTextEditor,"_l10nPromise",void 0);_defineProperty(FreeTextEditor,"_internalPadding",0);_defineProperty(FreeTextEditor,"_defaultColor",null);_defineProperty(FreeTextEditor,"_defaultFontSize",10);_defineProperty(FreeTextEditor,"_keyboardManager",new s.KeyboardManager([[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],FreeTextEditor.prototype.commitOrRemove]]));_defineProperty(FreeTextEditor,"_type","freetext")},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.InkEditor=void 0;Object.defineProperty(e,"fitCurve",{enumerable:!0,get:function(){return a.fitCurve}});var r=i(1),s=i(137),a=i(162),n=i(138);function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e);e.add(t)}function _defineProperty(t,e,i){(e=function _toPropertyKey(t){var e=function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i;return t}function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e);e.set(t,i)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classStaticPrivateMethodGet(t,e,i){!function _classCheckPrivateStaticAccess(t,e){if(t!==e)throw new TypeError("Private static access of wrong provenance")}(t,e);return i}function _classPrivateFieldSet(t,e,i){!function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}(t,_classExtractFieldDescriptor(t,e,"set"),i);return i}function _classPrivateFieldGet(t,e){return function _classApplyDescriptorGet(t,e){if(e.get)return e.get.call(t);return e.value}(t,_classExtractFieldDescriptor(t,e,"get"))}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}const o=16,l=100;var c=new WeakMap,d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,g=new WeakMap,m=new WeakMap,v=new WeakMap,_=new WeakMap,b=new WeakMap,y=new WeakMap,A=new WeakMap,S=new WeakMap,P=new WeakSet,x=new WeakSet,w=new WeakSet,E=new WeakSet,C=new WeakSet,k=new WeakSet,M=new WeakSet,T=new WeakSet,R=new WeakSet,I=new WeakSet,D=new WeakSet,O=new WeakSet,L=new WeakSet,G=new WeakSet,N=new WeakSet,j=new WeakSet,U=new WeakSet,B=new WeakSet,W=new WeakSet,q=new WeakSet,z=new WeakSet,H=new WeakSet;class InkEditor extends s.AnnotationEditor{constructor(t){super({...t,name:"inkEditor"});_classPrivateMethodInitSpec(this,H);_classPrivateMethodInitSpec(this,z);_classPrivateMethodInitSpec(this,q);_classPrivateMethodInitSpec(this,W);_classPrivateMethodInitSpec(this,B);_classPrivateMethodInitSpec(this,U);_classPrivateMethodInitSpec(this,j);_classPrivateMethodInitSpec(this,N);_classPrivateMethodInitSpec(this,G);_classPrivateMethodInitSpec(this,L);_classPrivateMethodInitSpec(this,O);_classPrivateMethodInitSpec(this,D);_classPrivateMethodInitSpec(this,I);_classPrivateMethodInitSpec(this,R);_classPrivateMethodInitSpec(this,T);_classPrivateMethodInitSpec(this,M);_classPrivateMethodInitSpec(this,k);_classPrivateMethodInitSpec(this,C);_classPrivateMethodInitSpec(this,E);_classPrivateMethodInitSpec(this,w);_classPrivateMethodInitSpec(this,x);_classPrivateMethodInitSpec(this,P);_classPrivateFieldInitSpec(this,c,{writable:!0,value:0});_classPrivateFieldInitSpec(this,d,{writable:!0,value:0});_classPrivateFieldInitSpec(this,h,{writable:!0,value:0});_classPrivateFieldInitSpec(this,u,{writable:!0,value:this.canvasPointermove.bind(this)});_classPrivateFieldInitSpec(this,p,{writable:!0,value:this.canvasPointerleave.bind(this)});_classPrivateFieldInitSpec(this,f,{writable:!0,value:this.canvasPointerup.bind(this)});_classPrivateFieldInitSpec(this,g,{writable:!0,value:this.canvasPointerdown.bind(this)});_classPrivateFieldInitSpec(this,m,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,v,{writable:!0,value:!1});_classPrivateFieldInitSpec(this,_,{writable:!0,value:null});_classPrivateFieldInitSpec(this,b,{writable:!0,value:null});_classPrivateFieldInitSpec(this,y,{writable:!0,value:0});_classPrivateFieldInitSpec(this,A,{writable:!0,value:0});_classPrivateFieldInitSpec(this,S,{writable:!0,value:null});this.color=t.color||null;this.thickness=t.thickness||null;this.opacity=t.opacity||null;this.paths=[];this.bezierPath2D=[];this.currentPath=[];this.scaleFactor=1;this.translationX=this.translationY=0;this.x=0;this.y=0}static initialize(t){this._l10nPromise=new Map(["editor_ink_canvas_aria_label","editor_ink2_aria_label"].map((e=>[e,t.get(e)])))}static updateDefaultParams(t,e){switch(t){case r.AnnotationEditorParamsType.INK_THICKNESS:InkEditor._defaultThickness=e;break;case r.AnnotationEditorParamsType.INK_COLOR:InkEditor._defaultColor=e;break;case r.AnnotationEditorParamsType.INK_OPACITY:InkEditor._defaultOpacity=e/100}}updateParams(t,e){switch(t){case r.AnnotationEditorParamsType.INK_THICKNESS:_classPrivateMethodGet(this,P,_updateThickness2).call(this,e);break;case r.AnnotationEditorParamsType.INK_COLOR:_classPrivateMethodGet(this,x,_updateColor2).call(this,e);break;case r.AnnotationEditorParamsType.INK_OPACITY:_classPrivateMethodGet(this,w,_updateOpacity2).call(this,e)}}static get defaultPropertiesToUpdate(){return[[r.AnnotationEditorParamsType.INK_THICKNESS,InkEditor._defaultThickness],[r.AnnotationEditorParamsType.INK_COLOR,InkEditor._defaultColor||s.AnnotationEditor._defaultLineColor],[r.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*InkEditor._defaultOpacity)]]}get propertiesToUpdate(){return[[r.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||InkEditor._defaultThickness],[r.AnnotationEditorParamsType.INK_COLOR,this.color||InkEditor._defaultColor||s.AnnotationEditor._defaultLineColor],[r.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??InkEditor._defaultOpacity))]]}rebuild(){super.rebuild();if(null!==this.div){if(!this.canvas){_classPrivateMethodGet(this,D,_createCanvas2).call(this);_classPrivateMethodGet(this,O,_createObserver2).call(this)}if(!this.isAttachedToDOM){this.parent.add(this);_classPrivateMethodGet(this,L,_setCanvasDims2).call(this)}_classPrivateMethodGet(this,z,_fitToContent2).call(this)}}remove(){if(null!==this.canvas){this.isEmpty()||this.commit();this.canvas.width=this.canvas.height=0;this.canvas.remove();this.canvas=null;_classPrivateFieldGet(this,b).disconnect();_classPrivateFieldSet(this,b,null);super.remove()}}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this);super.setParent(t)}onScaleChanging(){const[t,e]=this.parentDimensions,i=this.width*t,r=this.height*e;this.setDimensions(i,r)}enableEditMode(){if(!_classPrivateFieldGet(this,m)&&null!==this.canvas){super.enableEditMode();this.div.draggable=!1;this.canvas.addEventListener("pointerdown",_classPrivateFieldGet(this,g));this.canvas.addEventListener("pointerup",_classPrivateFieldGet(this,f))}}disableEditMode(){if(this.isInEditMode()&&null!==this.canvas){super.disableEditMode();this.div.draggable=!this.isEmpty();this.div.classList.remove("editing");this.canvas.removeEventListener("pointerdown",_classPrivateFieldGet(this,g));this.canvas.removeEventListener("pointerup",_classPrivateFieldGet(this,f))}}onceAdded(){this.div.draggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}commit(){if(!_classPrivateFieldGet(this,m)){super.commit();this.isEditing=!1;this.disableEditMode();this.setInForeground();_classPrivateFieldSet(this,m,!0);this.div.classList.add("disabled");_classPrivateMethodGet(this,z,_fitToContent2).call(this,!0);this.parent.addInkEditorIfNeeded(!0);this.parent.moveEditorInDOM(this);this.div.focus({preventScroll:!0})}}focusin(t){super.focusin(t);this.enableEditMode()}canvasPointerdown(t){if(0===t.button&&this.isInEditMode()&&!_classPrivateFieldGet(this,m)){this.setInForeground();"mouse"!==t.type&&this.div.focus();t.stopPropagation();this.canvas.addEventListener("pointerleave",_classPrivateFieldGet(this,p));this.canvas.addEventListener("pointermove",_classPrivateFieldGet(this,u));_classPrivateMethodGet(this,k,_startDrawing2).call(this,t.offsetX,t.offsetY)}}canvasPointermove(t){t.stopPropagation();_classPrivateMethodGet(this,M,_draw2).call(this,t.offsetX,t.offsetY)}canvasPointerup(t){if(0===t.button&&this.isInEditMode()&&0!==this.currentPath.length){t.stopPropagation();_classPrivateMethodGet(this,I,_endDrawing2).call(this,t);this.setInBackground()}}canvasPointerleave(t){_classPrivateMethodGet(this,I,_endDrawing2).call(this,t);this.setInBackground()}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();InkEditor._l10nPromise.get("editor_ink2_aria_label").then((t=>{var e;return null===(e=this.div)||void 0===e?void 0:e.setAttribute("aria-label",t)}));const[i,r,s,a]=_classPrivateMethodGet(this,E,_getInitialBBox2).call(this);this.setAt(i,r,0,0);this.setDims(s,a);_classPrivateMethodGet(this,D,_createCanvas2).call(this);if(this.width){const[i,r]=this.parentDimensions;this.setAt(t*i,e*r,this.width*i,this.height*r);_classPrivateFieldSet(this,v,!0);_classPrivateMethodGet(this,L,_setCanvasDims2).call(this);this.setDims(this.width*i,this.height*r);_classPrivateMethodGet(this,R,_redraw2).call(this);_classPrivateMethodGet(this,H,_setMinDims2).call(this);this.div.classList.add("disabled")}else{this.div.classList.add("editing");this.enableEditMode()}_classPrivateMethodGet(this,O,_createObserver2).call(this);return this.div}setDimensions(t,e){const i=Math.round(t),r=Math.round(e);if(_classPrivateFieldGet(this,y)===i&&_classPrivateFieldGet(this,A)===r)return;_classPrivateFieldSet(this,y,i);_classPrivateFieldSet(this,A,r);this.canvas.style.visibility="hidden";if(_classPrivateFieldGet(this,c)&&Math.abs(_classPrivateFieldGet(this,c)-t/e)>.01){e=Math.ceil(t/_classPrivateFieldGet(this,c));this.setDims(t,e)}const[s,a]=this.parentDimensions;this.width=t/s;this.height=e/a;_classPrivateFieldGet(this,m)&&_classPrivateMethodGet(this,G,_setScaleFactor2).call(this,t,e);_classPrivateMethodGet(this,L,_setCanvasDims2).call(this);_classPrivateMethodGet(this,R,_redraw2).call(this);this.canvas.style.visibility="visible"}static deserialize(t,e,i){const s=super.deserialize(t,e,i);s.thickness=t.thickness;s.color=r.Util.makeHexColor(...t.color);s.opacity=t.opacity;const[a,n]=s.pageDimensions,l=s.width*a,u=s.height*n,p=s.parentScale,f=t.thickness/2;_classPrivateFieldSet(s,c,l/u);_classPrivateFieldSet(s,m,!0);_classPrivateFieldSet(s,y,Math.round(l));_classPrivateFieldSet(s,A,Math.round(u));for(const{bezier:e}of t.paths){const t=[];s.paths.push(t);let i=p*(e[0]-f),r=p*(u-e[1]-f);for(let s=2,a=e.length;s<a;s+=6){const a=p*(e[s]-f),n=p*(u-e[s+1]-f),o=p*(e[s+2]-f),l=p*(u-e[s+3]-f),c=p*(e[s+4]-f),d=p*(u-e[s+5]-f);t.push([[i,r],[a,n],[o,l],[c,d]]);i=c;r=d}const a=_classStaticPrivateMethodGet(this,InkEditor,_buildPath2D).call(this,t);s.bezierPath2D.push(a)}const g=_classPrivateMethodGet(s,W,_getBbox2).call(s);_classPrivateFieldSet(s,h,Math.max(o,g[2]-g[0]));_classPrivateFieldSet(s,d,Math.max(o,g[3]-g[1]));_classPrivateMethodGet(s,G,_setScaleFactor2).call(s,l,u);return s}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=this.rotation%180==0?t[3]-t[1]:t[2]-t[0],i=s.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:r.AnnotationEditorType.INK,color:i,thickness:this.thickness,opacity:this.opacity,paths:_classPrivateMethodGet(this,j,_serializePaths2).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,e),pageIndex:this.pageIndex,rect:t,rotation:this.rotation}}}e.InkEditor=InkEditor;function _updateThickness2(t){const e=this.thickness;this.addCommands({cmd:()=>{this.thickness=t;_classPrivateMethodGet(this,z,_fitToContent2).call(this)},undo:()=>{this.thickness=e;_classPrivateMethodGet(this,z,_fitToContent2).call(this)},mustExec:!0,type:r.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}function _updateColor2(t){const e=this.color;this.addCommands({cmd:()=>{this.color=t;_classPrivateMethodGet(this,R,_redraw2).call(this)},undo:()=>{this.color=e;_classPrivateMethodGet(this,R,_redraw2).call(this)},mustExec:!0,type:r.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}function _updateOpacity2(t){t/=100;const e=this.opacity;this.addCommands({cmd:()=>{this.opacity=t;_classPrivateMethodGet(this,R,_redraw2).call(this)},undo:()=>{this.opacity=e;_classPrivateMethodGet(this,R,_redraw2).call(this)},mustExec:!0,type:r.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}function _getInitialBBox2(){const{parentRotation:t,parentDimensions:[e,i]}=this;switch(t){case 90:return[0,i,i,e];case 180:return[e,i,e,i];case 270:return[e,0,i,e];default:return[0,0,e,i]}}function _setStroke2(){const{ctx:t,color:e,opacity:i,thickness:r,parentScale:s,scaleFactor:a}=this;t.lineWidth=r*s/a;t.lineCap="round";t.lineJoin="round";t.miterLimit=10;t.strokeStyle=`${e}${(0,n.opacityToHex)(i)}`}function _startDrawing2(t,e){this.isEditing=!0;if(!_classPrivateFieldGet(this,v)){_classPrivateFieldSet(this,v,!0);_classPrivateMethodGet(this,L,_setCanvasDims2).call(this);this.thickness||(this.thickness=InkEditor._defaultThickness);this.color||(this.color=InkEditor._defaultColor||s.AnnotationEditor._defaultLineColor);this.opacity??(this.opacity=InkEditor._defaultOpacity)}this.currentPath.push([t,e]);_classPrivateFieldSet(this,_,null);_classPrivateMethodGet(this,C,_setStroke2).call(this);this.ctx.beginPath();this.ctx.moveTo(t,e);_classPrivateFieldSet(this,S,(()=>{if(_classPrivateFieldGet(this,S)){if(_classPrivateFieldGet(this,_)){if(this.isEmpty()){this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height)}else _classPrivateMethodGet(this,R,_redraw2).call(this);this.ctx.lineTo(..._classPrivateFieldGet(this,_));_classPrivateFieldSet(this,_,null);this.ctx.stroke()}window.requestAnimationFrame(_classPrivateFieldGet(this,S))}}));window.requestAnimationFrame(_classPrivateFieldGet(this,S))}function _draw2(t,e){const[i,r]=this.currentPath.at(-1);if(t!==i||e!==r){this.currentPath.push([t,e]);_classPrivateFieldSet(this,_,[t,e])}}function _stopDrawing2(t,e){this.ctx.closePath();_classPrivateFieldSet(this,S,null);t=Math.min(Math.max(t,0),this.canvas.width);e=Math.min(Math.max(e,0),this.canvas.height);const[i,r]=this.currentPath.at(-1);t===i&&e===r||this.currentPath.push([t,e]);let s;if(1!==this.currentPath.length)s=(0,a.fitCurve)(this.currentPath,30,null);else{const i=[t,e];s=[[i,i.slice(),i.slice(),i]]}const n=_classStaticPrivateMethodGet(InkEditor,InkEditor,_buildPath2D).call(InkEditor,s);this.currentPath.length=0;this.addCommands({cmd:()=>{this.paths.push(s);this.bezierPath2D.push(n);this.rebuild()},undo:()=>{this.paths.pop();this.bezierPath2D.pop();if(0===this.paths.length)this.remove();else{if(!this.canvas){_classPrivateMethodGet(this,D,_createCanvas2).call(this);_classPrivateMethodGet(this,O,_createObserver2).call(this)}_classPrivateMethodGet(this,z,_fitToContent2).call(this)}},mustExec:!0})}function _redraw2(){if(this.isEmpty()){_classPrivateMethodGet(this,N,_updateTransform2).call(this);return}_classPrivateMethodGet(this,C,_setStroke2).call(this);const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,t.width,t.height);_classPrivateMethodGet(this,N,_updateTransform2).call(this);for(const t of this.bezierPath2D)e.stroke(t)}function _endDrawing2(t){_classPrivateMethodGet(this,T,_stopDrawing2).call(this,t.offsetX,t.offsetY);this.canvas.removeEventListener("pointerleave",_classPrivateFieldGet(this,p));this.canvas.removeEventListener("pointermove",_classPrivateFieldGet(this,u));this.addToAnnotationStorage()}function _createCanvas2(){this.canvas=document.createElement("canvas");this.canvas.width=this.canvas.height=0;this.canvas.className="inkEditorCanvas";InkEditor._l10nPromise.get("editor_ink_canvas_aria_label").then((t=>{var e;return null===(e=this.canvas)||void 0===e?void 0:e.setAttribute("aria-label",t)}));this.div.append(this.canvas);this.ctx=this.canvas.getContext("2d")}function _createObserver2(){let t=null;_classPrivateFieldSet(this,b,new ResizeObserver((e=>{const i=e[0].contentRect;if(i.width&&i.height){null!==t&&clearTimeout(t);t=setTimeout((()=>{this.fixDims();t=null}),l);this.setDimensions(i.width,i.height)}})));_classPrivateFieldGet(this,b).observe(this.div)}function _setCanvasDims2(){if(!_classPrivateFieldGet(this,v))return;const[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t);this.canvas.height=Math.ceil(this.height*e);_classPrivateMethodGet(this,N,_updateTransform2).call(this)}function _setScaleFactor2(t,e){const i=_classPrivateMethodGet(this,q,_getPadding2).call(this),r=(t-i)/_classPrivateFieldGet(this,h),s=(e-i)/_classPrivateFieldGet(this,d);this.scaleFactor=Math.min(r,s)}function _updateTransform2(){const t=_classPrivateMethodGet(this,q,_getPadding2).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}function _buildPath2D(t){const e=new Path2D;for(let i=0,r=t.length;i<r;i++){const[r,s,a,n]=t[i];0===i&&e.moveTo(...r);e.bezierCurveTo(s[0],s[1],a[0],a[1],n[0],n[1])}return e}function _serializePaths2(t,e,i,r){const s=[],a=this.thickness/2;let n,o;for(const l of this.paths){n=[];o=[];for(let s=0,c=l.length;s<c;s++){const[c,d,h,u]=l[s],p=t*(c[0]+e)+a,f=r-t*(c[1]+i)-a,g=t*(d[0]+e)+a,m=r-t*(d[1]+i)-a,v=t*(h[0]+e)+a,_=r-t*(h[1]+i)-a,b=t*(u[0]+e)+a,y=r-t*(u[1]+i)-a;if(0===s){n.push(p,f);o.push(p,f)}n.push(g,m,v,_,b,y);_classPrivateMethodGet(this,U,_extractPointsOnBezier2).call(this,p,f,g,m,v,_,b,y,4,o)}s.push({bezier:n,points:o})}return s}function _extractPointsOnBezier2(t,e,i,r,s,a,n,o,l,c){if(_classPrivateMethodGet(this,B,_isAlmostFlat2).call(this,t,e,i,r,s,a,n,o))c.push(n,o);else{for(let d=1;d<l-1;d++){const h=d/l,u=1-h;let p=h*t+u*i,f=h*e+u*r,g=h*i+u*s,m=h*r+u*a;p=h*p+u*g;f=h*f+u*m;g=h*g+u*(h*s+u*n);m=h*m+u*(h*a+u*o);p=h*p+u*g;f=h*f+u*m;c.push(p,f)}c.push(n,o)}}function _isAlmostFlat2(t,e,i,r,s,a,n,o){const l=(3*i-2*t-n)**2,c=(3*r-2*e-o)**2,d=(3*s-t-2*n)**2,h=(3*a-e-2*o)**2;return Math.max(l,d)+Math.max(c,h)<=10}function _getBbox2(){let t=1/0,e=-1/0,i=1/0,s=-1/0;for(const a of this.paths)for(const[n,o,l,c]of a){const a=r.Util.bezierBoundingBox(...n,...o,...l,...c);t=Math.min(t,a[0]);i=Math.min(i,a[1]);e=Math.max(e,a[2]);s=Math.max(s,a[3])}return[t,i,e,s]}function _getPadding2(){return _classPrivateFieldGet(this,m)?Math.ceil(this.thickness*this.parentScale):0}function _fitToContent2(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return;if(!_classPrivateFieldGet(this,m)){_classPrivateMethodGet(this,R,_redraw2).call(this);return}const e=_classPrivateMethodGet(this,W,_getBbox2).call(this),i=_classPrivateMethodGet(this,q,_getPadding2).call(this);_classPrivateFieldSet(this,h,Math.max(o,e[2]-e[0]));_classPrivateFieldSet(this,d,Math.max(o,e[3]-e[1]));const r=Math.ceil(i+_classPrivateFieldGet(this,h)*this.scaleFactor),s=Math.ceil(i+_classPrivateFieldGet(this,d)*this.scaleFactor),[a,n]=this.parentDimensions;this.width=r/a;this.height=s/n;_classPrivateFieldSet(this,c,r/s);_classPrivateMethodGet(this,H,_setMinDims2).call(this);const l=this.translationX,u=this.translationY;this.translationX=-e[0];this.translationY=-e[1];_classPrivateMethodGet(this,L,_setCanvasDims2).call(this);_classPrivateMethodGet(this,R,_redraw2).call(this);_classPrivateFieldSet(this,y,r);_classPrivateFieldSet(this,A,s);this.setDims(r,s);const p=t?i/this.scaleFactor/2:0;this.translate(l-this.translationX-p,u-this.translationY-p)}function _setMinDims2(){const{style:t}=this.div;if(_classPrivateFieldGet(this,c)>=1){t.minHeight=`${o}px`;t.minWidth=`${Math.round(_classPrivateFieldGet(this,c)*o)}px`}else{t.minWidth=`${o}px`;t.minHeight=`${Math.round(o/_classPrivateFieldGet(this,c))}px`}}_defineProperty(InkEditor,"_defaultColor",null);_defineProperty(InkEditor,"_defaultOpacity",1);_defineProperty(InkEditor,"_defaultThickness",1);_defineProperty(InkEditor,"_l10nPromise",void 0);_defineProperty(InkEditor,"_type","ink")},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.fitCurve=void 0;const r=i(163);e.fitCurve=r},t=>{"use strict";function fitCubic(t,e,i,r,s){var a,n,o,l,c,d,h,u,p,f,g,m,v;if(2===t.length){m=maths.vectorLen(maths.subtract(t[0],t[1]))/3;return[a=[t[0],maths.addArrays(t[0],maths.mulItems(e,m)),maths.addArrays(t[1],maths.mulItems(i,m)),t[1]]]}n=function chordLengthParameterize(t){var e,i,r,s=[];t.forEach(((t,a)=>{e=a?i+maths.vectorLen(maths.subtract(t,r)):0;s.push(e);i=e;r=t}));s=s.map((t=>t/i));return s}(t);[a,l,d]=generateAndReport(t,n,n,e,i,s);if(0===l||l<r)return[a];if(l<r*r){o=n;c=l;h=d;for(v=0;v<20;v++){o=reparameterize(a,t,o);[a,l,d]=generateAndReport(t,n,o,e,i,s);if(l<r)return[a];if(d===h){let t=l/c;if(t>.9999&&t<1.0001)break}c=l;h=d}}g=[];if((u=maths.subtract(t[d-1],t[d+1])).every((t=>0===t))){u=maths.subtract(t[d-1],t[d]);[u[0],u[1]]=[-u[1],u[0]]}p=maths.normalize(u);f=maths.mulItems(p,-1);return g=(g=g.concat(fitCubic(t.slice(0,d+1),e,p,r,s))).concat(fitCubic(t.slice(d),f,i,r,s))}function generateAndReport(t,e,i,r,s,a){var n,o,l;n=function generateBezier(t,e,i,r){var s,a,n,o,l,c,d,h,u,p,f,g,m,v,_,b,y,A=t[0],S=t[t.length-1];s=[A,null,null,S];a=maths.zeros_Xx2x2(e.length);for(m=0,v=e.length;m<v;m++){y=1-(b=e[m]);(n=a[m])[0]=maths.mulItems(i,3*b*(y*y));n[1]=maths.mulItems(r,3*y*(b*b))}o=[[0,0],[0,0]];l=[0,0];for(m=0,v=t.length;m<v;m++){b=e[m];n=a[m];o[0][0]+=maths.dot(n[0],n[0]);o[0][1]+=maths.dot(n[0],n[1]);o[1][0]+=maths.dot(n[0],n[1]);o[1][1]+=maths.dot(n[1],n[1]);_=maths.subtract(t[m],bezier.q([A,A,S,S],b));l[0]+=maths.dot(n[0],_);l[1]+=maths.dot(n[1],_)}c=o[0][0]*o[1][1]-o[1][0]*o[0][1];d=o[0][0]*l[1]-o[1][0]*l[0];h=l[0]*o[1][1]-l[1]*o[0][1];u=0===c?0:h/c;p=0===c?0:d/c;g=maths.vectorLen(maths.subtract(A,S));if(u<(f=1e-6*g)||p<f){s[1]=maths.addArrays(A,maths.mulItems(i,g/3));s[2]=maths.addArrays(S,maths.mulItems(r,g/3))}else{s[1]=maths.addArrays(A,maths.mulItems(i,u));s[2]=maths.addArrays(S,maths.mulItems(r,p))}return s}(t,i,r,s);[o,l]=function computeMaxError(t,e,i){var r,s,a,n,o,l,c,d;s=0;a=Math.floor(t.length/2);const h=mapTtoRelativeDistances(e,10);for(o=0,l=t.length;o<l;o++){c=t[o];d=find_t(e,i[o],h,10);if((r=(n=maths.subtract(bezier.q(e,d),c))[0]*n[0]+n[1]*n[1])>s){s=r;a=o}}return[s,a]}(t,n,e);a&&a({bez:n,points:t,params:e,maxErr:o,maxPoint:l});return[n,o,l]}function reparameterize(t,e,i){return i.map(((i,r)=>newtonRaphsonRootFind(t,e[r],i)))}function newtonRaphsonRootFind(t,e,i){var r=maths.subtract(bezier.q(t,i),e),s=bezier.qprime(t,i),a=maths.mulMatrix(r,s),n=maths.sum(maths.squareItems(s))+2*maths.mulMatrix(r,bezier.qprimeprime(t,i));return 0===n?i:i-a/n}var mapTtoRelativeDistances=function(t,e){for(var i,r=[0],s=t[0],a=0,n=1;n<=e;n++){i=bezier.q(t,n/e);a+=maths.vectorLen(maths.subtract(i,s));r.push(a);s=i}return r=r.map((t=>t/a))};function find_t(t,e,i,r){if(e<0)return 0;if(e>1)return 1;for(var s,a,n,o,l=1;l<=r;l++)if(e<=i[l]){n=(l-1)/r;a=l/r;o=(e-(s=i[l-1]))/(i[l]-s)*(a-n)+n;break}return o}function createTangent(t,e){return maths.normalize(maths.subtract(t,e))}class maths{static zeros_Xx2x2(t){for(var e=[];t--;)e.push([0,0]);return e}static mulItems(t,e){return t.map((t=>t*e))}static mulMatrix(t,e){return t.reduce(((t,i,r)=>t+i*e[r]),0)}static subtract(t,e){return t.map(((t,i)=>t-e[i]))}static addArrays(t,e){return t.map(((t,i)=>t+e[i]))}static addItems(t,e){return t.map((t=>t+e))}static sum(t){return t.reduce(((t,e)=>t+e))}static dot(t,e){return maths.mulMatrix(t,e)}static vectorLen(t){return Math.hypot(...t)}static divItems(t,e){return t.map((t=>t/e))}static squareItems(t){return t.map((t=>t*t))}static normalize(t){return this.divItems(t,this.vectorLen(t))}}class bezier{static q(t,e){var i=1-e,r=maths.mulItems(t[0],i*i*i),s=maths.mulItems(t[1],3*i*i*e),a=maths.mulItems(t[2],3*i*e*e),n=maths.mulItems(t[3],e*e*e);return maths.addArrays(maths.addArrays(r,s),maths.addArrays(a,n))}static qprime(t,e){var i=1-e,r=maths.mulItems(maths.subtract(t[1],t[0]),3*i*i),s=maths.mulItems(maths.subtract(t[2],t[1]),6*i*e),a=maths.mulItems(maths.subtract(t[3],t[2]),3*e*e);return maths.addArrays(maths.addArrays(r,s),a)}static qprimeprime(t,e){return maths.addArrays(maths.mulItems(maths.addArrays(maths.subtract(t[2],maths.mulItems(t[1],2)),t[0]),6*(1-e)),maths.mulItems(maths.addArrays(maths.subtract(t[3],maths.mulItems(t[2],2)),t[1]),6*e))}}t.exports=function fitCurve(t,e,i){if(!Array.isArray(t))throw new TypeError("First argument should be an array");t.forEach((e=>{if(!Array.isArray(e)||e.some((t=>"number"!=typeof t))||e.length!==t[0].length)throw Error("Each point should be an array of numbers. Each point should have the same amount of numbers.")}));if((t=t.filter(((e,i)=>0===i||!e.every(((e,r)=>e===t[i-1][r]))))).length<2)return[];const r=t.length,s=createTangent(t[1],t[0]),a=createTangent(t[r-2],t[r-1]);return fitCubic(t,s,a,e,i)};t.exports.fitCubic=fitCubic;t.exports.createTangent=createTangent},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationLayer=void 0;var r=i(1),s=i(139),a=i(136),n=i(165),o=i(166);function _classStaticPrivateMethodGet(t,e,i){!function _classCheckPrivateStaticAccess(t,e){if(t!==e)throw new TypeError("Private static access of wrong provenance")}(t,e);return i}function _classPrivateMethodInitSpec(t,e){!function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}(t,e);e.add(t)}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}const l=1e3,c=new WeakSet;function getRectDims(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case r.AnnotationType.LINK:return new LinkAnnotationElement(t);case r.AnnotationType.TEXT:return new TextAnnotationElement(t);case r.AnnotationType.WIDGET:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case r.AnnotationType.POPUP:return new PopupAnnotationElement(t);case r.AnnotationType.FREETEXT:return new FreeTextAnnotationElement(t);case r.AnnotationType.LINE:return new LineAnnotationElement(t);case r.AnnotationType.SQUARE:return new SquareAnnotationElement(t);case r.AnnotationType.CIRCLE:return new CircleAnnotationElement(t);case r.AnnotationType.POLYLINE:return new PolylineAnnotationElement(t);case r.AnnotationType.CARET:return new CaretAnnotationElement(t);case r.AnnotationType.INK:return new InkAnnotationElement(t);case r.AnnotationType.POLYGON:return new PolygonAnnotationElement(t);case r.AnnotationType.HIGHLIGHT:return new HighlightAnnotationElement(t);case r.AnnotationType.UNDERLINE:return new UnderlineAnnotationElement(t);case r.AnnotationType.SQUIGGLY:return new SquigglyAnnotationElement(t);case r.AnnotationType.STRIKEOUT:return new StrikeOutAnnotationElement(t);case r.AnnotationType.STAMP:return new StampAnnotationElement(t);case r.AnnotationType.FILEATTACHMENT:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{constructor(t){let{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.page=t.page;this.viewport=t.viewport;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;e&&(this.container=this._createContainer(i));r&&(this.quadrilaterals=this._createQuadrilaterals(i))}_createContainer(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const{data:e,page:i,viewport:s}=this,a=document.createElement("section");a.setAttribute("data-annotation-id",e.id);const{pageWidth:n,pageHeight:o,pageX:l,pageY:c}=s.rawDims,{width:d,height:h}=getRectDims(e.rect),u=r.Util.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]);if(!t&&e.borderStyle.width>0){a.style.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${i}px * var(--scale-factor))`;a.style.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${d}px * var(--scale-factor)) / calc(${h}px * var(--scale-factor))`;a.style.borderRadius=t}switch(e.borderStyle.style){case r.AnnotationBorderStyleType.SOLID:a.style.borderStyle="solid";break;case r.AnnotationBorderStyleType.DASHED:a.style.borderStyle="dashed";break;case r.AnnotationBorderStyleType.BEVELED:(0,r.warn)("Unimplemented border style: beveled");break;case r.AnnotationBorderStyleType.INSET:(0,r.warn)("Unimplemented border style: inset");break;case r.AnnotationBorderStyleType.UNDERLINE:a.style.borderBottomStyle="solid"}const s=e.borderColor||null;s?a.style.borderColor=r.Util.makeHexColor(0|s[0],0|s[1],0|s[2]):a.style.borderWidth=0}a.style.left=100*(u[0]-l)/n+"%";a.style.top=100*(u[1]-c)/o+"%";const{rotation:p}=e;if(e.hasOwnCanvas||0===p){a.style.width=100*d/n+"%";a.style.height=100*h/o+"%"}else this.setRotation(p,a);return a}setRotation(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.container;const{pageWidth:i,pageHeight:r}=this.viewport.rawDims,{width:s,height:a}=getRectDims(this.data.rect);let n,o;if(t%180==0){n=100*s/i;o=100*a/r}else{n=100*a/i;o=100*s/r}e.style.width=`${n}%`;e.style.height=`${o}%`;e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,i)=>{const r=i.detail[t];i.target.style[e]=n.ColorConverters[`${r[0]}_HTML`](r.slice(1))};return(0,r.shadow)(this,"_commonActions",{display:t=>{const e=t.detail.display%2==1;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{hidden:e,print:0===t.detail.display||3===t.detail.display})},print:t=>{this.annotationStorage.setValue(this.data.id,{print:t.detail.print})},hidden:t=>{this.container.style.visibility=t.detail.hidden?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{hidden:t.detail.hidden})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.detail.readonly?t.target.setAttribute("readonly",""):t.target.removeAttribute("readonly")},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const r of Object.keys(e.detail)){const s=t[r]||i[r];null==s||s(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[r,s]of Object.entries(e)){const a=i[r];if(a){a({detail:{[r]:s},target:t});delete e[r]}}}_createQuadrilaterals(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.data.quadPoints)return null;const e=[],i=this.data.rect;for(const i of this.data.quadPoints){this.data.rect=[i[2].x,i[2].y,i[1].x,i[1].y];e.push(this._createContainer(t))}this.data.rect=i;return e}_createPopup(t,e){let i=this.container;if(this.quadrilaterals){t=t||this.quadrilaterals;i=this.quadrilaterals[0]}if(!t){(t=document.createElement("div")).className="popupTriggerArea";i.append(t)}const r=new PopupElement({container:i,trigger:t,color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,hideWrapper:!0}).render();r.style.left="100%";i.append(r)}_renderQuadrilaterals(t){for(const e of this.quadrilaterals)e.className=t;return this.quadrilaterals}render(){(0,r.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:a,exportValues:n}of s){if(-1===t)continue;if(a===e)continue;const s="string"==typeof n?n:null,o=document.querySelector(`[data-element-id="${a}"]`);!o||c.has(o)?i.push({id:a,exportValue:s,domElement:o}):(0,r.warn)(`_getElementsByName - element not allowed: ${a}`)}return i}for(const r of document.getElementsByName(t)){const{exportValue:t}=r,s=r.getAttribute("data-element-id");s!==e&&(c.has(r)&&i.push({id:s,exportValue:t,domElement:r}))}return i}}var d=new WeakSet,h=new WeakSet;class LinkAnnotationElement extends AnnotationElement{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(t,{isRenderable:!0,ignoreBorder:!(null==e||!e.ignoreBorder),createQuadrilaterals:!0});_classPrivateMethodInitSpec(this,h);_classPrivateMethodInitSpec(this,d);this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let r=!1;if(t.url){e.addLinkAttributes(i,t.url,t.newWindow);r=!0}else if(t.action){this._bindNamedAction(i,t.action);r=!0}else if(t.attachment){this._bindAttachment(i,t.attachment);r=!0}else if(t.setOCGState){_classPrivateMethodGet(this,h,_bindSetOCGState2).call(this,i,t.setOCGState);r=!0}else if(t.dest){this._bindLink(i,t.dest);r=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(i,t);r=!0}if(t.resetForm){this._bindResetFormAction(i,t.resetForm);r=!0}else if(this.isTooltipOnly&&!r){this._bindLink(i,"");r=!0}}if(this.quadrilaterals)return this._renderQuadrilaterals("linkAnnotation").map(((t,e)=>{const r=0===e?i:i.cloneNode();t.append(r);return t}));this.container.className="linkAnnotation";r&&this.container.append(i);return this.container}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&_classPrivateMethodGet(this,d,_setInternalLink2).call(this)}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};_classPrivateMethodGet(this,d,_setInternalLink2).call(this)}_bindAttachment(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{var t;null===(t=this.downloadManager)||void 0===t||t.openOrDownloadData(this.container,e.content,e.filename);return!1};_classPrivateMethodGet(this,d,_setInternalLink2).call(this)}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const r of Object.keys(e.actions)){const s=i.get(r);s&&(t[s]=()=>{var t;null===(t=this.linkService.eventBus)||void 0===t||t.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:r}});return!1})}t.onclick||(t.onclick=()=>!1);_classPrivateMethodGet(this,d,_setInternalLink2).call(this)}_bindResetFormAction(t,e){const i=t.onclick;i||(t.href=this.linkService.getAnchorUrl(""));_classPrivateMethodGet(this,d,_setInternalLink2).call(this);if(this._fieldObjects)t.onclick=()=>{null==i||i();const{fields:t,refs:s,include:a}=e,n=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===a&&n.push(i)}else for(const t of Object.values(this._fieldObjects))n.push(...t);const o=this.annotationStorage,l=[];for(const t of n){const{id:e}=t;l.push(e);switch(t.type){case"text":{const i=t.defaultValue||"";o.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;o.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";o.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(c.has(i)?i.dispatchEvent(new Event("resetform")):(0,r.warn)(`_bindResetFormAction - element not allowed: ${e}`))}if(this.enableScripting){var d;null===(d=this.linkService.eventBus)||void 0===d||d.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}})}return!1};else{(0,r.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');i||(t.onclick=()=>!1)}}}function _setInternalLink2(){this.container.setAttribute("data-internal-link","")}function _bindSetOCGState2(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};_classPrivateMethodGet(this,d,_setInternalLink2).call(this)}class TextAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str)})}render(){this.container.className="textAnnotation";const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.alt="[{{type}} Annotation]";t.dataset.l10nId="text_annotation_type";t.dataset.l10nArgs=JSON.stringify({type:this.data.name});this.data.hasPopup||this._createPopup(t,this.data);this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){this.data.alternativeText&&(this.container.title=this.data.alternativeText);return this.container}_getKeyModifier(t){const{isWin:e,isMac:i}=r.FeatureTest.platform;return e&&t.ctrlKey||i&&t.metaKey}_setEventListener(t,e,i,r){e.includes("mouse")?t.addEventListener(e,(t=>{var e;null===(e=this.linkService.eventBus)||void 0===e||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(e,(t=>{var e;null===(e=this.linkService.eventBus)||void 0===e||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(t)}})}))}_setEventListeners(t,e,i){for(const[s,a]of e){var r;("Action"===a||null!==(r=this.data.actions)&&void 0!==r&&r[a])&&this._setEventListener(t,s,a,i)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":r.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,a=t.style;let n;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(r.LINE_FACTOR*s))||1);n=Math.min(s,roundToOneDecimal(e/r.LINE_FACTOR))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);n=Math.min(s,roundToOneDecimal(t/r.LINE_FACTOR))}a.fontSize=`calc(${n}px * var(--scale-factor))`;a.color=r.Util.makeHexColor(i[0],i[1],i[2]);null!==this.data.textAlignment&&(a.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,r){const s=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id)){a.domElement&&(a.domElement[e]=i);s.setValue(a.id,{[r]:i})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.className="textWidgetAnnotation";let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let a=s.formattedValue||s.value||"";const n=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;n&&a.length>n&&(a=a.slice(0,n));const o={userValue:a,formattedValue:null,lastCommittedValue:null,commitKey:1};if(this.data.multiLine){i=document.createElement("textarea");i.textContent=a;this.data.doNotScroll&&(i.style.overflowY="hidden")}else{i=document.createElement("input");i.type="text";i.setAttribute("value",a);this.data.doNotScroll&&(i.style.overflowX="hidden")}c.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;i.name=this.data.fieldName;i.tabIndex=l;this._setRequired(i,this.data.required);n&&(i.maxLength=n);i.addEventListener("input",(r=>{t.setValue(e,{value:r.target.value});this.setPropertyOnSiblings(i,"value",r.target.value,"value")}));i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e;o.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){var r;i.addEventListener("focus",(t=>{const{target:e}=t;o.userValue&&(e.value=o.userValue);o.lastCommittedValue=e.value;o.commitKey=1}));i.addEventListener("updatefromsandbox",(i=>{const r={value(i){o.userValue=i.detail.value??"";t.setValue(e,{value:o.userValue.toString()});i.target.value=o.userValue},formattedValue(i){const{formattedValue:r}=i.detail;o.formattedValue=r;null!=r&&i.target!==document.activeElement&&(i.target.value=r);t.setValue(e,{formattedValue:r})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{var r;const{charLimit:s}=i.detail,{target:a}=i;if(0===s){a.removeAttribute("maxLength");return}a.setAttribute("maxLength",s);let n=o.userValue;if(n&&!(n.length<=s)){n=n.slice(0,s);a.value=o.userValue=n;t.setValue(e,{value:n});null===(r=this.linkService.eventBus)||void 0===r||r.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,willCommit:!0,commitKey:1,selStart:a.selectionStart,selEnd:a.selectionEnd}})}}};this._dispatchEventFromSandbox(r,i)}));i.addEventListener("keydown",(t=>{var i;o.commitKey=1;let r=-1;"Escape"===t.key?r=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):r=2;if(-1===r)return;const{value:s}=t.target;if(o.lastCommittedValue!==s){o.lastCommittedValue=s;o.userValue=s;null===(i=this.linkService.eventBus)||void 0===i||i.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:r,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const s=blurListener;blurListener=null;i.addEventListener("blur",(t=>{if(!t.relatedTarget)return;const{value:i}=t.target;o.userValue=i;if(o.lastCommittedValue!==i){var r;null===(r=this.linkService.eventBus)||void 0===r||r.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}s(t)}));null!==(r=this.data.actions)&&void 0!==r&&r.Keystroke&&i.addEventListener("beforeinput",(t=>{var i;o.lastCommittedValue=null;const{data:r,target:s}=t,{value:a,selectionStart:n,selectionEnd:l}=s;let c=n,d=l;switch(t.inputType){case"deleteWordBackward":{const t=a.substring(0,n).match(/\w*[^\w]*$/);t&&(c-=t[0].length);break}case"deleteWordForward":{const t=a.substring(n).match(/^[^\w]*\w*/);t&&(d+=t[0].length);break}case"deleteContentBackward":n===l&&(c-=1);break;case"deleteContentForward":n===l&&(d+=1)}t.preventDefault();null===(i=this.linkService.eventBus)||void 0===i||i.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,change:r||"",willCommit:!1,selStart:c,selEnd:d}})}));this._setEventListeners(i,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&i.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/n;i.classList.add("comb");i.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else{i=document.createElement("div");i.textContent=this.data.fieldValue;i.style.verticalAlign="middle";i.style.display="table-cell"}this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let r=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof r){r="Off"!==r;t.setValue(i,{value:r})}this.container.className="buttonWidgetAnnotation checkBox";const s=document.createElement("input");c.add(s);s.setAttribute("data-element-id",i);s.disabled=e.readOnly;this._setRequired(s,this.data.required);s.type="checkbox";s.name=e.fieldName;r&&s.setAttribute("checked",!0);s.setAttribute("exportValue",e.exportValue);s.tabIndex=l;s.addEventListener("change",(r=>{const{name:s,checked:a}=r.target;for(const r of this._getElementsByName(s,i)){const i=a&&r.exportValue===e.exportValue;r.domElement&&(r.domElement.checked=i);t.setValue(r.id,{value:i})}t.setValue(i,{value:a})}));s.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}));if(this.enableScripting&&this.hasJSActions){s.addEventListener("updatefromsandbox",(e=>{const r={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(r,e)}));this._setEventListeners(s,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.className="buttonWidgetAnnotation radioButton";const t=this.annotationStorage,e=this.data,i=e.id;let r=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof r){r=r!==e.buttonValue;t.setValue(i,{value:r})}const s=document.createElement("input");c.add(s);s.setAttribute("data-element-id",i);s.disabled=e.readOnly;this._setRequired(s,this.data.required);s.type="radio";s.name=e.fieldName;r&&s.setAttribute("checked",!0);s.tabIndex=l;s.addEventListener("change",(e=>{const{name:r,checked:s}=e.target;for(const e of this._getElementsByName(r,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:s})}));s.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const r=e.buttonValue;s.addEventListener("updatefromsandbox",(e=>{const s={value:e=>{const s=r===e.detail.value;for(const r of this._getElementsByName(e.target.name)){const e=s&&r.id===i;r.domElement&&(r.domElement.checked=e);t.setValue(r.id,{value:e})}}};this._dispatchEventFromSandbox(s,e)}));this._setEventListeners(s,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.className="buttonWidgetAnnotation pushButton";this.data.alternativeText&&(t.title=this.data.alternativeText);const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.className="choiceWidgetAnnotation";const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),r=document.createElement("select");c.add(r);r.setAttribute("data-element-id",e);r.disabled=this.data.readOnly;this._setRequired(r,this.data.required);r.name=this.data.fieldName;r.tabIndex=l;let s=this.data.combo&&this.data.options.length>0;if(!this.data.combo){r.size=this.data.options.length;this.data.multiSelect&&(r.multiple=!0)}r.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of r.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(i.value.includes(t.exportValue)){e.setAttribute("selected",!0);s=!1}r.append(e)}let a=null;if(s){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);r.prepend(t);a=()=>{t.remove();r.removeEventListener("input",a);a=null};r.addEventListener("input",a)}const getValue=t=>{const e=t?"value":"textContent",{options:i,multiple:s}=r;return s?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let n=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){r.addEventListener("updatefromsandbox",(i=>{const s={value(i){var s;null===(s=a)||void 0===s||s();const o=i.detail.value,l=new Set(Array.isArray(o)?o:[o]);for(const t of r.options)t.selected=l.has(t.value);t.setValue(e,{value:getValue(!0)});n=getValue(!1)},multipleSelection(t){r.multiple=!0},remove(i){const s=r.options,a=i.detail.remove;s[a].selected=!1;r.remove(a);if(s.length>0){-1===Array.prototype.findIndex.call(s,(t=>t.selected))&&(s[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(i)});n=getValue(!1)},clear(i){for(;0!==r.length;)r.remove(0);t.setValue(e,{value:null,items:[]});n=getValue(!1)},insert(i){const{index:s,displayValue:a,exportValue:o}=i.detail.insert,l=r.children[s],c=document.createElement("option");c.textContent=a;c.value=o;l?l.before(c):r.append(c);t.setValue(e,{value:getValue(!0),items:getItems(i)});n=getValue(!1)},items(i){const{items:s}=i.detail;for(;0!==r.length;)r.remove(0);for(const t of s){const{displayValue:e,exportValue:i}=t,s=document.createElement("option");s.textContent=e;s.value=i;r.append(s)}r.options.length>0&&(r.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(i)});n=getValue(!1)},indices(i){const r=new Set(i.detail.indices);for(const t of i.target.options)t.selected=r.has(t.index);t.setValue(e,{value:getValue(!0)});n=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(s,i)}));r.addEventListener("input",(i=>{var r;const s=getValue(!0);t.setValue(e,{value:s});i.preventDefault();null===(r=this.linkService.eventBus)||void 0===r||r.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(r,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else r.addEventListener("input",(function(i){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(r);this._setBackgroundColor(r);this._setDefaultPropertiesFromJS(r);this.container.append(r);return this.container}}class PopupAnnotationElement extends AnnotationElement{static IGNORE_TYPES=new Set(["Line","Square","Circle","PolyLine","Polygon","Ink"]);constructor(t){var e,i,r;const{data:s}=t;super(t,{isRenderable:!PopupAnnotationElement.IGNORE_TYPES.has(s.parentType)&&!!(null!==(e=s.titleObj)&&void 0!==e&&e.str||null!==(i=s.contentsObj)&&void 0!==i&&i.str||null!==(r=s.richText)&&void 0!==r&&r.str)})}render(){this.container.className="popupAnnotation";const t=this.layer.querySelectorAll(`[data-annotation-id="${this.data.parentId}"]`);if(0===t.length)return this.container;const e=new PopupElement({container:this.container,trigger:Array.from(t),color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText}),i=this.page,s=r.Util.normalizeRect([this.data.parentRect[0],i.view[3]-this.data.parentRect[1]+i.view[1],this.data.parentRect[2],i.view[3]-this.data.parentRect[3]+i.view[1]]),a=s[0]+this.data.parentRect[2]-this.data.parentRect[0],n=s[1],{pageWidth:o,pageHeight:l,pageX:c,pageY:d}=this.viewport.rawDims;this.container.style.left=100*(a-c)/o+"%";this.container.style.top=100*(n-d)/l+"%";this.container.append(e.render());return this.container}}class PopupElement{constructor(t){this.container=t.container;this.trigger=t.trigger;this.color=t.color;this.titleObj=t.titleObj;this.modificationDate=t.modificationDate;this.contentsObj=t.contentsObj;this.richText=t.richText;this.hideWrapper=t.hideWrapper||!1;this.pinned=!1}render(){var t,e;const i=document.createElement("div");i.className="popupWrapper";this.hideElement=this.hideWrapper?i:this.container;this.hideElement.hidden=!0;const a=document.createElement("div");a.className="popup";const n=this.color;if(n){const t=.7*(255-n[0])+n[0],e=.7*(255-n[1])+n[1],i=.7*(255-n[2])+n[2];a.style.backgroundColor=r.Util.makeHexColor(0|t,0|e,0|i)}const l=document.createElement("h1");l.dir=this.titleObj.dir;l.textContent=this.titleObj.str;a.append(l);const c=s.PDFDateString.toDateObject(this.modificationDate);if(c){const t=document.createElement("span");t.className="popupDate";t.textContent="{{date}}, {{time}}";t.dataset.l10nId="annotation_date_string";t.dataset.l10nArgs=JSON.stringify({date:c.toLocaleDateString(),time:c.toLocaleTimeString()});a.append(t)}if(null===(t=this.richText)||void 0===t||!t.str||null!==(e=this.contentsObj)&&void 0!==e&&e.str&&this.contentsObj.str!==this.richText.str){const t=this._formatContents(this.contentsObj);a.append(t)}else{o.XfaLayer.render({xfaHtml:this.richText.html,intent:"richText",div:a});a.lastChild.className="richText popupContent"}Array.isArray(this.trigger)||(this.trigger=[this.trigger]);for(const t of this.trigger){t.addEventListener("click",this._toggle.bind(this));t.addEventListener("mouseover",this._show.bind(this,!1));t.addEventListener("mouseout",this._hide.bind(this,!1))}a.addEventListener("click",this._hide.bind(this,!0));i.append(a);return i}_formatContents(t){let{str:e,dir:i}=t;const r=document.createElement("p");r.className="popupContent";r.dir=i;const s=e.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const i=s[t];r.append(document.createTextNode(i));t<e-1&&r.append(document.createElement("br"))}return r}_toggle(){this.pinned?this._hide(!0):this._show(!0)}_show(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(this.pinned=!0);if(this.hideElement.hidden){this.hideElement.hidden=!1;this.container.style.zIndex=parseInt(this.container.style.zIndex)+1e3}}_hide(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.pinned=!1);if(!this.hideElement.hidden&&!this.pinned){this.hideElement.hidden=!0;this.container.style.zIndex=parseInt(this.container.style.zIndex)-1e3}}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0});this.textContent=t.data.textContent}render(){this.container.className="freeTextAnnotation";if(this.textContent){const t=document.createElement("div");t.className="annotationTextContent";t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e;t.append(i)}this.container.append(t)}this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class LineAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0})}render(){this.container.className="lineAnnotation";const t=this.data,{width:e,height:i}=getRectDims(t.rect),r=this.svgFactory.create(e,i,!0),s=this.svgFactory.createElement("svg:line");s.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);s.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);s.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);s.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);s.setAttribute("stroke-width",t.borderStyle.width||1);s.setAttribute("stroke","transparent");s.setAttribute("fill","transparent");r.append(s);this.container.append(r);this._createPopup(s,t);return this.container}}class SquareAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0})}render(){this.container.className="squareAnnotation";const t=this.data,{width:e,height:i}=getRectDims(t.rect),r=this.svgFactory.create(e,i,!0),s=t.borderStyle.width,a=this.svgFactory.createElement("svg:rect");a.setAttribute("x",s/2);a.setAttribute("y",s/2);a.setAttribute("width",e-s);a.setAttribute("height",i-s);a.setAttribute("stroke-width",s||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","transparent");r.append(a);this.container.append(r);this._createPopup(a,t);return this.container}}class CircleAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0})}render(){this.container.className="circleAnnotation";const t=this.data,{width:e,height:i}=getRectDims(t.rect),r=this.svgFactory.create(e,i,!0),s=t.borderStyle.width,a=this.svgFactory.createElement("svg:ellipse");a.setAttribute("cx",e/2);a.setAttribute("cy",i/2);a.setAttribute("rx",e/2-s/2);a.setAttribute("ry",i/2-s/2);a.setAttribute("stroke-width",s||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","transparent");r.append(a);this.container.append(r);this._createPopup(a,t);return this.container}}class PolylineAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const t=this.data,{width:e,height:i}=getRectDims(t.rect),r=this.svgFactory.create(e,i,!0);let s=[];for(const e of t.vertices){const i=e.x-t.rect[0],r=t.rect[3]-e.y;s.push(i+","+r)}s=s.join(" ");const a=this.svgFactory.createElement(this.svgElementName);a.setAttribute("points",s);a.setAttribute("stroke-width",t.borderStyle.width||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","transparent");r.append(a);this.container.append(r);this._createPopup(a,t);return this.container}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0})}render(){this.container.className="caretAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class InkAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const t=this.data,{width:e,height:i}=getRectDims(t.rect),r=this.svgFactory.create(e,i,!0);for(const e of t.inkLists){let i=[];for(const r of e){const e=r.x-t.rect[0],s=t.rect[3]-r.y;i.push(`${e},${s}`)}i=i.join(" ");const s=this.svgFactory.createElement(this.svgElementName);s.setAttribute("points",i);s.setAttribute("stroke-width",t.borderStyle.width||1);s.setAttribute("stroke","transparent");s.setAttribute("fill","transparent");this._createPopup(s,t);r.append(s)}this.container.append(r);return this.container}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("highlightAnnotation");this.container.className="highlightAnnotation";return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("underlineAnnotation");this.container.className="underlineAnnotation";return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("squigglyAnnotation");this.container.className="squigglyAnnotation";return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("strikeoutAnnotation");this.container.className="strikeoutAnnotation";return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){var e,i,r;super(t,{isRenderable:!!(t.data.hasPopup||null!==(e=t.data.titleObj)&&void 0!==e&&e.str||null!==(i=t.data.contentsObj)&&void 0!==i&&i.str||null!==(r=t.data.richText)&&void 0!==r&&r.str),ignoreBorder:!0})}render(){this.container.className="stampAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{constructor(t){var e;super(t,{isRenderable:!0});const{filename:i,content:r}=this.data.file;this.filename=(0,s.getFilenameFromUrl)(i,!0);this.content=r;null===(e=this.linkService.eventBus)||void 0===e||e.dispatch("fileattachmentannotation",{source:this,filename:i,content:r})}render(){var t,e;this.container.className="fileAttachmentAnnotation";let i;if(this.data.hasAppearance)i=document.createElement("div");else{i=document.createElement("img");i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(this.data.name)?"paperclip":"pushpin"}.svg`}i.className="popupTriggerArea";i.addEventListener("dblclick",this._download.bind(this));!this.data.hasPopup&&(null!==(t=this.data.titleObj)&&void 0!==t&&t.str||null!==(e=this.data.contentsObj)&&void 0!==e&&e.str||this.data.richText)&&this._createPopup(i,this.data);this.container.append(i);return this.container}_download(){var t;null===(t=this.downloadManager)||void 0===t||t.openOrDownloadData(this.container,this.content,this.filename)}}class AnnotationLayer{static render(t){const{annotations:e,div:i,viewport:n,accessibilityManager:o}=t;(0,s.setLayerDimensions)(i,n);const l={data:null,layer:i,page:t.page,viewport:n,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new s.DOMSVGFactory,annotationStorage:t.annotationStorage||new a.AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects};let c=0;for(const t of e){if(t.annotationType!==r.AnnotationType.POPUP){const{width:e,height:i}=getRectDims(t.rect);if(e<=0||i<=0)continue}l.data=t;const e=AnnotationElementFactory.create(l);if(!e.isRenderable)continue;const s=e.render();t.hidden&&(s.style.visibility="hidden");if(Array.isArray(s))for(const e of s){e.style.zIndex=c++;_classStaticPrivateMethodGet(AnnotationLayer,AnnotationLayer,_appendElement).call(AnnotationLayer,e,t.id,i,o)}else{s.style.zIndex=c++;e instanceof PopupAnnotationElement?i.prepend(s):_classStaticPrivateMethodGet(AnnotationLayer,AnnotationLayer,_appendElement).call(AnnotationLayer,s,t.id,i,o)}}_classStaticPrivateMethodGet(this,AnnotationLayer,_setAnnotationCanvasMap).call(this,i,t.annotationCanvasMap)}static update(t){const{annotationCanvasMap:e,div:i,viewport:r}=t;(0,s.setLayerDimensions)(i,{rotation:r.rotation});_classStaticPrivateMethodGet(this,AnnotationLayer,_setAnnotationCanvasMap).call(this,i,e);i.hidden=!1}}e.AnnotationLayer=AnnotationLayer;function _appendElement(t,e,i,r){const a=t.firstChild||t;a.id=`${s.AnnotationPrefix}${e}`;i.append(t);null==r||r.moveElementInDOM(i,t,a,!1)}function _setAnnotationCanvasMap(t,e){if(e){for(const[i,r]of e){const e=t.querySelector(`[data-annotation-id="${i}"]`);if(!e)continue;const{firstChild:s}=e;s?"CANVAS"===s.nodeName?s.replaceWith(r):s.before(r):e.append(r)}e.clear()}}},(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.ColorConverters=void 0;function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}e.ColorConverters=class ColorConverters{static CMYK_G(t){let[e,i,r,s]=t;return["G",1-Math.min(1,.3*e+.59*r+.11*i+s)]}static G_CMYK(t){let[e]=t;return["CMYK",0,0,0,1-e]}static G_RGB(t){let[e]=t;return["RGB",e,e,e]}static G_HTML(t){let[e]=t;const i=makeColorComp(e);return`#${i}${i}${i}`}static RGB_G(t){let[e,i,r]=t;return["G",.3*e+.59*i+.11*r]}static RGB_HTML(t){let[e,i,r]=t;return`#${makeColorComp(e)}${makeColorComp(i)}${makeColorComp(r)}`}static T_HTML(){return"#00000000"}static CMYK_RGB(t){let[e,i,r,s]=t;return["RGB",1-Math.min(1,e+s),1-Math.min(1,r+s),1-Math.min(1,i+s)]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK(t){let[e,i,r]=t;const s=1-e,a=1-i,n=1-r;return["CMYK",s,a,n,Math.min(s,a,n)]}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.XfaLayer=void 0;var r=i(151);e.XfaLayer=class XfaLayer{static setupStorage(t,e,i,r,s){const a=r.getValue(e,{value:null});switch(i.name){case"textarea":null!==a.value&&(t.textContent=a.value);if("print"===s)break;t.addEventListener("input",(t=>{r.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){a.value===i.attributes.xfaOn?t.setAttribute("checked",!0):a.value===i.attributes.xfaOff&&t.removeAttribute("checked");if("print"===s)break;t.addEventListener("change",(t=>{r.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==a.value&&t.setAttribute("value",a.value);if("print"===s)break;t.addEventListener("input",(t=>{r.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==a.value)for(const t of i.children)t.attributes.value===a.value&&(t.attributes.selected=!0);t.addEventListener("input",(t=>{const i=t.target.options,s=-1===i.selectedIndex?"":i[i.selectedIndex].value;r.setValue(e,{value:s})}))}}static setAttributes(t){let{html:e,element:i,storage:r=null,intent:s,linkService:a}=t;const{attributes:n}=i,o=e instanceof HTMLAnchorElement;"radio"===n.type&&(n.name=`${n.name}-${s}`);for(const[t,i]of Object.entries(n))if(null!=i)switch(t){case"class":i.length&&e.setAttribute(t,i.join(" "));break;case"dataId":break;case"id":e.setAttribute("data-element-id",i);break;case"style":Object.assign(e.style,i);break;case"textContent":e.textContent=i;break;default:(!o||"href"!==t&&"newWindow"!==t)&&e.setAttribute(t,i)}o&&a.addLinkAttributes(e,n.href,n.newWindow);r&&n.dataId&&this.setupStorage(e,n.dataId,i,r)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,a=t.intent||"display",n=document.createElement(s.name);s.attributes&&this.setAttributes({html:n,element:s,intent:a,linkService:i});const o=[[s,-1,n]],l=t.div;l.append(n);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=e}"richText"!==a&&l.setAttribute("class","xfaLayer xfaFont");const c=[];for(;o.length>0;){var d;const[t,s,n]=o.at(-1);if(s+1===t.children.length){o.pop();continue}const l=t.children[++o.at(-1)[1]];if(null===l)continue;const{name:h}=l;if("#text"===h){const t=document.createTextNode(l.value);c.push(t);n.append(t);continue}let u;u=null!=l&&null!==(d=l.attributes)&&void 0!==d&&d.xmlns?document.createElementNS(l.attributes.xmlns,h):document.createElement(h);n.append(u);l.attributes&&this.setAttributes({html:u,element:l,storage:e,intent:a,linkService:i});if(l.children&&l.children.length>0)o.push([l,-1,u]);else if(l.value){const t=document.createTextNode(l.value);r.XfaText.shouldBuildText(h)&&c.push(t);u.append(t)}}for(const t of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}},(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.SVGGraphics=void 0;var r=i(139),s=i(1),a=i(3);let n=class{constructor(){(0,s.unreachable)("Not implemented: SVGGraphics")}};e.SVGGraphics=n;{const o={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},l="http://www.w3.org/XML/1998/namespace",c="http://www.w3.org/1999/xlink",d=["butt","round","square"],h=["miter","round","bevel"],createObjectURL=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(URL.createObjectURL&&"undefined"!=typeof Blob&&!i)return URL.createObjectURL(new Blob([t],{type:e}));const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let s=`data:${e};base64,`;for(let e=0,i=t.length;e<i;e+=3){const a=255&t[e],n=255&t[e+1],o=255&t[e+2];s+=r[a>>2]+r[(3&a)<<4|n>>4]+r[e+1<i?(15&n)<<2|o>>6:64]+r[e+2<i?63&o:64]}return s},u=function(){const t=new Uint8Array([137,80,78,71,13,10,26,10]),e=12,i=new Int32Array(256);for(let t=0;t<256;t++){let e=t;for(let t=0;t<8;t++)e=1&e?3988292384^e>>1&2147483647:e>>1&2147483647;i[t]=e}function writePngChunk(t,e,r,s){let a=s;const n=e.length;r[a]=n>>24&255;r[a+1]=n>>16&255;r[a+2]=n>>8&255;r[a+3]=255&n;a+=4;r[a]=255&t.charCodeAt(0);r[a+1]=255&t.charCodeAt(1);r[a+2]=255&t.charCodeAt(2);r[a+3]=255&t.charCodeAt(3);a+=4;r.set(e,a);a+=e.length;const o=function crc32(t,e,r){let s=-1;for(let a=e;a<r;a++){const e=255&(s^t[a]);s=s>>>8^i[e]}return-1^s}(r,s+4,a);r[a]=o>>24&255;r[a+1]=o>>16&255;r[a+2]=o>>8&255;r[a+3]=255&o}function deflateSyncUncompressed(t){let e=t.length;const i=65535,r=Math.ceil(e/i),s=new Uint8Array(2+e+5*r+4);let a=0;s[a++]=120;s[a++]=156;let n=0;for(;e>i;){s[a++]=0;s[a++]=255;s[a++]=255;s[a++]=0;s[a++]=0;s.set(t.subarray(n,n+i),a);a+=i;n+=i;e-=i}s[a++]=1;s[a++]=255&e;s[a++]=e>>8&255;s[a++]=255&~e;s[a++]=(65535&~e)>>8&255;s.set(t.subarray(n),a);a+=t.length-n;const o=function adler32(t,e,i){let r=1,s=0;for(let a=e;a<i;++a){r=(r+(255&t[a]))%65521;s=(s+r)%65521}return s<<16|r}(t,0,t.length);s[a++]=o>>24&255;s[a++]=o>>16&255;s[a++]=o>>8&255;s[a++]=255&o;return s}function encode(i,r,n,o){const l=i.width,c=i.height;let d,h,u;const p=i.data;switch(r){case s.ImageKind.GRAYSCALE_1BPP:h=0;d=1;u=l+7>>3;break;case s.ImageKind.RGB_24BPP:h=2;d=8;u=3*l;break;case s.ImageKind.RGBA_32BPP:h=6;d=8;u=4*l;break;default:throw new Error("invalid format")}const f=new Uint8Array((1+u)*c);let g=0,m=0;for(let t=0;t<c;++t){f[g++]=0;f.set(p.subarray(m,m+u),g);m+=u;g+=u}if(r===s.ImageKind.GRAYSCALE_1BPP&&o){g=0;for(let t=0;t<c;t++){g++;for(let t=0;t<u;t++)f[g++]^=255}}const v=new Uint8Array([l>>24&255,l>>16&255,l>>8&255,255&l,c>>24&255,c>>16&255,c>>8&255,255&c,d,h,0,0,0]),_=function deflateSync(t){if(!a.isNodeJS)return deflateSyncUncompressed(t);try{let e;e=parseInt(process.versions.node)>=8?t:Buffer.from(t);const i=require("zlib").deflateSync(e,{level:9});return i instanceof Uint8Array?i:new Uint8Array(i)}catch(t){(0,s.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+t)}return deflateSyncUncompressed(t)}(f),b=t.length+3*e+v.length+_.length,y=new Uint8Array(b);let A=0;y.set(t,A);A+=t.length;writePngChunk("IHDR",v,y,A);A+=e+v.length;writePngChunk("IDATA",_,y,A);A+=e+_.length;writePngChunk("IEND",new Uint8Array(0),y,A);return createObjectURL(y,"image/png",n)}return function convertImgDataToPng(t,e,i){return encode(t,void 0===t.kind?s.ImageKind.GRAYSCALE_1BPP:t.kind,e,i)}}();class SVGExtraState{constructor(){this.fontSizeScale=1;this.fontWeight=o.fontWeight;this.fontSize=0;this.textMatrix=s.IDENTITY_MATRIX;this.fontMatrix=s.FONT_IDENTITY_MATRIX;this.leading=0;this.textRenderingMode=s.TextRenderingMode.FILL;this.textMatrixScale=1;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=o.fillColor;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.activeClipUrl=null;this.clipGroup=null;this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(t,e){this.x=t;this.y=e}}function opListToTree(t){let e=[];const i=[];for(const r of t)if("save"!==r.fn)"restore"===r.fn?e=i.pop():e.push(r);else{e.push({fnId:92,fn:"group",items:[]});i.push(e);e=e.at(-1).items}return e}function pf(t){if(Number.isInteger(t))return t.toString();const e=t.toFixed(10);let i=e.length-1;if("0"!==e[i])return e;do{i--}while("0"===e[i]);return e.substring(0,"."===e[i]?i:i+1)}function pm(t){if(0===t[4]&&0===t[5]){if(0===t[1]&&0===t[2])return 1===t[0]&&1===t[3]?"":`scale(${pf(t[0])} ${pf(t[3])})`;if(t[0]===t[3]&&t[1]===-t[2]){return`rotate(${pf(180*Math.acos(t[0])/Math.PI)})`}}else if(1===t[0]&&0===t[1]&&0===t[2]&&1===t[3])return`translate(${pf(t[4])} ${pf(t[5])})`;return`matrix(${pf(t[0])} ${pf(t[1])} ${pf(t[2])} ${pf(t[3])} ${pf(t[4])} ${pf(t[5])})`}let p=0,f=0,g=0;e.SVGGraphics=n=class{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(0,r.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future.");this.svgFactory=new r.DOMSVGFactory;this.current=new SVGExtraState;this.transformMatrix=s.IDENTITY_MATRIX;this.transformStack=[];this.extraStack=[];this.commonObjs=t;this.objs=e;this.pendingClip=null;this.pendingEOFill=!1;this.embedFonts=!1;this.embeddedFonts=Object.create(null);this.cssStyle=null;this.forceDataSchema=!!i;this._operatorIdMapping=[];for(const t in s.OPS)this._operatorIdMapping[s.OPS[t]]=t}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}save(){this.transformStack.push(this.transformMatrix);const t=this.current;this.extraStack.push(t);this.current=t.clone()}restore(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.pendingClip=null;this.tgrp=null}group(t){this.save();this.executeOpTree(t);this.restore()}loadDependencies(t){const e=t.fnArray,i=t.argsArray;for(let t=0,r=e.length;t<r;t++)if(e[t]===s.OPS.dependency)for(const e of i[t]){const t=e.startsWith("g_")?this.commonObjs:this.objs,i=new Promise((i=>{t.get(e,i)}));this.current.dependencies.push(i)}return Promise.all(this.current.dependencies)}transform(t,e,i,r,a,n){const o=[t,e,i,r,a,n];this.transformMatrix=s.Util.transform(this.transformMatrix,o);this.tgrp=null}getSVG(t,e){this.viewport=e;const i=this._initialize(e);return this.loadDependencies(t).then((()=>{this.transformMatrix=s.IDENTITY_MATRIX;this.executeOpTree(this.convertOpList(t));return i}))}convertOpList(t){const e=this._operatorIdMapping,i=t.argsArray,r=t.fnArray,s=[];for(let t=0,a=r.length;t<a;t++){const a=r[t];s.push({fnId:a,fn:e[a],args:i[t]})}return opListToTree(s)}executeOpTree(t){for(const e of t){const t=e.fn,i=e.fnId,r=e.args;switch(0|i){case s.OPS.beginText:this.beginText();break;case s.OPS.dependency:break;case s.OPS.setLeading:this.setLeading(r);break;case s.OPS.setLeadingMoveText:this.setLeadingMoveText(r[0],r[1]);break;case s.OPS.setFont:this.setFont(r);break;case s.OPS.showText:case s.OPS.showSpacedText:this.showText(r[0]);break;case s.OPS.endText:this.endText();break;case s.OPS.moveText:this.moveText(r[0],r[1]);break;case s.OPS.setCharSpacing:this.setCharSpacing(r[0]);break;case s.OPS.setWordSpacing:this.setWordSpacing(r[0]);break;case s.OPS.setHScale:this.setHScale(r[0]);break;case s.OPS.setTextMatrix:this.setTextMatrix(r[0],r[1],r[2],r[3],r[4],r[5]);break;case s.OPS.setTextRise:this.setTextRise(r[0]);break;case s.OPS.setTextRenderingMode:this.setTextRenderingMode(r[0]);break;case s.OPS.setLineWidth:this.setLineWidth(r[0]);break;case s.OPS.setLineJoin:this.setLineJoin(r[0]);break;case s.OPS.setLineCap:this.setLineCap(r[0]);break;case s.OPS.setMiterLimit:this.setMiterLimit(r[0]);break;case s.OPS.setFillRGBColor:this.setFillRGBColor(r[0],r[1],r[2]);break;case s.OPS.setStrokeRGBColor:this.setStrokeRGBColor(r[0],r[1],r[2]);break;case s.OPS.setStrokeColorN:this.setStrokeColorN(r);break;case s.OPS.setFillColorN:this.setFillColorN(r);break;case s.OPS.shadingFill:this.shadingFill(r[0]);break;case s.OPS.setDash:this.setDash(r[0],r[1]);break;case s.OPS.setRenderingIntent:this.setRenderingIntent(r[0]);break;case s.OPS.setFlatness:this.setFlatness(r[0]);break;case s.OPS.setGState:this.setGState(r[0]);break;case s.OPS.fill:this.fill();break;case s.OPS.eoFill:this.eoFill();break;case s.OPS.stroke:this.stroke();break;case s.OPS.fillStroke:this.fillStroke();break;case s.OPS.eoFillStroke:this.eoFillStroke();break;case s.OPS.clip:this.clip("nonzero");break;case s.OPS.eoClip:this.clip("evenodd");break;case s.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case s.OPS.paintImageXObject:this.paintImageXObject(r[0]);break;case s.OPS.paintInlineImageXObject:this.paintInlineImageXObject(r[0]);break;case s.OPS.paintImageMaskXObject:this.paintImageMaskXObject(r[0]);break;case s.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(r[0],r[1]);break;case s.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case s.OPS.closePath:this.closePath();break;case s.OPS.closeStroke:this.closeStroke();break;case s.OPS.closeFillStroke:this.closeFillStroke();break;case s.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case s.OPS.nextLine:this.nextLine();break;case s.OPS.transform:this.transform(r[0],r[1],r[2],r[3],r[4],r[5]);break;case s.OPS.constructPath:this.constructPath(r[0],r[1]);break;case s.OPS.endPath:this.endPath();break;case 92:this.group(e.items);break;default:(0,s.warn)(`Unimplemented operator ${t}`)}}}setWordSpacing(t){this.current.wordSpacing=t}setCharSpacing(t){this.current.charSpacing=t}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(t,e,i,r,s,a){const n=this.current;n.textMatrix=n.lineMatrix=[t,e,i,r,s,a];n.textMatrixScale=Math.hypot(t,e);n.x=n.lineX=0;n.y=n.lineY=0;n.xcoords=[];n.ycoords=[];n.tspan=this.svgFactory.createElement("svg:tspan");n.tspan.setAttributeNS(null,"font-family",n.fontFamily);n.tspan.setAttributeNS(null,"font-size",`${pf(n.fontSize)}px`);n.tspan.setAttributeNS(null,"y",pf(-n.y));n.txtElement=this.svgFactory.createElement("svg:text");n.txtElement.append(n.tspan)}beginText(){const t=this.current;t.x=t.lineX=0;t.y=t.lineY=0;t.textMatrix=s.IDENTITY_MATRIX;t.lineMatrix=s.IDENTITY_MATRIX;t.textMatrixScale=1;t.tspan=this.svgFactory.createElement("svg:tspan");t.txtElement=this.svgFactory.createElement("svg:text");t.txtgrp=this.svgFactory.createElement("svg:g");t.xcoords=[];t.ycoords=[]}moveText(t,e){const i=this.current;i.x=i.lineX+=t;i.y=i.lineY+=e;i.xcoords=[];i.ycoords=[];i.tspan=this.svgFactory.createElement("svg:tspan");i.tspan.setAttributeNS(null,"font-family",i.fontFamily);i.tspan.setAttributeNS(null,"font-size",`${pf(i.fontSize)}px`);i.tspan.setAttributeNS(null,"y",pf(-i.y))}showText(t){const e=this.current,i=e.font,r=e.fontSize;if(0===r)return;const a=e.fontSizeScale,n=e.charSpacing,c=e.wordSpacing,d=e.fontDirection,h=e.textHScale*d,u=i.vertical,p=u?1:-1,f=i.defaultVMetrics,g=r*e.fontMatrix[0];let m=0;for(const s of t){if(null===s){m+=d*c;continue}if("number"==typeof s){m+=p*s*r/1e3;continue}const t=(s.isSpace?c:0)+n,o=s.fontChar;let l,h,v,_=s.width;if(u){let t;const e=s.vmetric||f;t=s.vmetric?e[1]:.5*_;t=-t*g;const i=e[2]*g;_=e?-e[0]:_;l=t/a;h=(m+i)/a}else{l=m/a;h=0}if(s.isInFont||i.missingFile){e.xcoords.push(e.x+l);u&&e.ycoords.push(-e.y+h);e.tspan.textContent+=o}v=u?_*g-t*d:_*g+t*d;m+=v}e.tspan.setAttributeNS(null,"x",e.xcoords.map(pf).join(" "));u?e.tspan.setAttributeNS(null,"y",e.ycoords.map(pf).join(" ")):e.tspan.setAttributeNS(null,"y",pf(-e.y));u?e.y-=m:e.x+=m*h;e.tspan.setAttributeNS(null,"font-family",e.fontFamily);e.tspan.setAttributeNS(null,"font-size",`${pf(e.fontSize)}px`);e.fontStyle!==o.fontStyle&&e.tspan.setAttributeNS(null,"font-style",e.fontStyle);e.fontWeight!==o.fontWeight&&e.tspan.setAttributeNS(null,"font-weight",e.fontWeight);const v=e.textRenderingMode&s.TextRenderingMode.FILL_STROKE_MASK;if(v===s.TextRenderingMode.FILL||v===s.TextRenderingMode.FILL_STROKE){e.fillColor!==o.fillColor&&e.tspan.setAttributeNS(null,"fill",e.fillColor);e.fillAlpha<1&&e.tspan.setAttributeNS(null,"fill-opacity",e.fillAlpha)}else e.textRenderingMode===s.TextRenderingMode.ADD_TO_PATH?e.tspan.setAttributeNS(null,"fill","transparent"):e.tspan.setAttributeNS(null,"fill","none");if(v===s.TextRenderingMode.STROKE||v===s.TextRenderingMode.FILL_STROKE){const t=1/(e.textMatrixScale||1);this._setStrokeAttributes(e.tspan,t)}let _=e.textMatrix;if(0!==e.textRise){_=_.slice();_[5]+=e.textRise}e.txtElement.setAttributeNS(null,"transform",`${pm(_)} scale(${pf(h)}, -1)`);e.txtElement.setAttributeNS(l,"xml:space","preserve");e.txtElement.append(e.tspan);e.txtgrp.append(e.txtElement);this._ensureTransformGroup().append(e.txtElement)}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}addFontStyle(t){if(!t.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');if(!this.cssStyle){this.cssStyle=this.svgFactory.createElement("svg:style");this.cssStyle.setAttributeNS(null,"type","text/css");this.defs.append(this.cssStyle)}const e=createObjectURL(t.data,t.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${t.loadedName}"; src: url(${e}); }\n`}setFont(t){const e=this.current,i=this.commonObjs.get(t[0]);let r=t[1];e.font=i;if(this.embedFonts&&!i.missingFile&&!this.embeddedFonts[i.loadedName]){this.addFontStyle(i);this.embeddedFonts[i.loadedName]=i}e.fontMatrix=i.fontMatrix||s.FONT_IDENTITY_MATRIX;let a="normal";i.black?a="900":i.bold&&(a="bold");const n=i.italic?"italic":"normal";if(r<0){r=-r;e.fontDirection=-1}else e.fontDirection=1;e.fontSize=r;e.fontFamily=i.loadedName;e.fontWeight=a;e.fontStyle=n;e.tspan=this.svgFactory.createElement("svg:tspan");e.tspan.setAttributeNS(null,"y",pf(-e.y));e.xcoords=[];e.ycoords=[]}endText(){var t;const e=this.current;if(e.textRenderingMode&s.TextRenderingMode.ADD_TO_PATH_FLAG&&null!==(t=e.txtElement)&&void 0!==t&&t.hasChildNodes()){e.element=e.txtElement;this.clip("nonzero");this.endPath()}}setLineWidth(t){t>0&&(this.current.lineWidth=t)}setLineCap(t){this.current.lineCap=d[t]}setLineJoin(t){this.current.lineJoin=h[t]}setMiterLimit(t){this.current.miterLimit=t}setStrokeAlpha(t){this.current.strokeAlpha=t}setStrokeRGBColor(t,e,i){this.current.strokeColor=s.Util.makeHexColor(t,e,i)}setFillAlpha(t){this.current.fillAlpha=t}setFillRGBColor(t,e,i){this.current.fillColor=s.Util.makeHexColor(t,e,i);this.current.tspan=this.svgFactory.createElement("svg:tspan");this.current.xcoords=[];this.current.ycoords=[]}setStrokeColorN(t){this.current.strokeColor=this._makeColorN_Pattern(t)}setFillColorN(t){this.current.fillColor=this._makeColorN_Pattern(t)}shadingFill(t){const e=this.viewport.width,i=this.viewport.height,r=s.Util.inverseTransform(this.transformMatrix),a=s.Util.applyTransform([0,0],r),n=s.Util.applyTransform([0,i],r),o=s.Util.applyTransform([e,0],r),l=s.Util.applyTransform([e,i],r),c=Math.min(a[0],n[0],o[0],l[0]),d=Math.min(a[1],n[1],o[1],l[1]),h=Math.max(a[0],n[0],o[0],l[0]),u=Math.max(a[1],n[1],o[1],l[1]),p=this.svgFactory.createElement("svg:rect");p.setAttributeNS(null,"x",c);p.setAttributeNS(null,"y",d);p.setAttributeNS(null,"width",h-c);p.setAttributeNS(null,"height",u-d);p.setAttributeNS(null,"fill",this._makeShadingPattern(t));this.current.fillAlpha<1&&p.setAttributeNS(null,"fill-opacity",this.current.fillAlpha);this._ensureTransformGroup().append(p)}_makeColorN_Pattern(t){return"TilingPattern"===t[0]?this._makeTilingPattern(t):this._makeShadingPattern(t)}_makeTilingPattern(t){const e=t[1],i=t[2],r=t[3]||s.IDENTITY_MATRIX,[a,n,o,l]=t[4],c=t[5],d=t[6],h=t[7],u="shading"+g++,[p,f,m,v]=s.Util.normalizeRect([...s.Util.applyTransform([a,n],r),...s.Util.applyTransform([o,l],r)]),[_,b]=s.Util.singularValueDecompose2dScale(r),y=c*_,A=d*b,S=this.svgFactory.createElement("svg:pattern");S.setAttributeNS(null,"id",u);S.setAttributeNS(null,"patternUnits","userSpaceOnUse");S.setAttributeNS(null,"width",y);S.setAttributeNS(null,"height",A);S.setAttributeNS(null,"x",`${p}`);S.setAttributeNS(null,"y",`${f}`);const P=this.svg,x=this.transformMatrix,w=this.current.fillColor,E=this.current.strokeColor,C=this.svgFactory.create(m-p,v-f);this.svg=C;this.transformMatrix=r;if(2===h){const t=s.Util.makeHexColor(...e);this.current.fillColor=t;this.current.strokeColor=t}this.executeOpTree(this.convertOpList(i));this.svg=P;this.transformMatrix=x;this.current.fillColor=w;this.current.strokeColor=E;S.append(C.childNodes[0]);this.defs.append(S);return`url(#${u})`}_makeShadingPattern(t){"string"==typeof t&&(t=this.objs.get(t));switch(t[0]){case"RadialAxial":const e="shading"+g++,i=t[3];let r;switch(t[1]){case"axial":const i=t[4],s=t[5];r=this.svgFactory.createElement("svg:linearGradient");r.setAttributeNS(null,"id",e);r.setAttributeNS(null,"gradientUnits","userSpaceOnUse");r.setAttributeNS(null,"x1",i[0]);r.setAttributeNS(null,"y1",i[1]);r.setAttributeNS(null,"x2",s[0]);r.setAttributeNS(null,"y2",s[1]);break;case"radial":const a=t[4],n=t[5],o=t[6],l=t[7];r=this.svgFactory.createElement("svg:radialGradient");r.setAttributeNS(null,"id",e);r.setAttributeNS(null,"gradientUnits","userSpaceOnUse");r.setAttributeNS(null,"cx",n[0]);r.setAttributeNS(null,"cy",n[1]);r.setAttributeNS(null,"r",l);r.setAttributeNS(null,"fx",a[0]);r.setAttributeNS(null,"fy",a[1]);r.setAttributeNS(null,"fr",o);break;default:throw new Error(`Unknown RadialAxial type: ${t[1]}`)}for(const t of i){const e=this.svgFactory.createElement("svg:stop");e.setAttributeNS(null,"offset",t[0]);e.setAttributeNS(null,"stop-color",t[1]);r.append(e)}this.defs.append(r);return`url(#${e})`;case"Mesh":(0,s.warn)("Unimplemented pattern Mesh");return null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${t[0]}`)}}setDash(t,e){this.current.dashArray=t;this.current.dashPhase=e}constructPath(t,e){const i=this.current;let r=i.x,a=i.y,n=[],o=0;for(const i of t)switch(0|i){case s.OPS.rectangle:r=e[o++];a=e[o++];const t=r+e[o++],i=a+e[o++];n.push("M",pf(r),pf(a),"L",pf(t),pf(a),"L",pf(t),pf(i),"L",pf(r),pf(i),"Z");break;case s.OPS.moveTo:r=e[o++];a=e[o++];n.push("M",pf(r),pf(a));break;case s.OPS.lineTo:r=e[o++];a=e[o++];n.push("L",pf(r),pf(a));break;case s.OPS.curveTo:r=e[o+4];a=e[o+5];n.push("C",pf(e[o]),pf(e[o+1]),pf(e[o+2]),pf(e[o+3]),pf(r),pf(a));o+=6;break;case s.OPS.curveTo2:n.push("C",pf(r),pf(a),pf(e[o]),pf(e[o+1]),pf(e[o+2]),pf(e[o+3]));r=e[o+2];a=e[o+3];o+=4;break;case s.OPS.curveTo3:r=e[o+2];a=e[o+3];n.push("C",pf(e[o]),pf(e[o+1]),pf(r),pf(a),pf(r),pf(a));o+=4;break;case s.OPS.closePath:n.push("Z")}n=n.join(" ");if(i.path&&t.length>0&&t[0]!==s.OPS.rectangle&&t[0]!==s.OPS.moveTo)n=i.path.getAttributeNS(null,"d")+n;else{i.path=this.svgFactory.createElement("svg:path");this._ensureTransformGroup().append(i.path)}i.path.setAttributeNS(null,"d",n);i.path.setAttributeNS(null,"fill","none");i.element=i.path;i.setCurrentPoint(r,a)}endPath(){const t=this.current;t.path=null;if(!this.pendingClip)return;if(!t.element){this.pendingClip=null;return}const e="clippath"+p++,i=this.svgFactory.createElement("svg:clipPath");i.setAttributeNS(null,"id",e);i.setAttributeNS(null,"transform",pm(this.transformMatrix));const r=t.element.cloneNode(!0);"evenodd"===this.pendingClip?r.setAttributeNS(null,"clip-rule","evenodd"):r.setAttributeNS(null,"clip-rule","nonzero");this.pendingClip=null;i.append(r);this.defs.append(i);if(t.activeClipUrl){t.clipGroup=null;for(const t of this.extraStack)t.clipGroup=null;i.setAttributeNS(null,"clip-path",t.activeClipUrl)}t.activeClipUrl=`url(#${e})`;this.tgrp=null}clip(t){this.pendingClip=t}closePath(){const t=this.current;if(t.path){const e=`${t.path.getAttributeNS(null,"d")}Z`;t.path.setAttributeNS(null,"d",e)}}setLeading(t){this.current.leading=-t}setTextRise(t){this.current.textRise=t}setTextRenderingMode(t){this.current.textRenderingMode=t}setHScale(t){this.current.textHScale=t/100}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i);break;case"CA":this.setStrokeAlpha(i);break;case"ca":this.setFillAlpha(i);break;default:(0,s.warn)(`Unimplemented graphic state operator ${e}`)}}fill(){const t=this.current;if(t.element){t.element.setAttributeNS(null,"fill",t.fillColor);t.element.setAttributeNS(null,"fill-opacity",t.fillAlpha);this.endPath()}}stroke(){const t=this.current;if(t.element){this._setStrokeAttributes(t.element);t.element.setAttributeNS(null,"fill","none");this.endPath()}}_setStrokeAttributes(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;const i=this.current;let r=i.dashArray;1!==e&&r.length>0&&(r=r.map((function(t){return e*t})));t.setAttributeNS(null,"stroke",i.strokeColor);t.setAttributeNS(null,"stroke-opacity",i.strokeAlpha);t.setAttributeNS(null,"stroke-miterlimit",pf(i.miterLimit));t.setAttributeNS(null,"stroke-linecap",i.lineCap);t.setAttributeNS(null,"stroke-linejoin",i.lineJoin);t.setAttributeNS(null,"stroke-width",pf(e*i.lineWidth)+"px");t.setAttributeNS(null,"stroke-dasharray",r.map(pf).join(" "));t.setAttributeNS(null,"stroke-dashoffset",pf(e*i.dashPhase)+"px")}eoFill(){var t;null===(t=this.current.element)||void 0===t||t.setAttributeNS(null,"fill-rule","evenodd");this.fill()}fillStroke(){this.stroke();this.fill()}eoFillStroke(){var t;null===(t=this.current.element)||void 0===t||t.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()}closeStroke(){this.closePath();this.stroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.closePath();this.eoFillStroke()}paintSolidColorImageMask(){const t=this.svgFactory.createElement("svg:rect");t.setAttributeNS(null,"x","0");t.setAttributeNS(null,"y","0");t.setAttributeNS(null,"width","1px");t.setAttributeNS(null,"height","1px");t.setAttributeNS(null,"fill",this.current.fillColor);this._ensureTransformGroup().append(t)}paintImageXObject(t){const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,s.warn)(`Dependent image with object ID ${t} is not ready yet`)}paintInlineImageXObject(t,e){const i=t.width,r=t.height,s=u(t,this.forceDataSchema,!!e),a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0");a.setAttributeNS(null,"y","0");a.setAttributeNS(null,"width",pf(i));a.setAttributeNS(null,"height",pf(r));this.current.element=a;this.clip("nonzero");const n=this.svgFactory.createElement("svg:image");n.setAttributeNS(c,"xlink:href",s);n.setAttributeNS(null,"x","0");n.setAttributeNS(null,"y",pf(-r));n.setAttributeNS(null,"width",pf(i)+"px");n.setAttributeNS(null,"height",pf(r)+"px");n.setAttributeNS(null,"transform",`scale(${pf(1/i)} ${pf(-1/r)})`);e?e.append(n):this._ensureTransformGroup().append(n)}paintImageMaskXObject(t){const e=this.getObject(t.data,t);if(e.bitmap){(0,s.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");return}const i=this.current,r=e.width,a=e.height,n=i.fillColor;i.maskId="mask"+f++;const o=this.svgFactory.createElement("svg:mask");o.setAttributeNS(null,"id",i.maskId);const l=this.svgFactory.createElement("svg:rect");l.setAttributeNS(null,"x","0");l.setAttributeNS(null,"y","0");l.setAttributeNS(null,"width",pf(r));l.setAttributeNS(null,"height",pf(a));l.setAttributeNS(null,"fill",n);l.setAttributeNS(null,"mask",`url(#${i.maskId})`);this.defs.append(o);this._ensureTransformGroup().append(l);this.paintInlineImageXObject(e,o)}paintFormXObjectBegin(t,e){Array.isArray(t)&&6===t.length&&this.transform(t[0],t[1],t[2],t[3],t[4],t[5]);if(e){const t=e[2]-e[0],i=e[3]-e[1],r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x",e[0]);r.setAttributeNS(null,"y",e[1]);r.setAttributeNS(null,"width",pf(t));r.setAttributeNS(null,"height",pf(i));this.current.element=r;this.clip("nonzero");this.endPath()}}paintFormXObjectEnd(){}_initialize(t){const e=this.svgFactory.create(t.width,t.height),i=this.svgFactory.createElement("svg:defs");e.append(i);this.defs=i;const r=this.svgFactory.createElement("svg:g");r.setAttributeNS(null,"transform",pm(t.transform));e.append(r);this.svg=r;return e}_ensureClipGroup(){if(!this.current.clipGroup){const t=this.svgFactory.createElement("svg:g");t.setAttributeNS(null,"clip-path",this.current.activeClipUrl);this.svg.append(t);this.current.clipGroup=t}return this.current.clipGroup}_ensureTransformGroup(){if(!this.tgrp){this.tgrp=this.svgFactory.createElement("svg:g");this.tgrp.setAttributeNS(null,"transform",pm(this.transformMatrix));this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)}return this.tgrp}}}}],__webpack_module_cache__={};function __w_pdfjs_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var i=__webpack_module_cache__[t]={exports:{}};__webpack_modules__[t](i,i.exports,__w_pdfjs_require__);return i.exports}var __webpack_exports__={};(()=>{"use strict";var t=__webpack_exports__;Object.defineProperty(t,"__esModule",{value:!0});Object.defineProperty(t,"AbortException",{enumerable:!0,get:function(){return e.AbortException}});Object.defineProperty(t,"AnnotationEditorLayer",{enumerable:!0,get:function(){return a.AnnotationEditorLayer}});Object.defineProperty(t,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return e.AnnotationEditorParamsType}});Object.defineProperty(t,"AnnotationEditorType",{enumerable:!0,get:function(){return e.AnnotationEditorType}});Object.defineProperty(t,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return n.AnnotationEditorUIManager}});Object.defineProperty(t,"AnnotationLayer",{enumerable:!0,get:function(){return o.AnnotationLayer}});Object.defineProperty(t,"AnnotationMode",{enumerable:!0,get:function(){return e.AnnotationMode}});Object.defineProperty(t,"CMapCompressionType",{enumerable:!0,get:function(){return e.CMapCompressionType}});Object.defineProperty(t,"FeatureTest",{enumerable:!0,get:function(){return e.FeatureTest}});Object.defineProperty(t,"GlobalWorkerOptions",{enumerable:!0,get:function(){return l.GlobalWorkerOptions}});Object.defineProperty(t,"InvalidPDFException",{enumerable:!0,get:function(){return e.InvalidPDFException}});Object.defineProperty(t,"MissingPDFException",{enumerable:!0,get:function(){return e.MissingPDFException}});Object.defineProperty(t,"OPS",{enumerable:!0,get:function(){return e.OPS}});Object.defineProperty(t,"PDFDataRangeTransport",{enumerable:!0,get:function(){return i.PDFDataRangeTransport}});Object.defineProperty(t,"PDFDateString",{enumerable:!0,get:function(){return r.PDFDateString}});Object.defineProperty(t,"PDFWorker",{enumerable:!0,get:function(){return i.PDFWorker}});Object.defineProperty(t,"PasswordResponses",{enumerable:!0,get:function(){return e.PasswordResponses}});Object.defineProperty(t,"PermissionFlag",{enumerable:!0,get:function(){return e.PermissionFlag}});Object.defineProperty(t,"PixelsPerInch",{enumerable:!0,get:function(){return r.PixelsPerInch}});Object.defineProperty(t,"RenderingCancelledException",{enumerable:!0,get:function(){return r.RenderingCancelledException}});Object.defineProperty(t,"SVGGraphics",{enumerable:!0,get:function(){return c.SVGGraphics}});Object.defineProperty(t,"UNSUPPORTED_FEATURES",{enumerable:!0,get:function(){return e.UNSUPPORTED_FEATURES}});Object.defineProperty(t,"UnexpectedResponseException",{enumerable:!0,get:function(){return e.UnexpectedResponseException}});Object.defineProperty(t,"Util",{enumerable:!0,get:function(){return e.Util}});Object.defineProperty(t,"VerbosityLevel",{enumerable:!0,get:function(){return e.VerbosityLevel}});Object.defineProperty(t,"XfaLayer",{enumerable:!0,get:function(){return d.XfaLayer}});Object.defineProperty(t,"build",{enumerable:!0,get:function(){return i.build}});Object.defineProperty(t,"createPromiseCapability",{enumerable:!0,get:function(){return e.createPromiseCapability}});Object.defineProperty(t,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return e.createValidAbsoluteUrl}});Object.defineProperty(t,"getDocument",{enumerable:!0,get:function(){return i.getDocument}});Object.defineProperty(t,"getFilenameFromUrl",{enumerable:!0,get:function(){return r.getFilenameFromUrl}});Object.defineProperty(t,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return r.getPdfFilenameFromUrl}});Object.defineProperty(t,"getXfaPageViewport",{enumerable:!0,get:function(){return r.getXfaPageViewport}});Object.defineProperty(t,"isDataScheme",{enumerable:!0,get:function(){return r.isDataScheme}});Object.defineProperty(t,"isPdfFile",{enumerable:!0,get:function(){return r.isPdfFile}});Object.defineProperty(t,"loadScript",{enumerable:!0,get:function(){return r.loadScript}});Object.defineProperty(t,"renderTextLayer",{enumerable:!0,get:function(){return s.renderTextLayer}});Object.defineProperty(t,"setLayerDimensions",{enumerable:!0,get:function(){return r.setLayerDimensions}});Object.defineProperty(t,"shadow",{enumerable:!0,get:function(){return e.shadow}});Object.defineProperty(t,"updateTextLayer",{enumerable:!0,get:function(){return s.updateTextLayer}});Object.defineProperty(t,"version",{enumerable:!0,get:function(){return i.version}});var e=__w_pdfjs_require__(1),i=__w_pdfjs_require__(135),r=__w_pdfjs_require__(139),s=__w_pdfjs_require__(158),a=__w_pdfjs_require__(159),n=__w_pdfjs_require__(138),o=__w_pdfjs_require__(164),l=__w_pdfjs_require__(146),c=__w_pdfjs_require__(167),d=__w_pdfjs_require__(166)})();return __webpack_exports__})()));