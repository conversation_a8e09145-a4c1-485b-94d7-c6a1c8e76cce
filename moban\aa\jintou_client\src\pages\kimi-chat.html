<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON>i问答 - 金投大脑</title>
  <link rel="stylesheet" href="../css/styles.css">
  <!-- 添加Prism.js用于代码高亮 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-python.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-java.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <!-- 添加marked.js用于Markdown渲染 -->
  <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      background-color: #f5f7fa;
      color: #333;
      display: flex;
      flex-direction: column;
      height: 100vh;
      margin: 0;
      padding: 0;
    }
    
    .header {
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 100;
    }
    
    .logo-container {
      display: flex;
      align-items: center;
    }
    
    .logo {
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #2c3e50;
      margin: 0;
    }
    
    .user-info {
      display: flex;
      align-items: center;
    }
    
    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #3498db;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .user-name {
      font-size: 14px;
      margin-right: 15px;
    }
    
    .logout-btn {
      background-color: transparent;
      color: #e74c3c;
      border: 1px solid #e74c3c;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .logout-btn:hover {
      background-color: #e74c3c;
      color: white;
    }
    
    .back-btn {
      background-color: transparent;
      color: #3498db;
      border: 1px solid #3498db;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      margin-right: 10px;
    }
    
    .back-btn:hover {
      background-color: #3498db;
      color: white;
    }
    
    .main-container {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
    
    .chat-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 20px;
      overflow: hidden;
    }
    
    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 15px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
      margin-bottom: 20px;
    }
    
    .message {
      margin-bottom: 24px;
      display: flex;
      align-items: flex-start;
    }
    
    .message-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
    }
    
    .user-message .message-avatar {
      background-color: #3498db;
    }
    
    .assistant-message .message-avatar {
      background-color: #2ecc71;
    }
    
    .message-content {
      flex: 1;
      padding: 15px 18px;
      border-radius: 12px;
      position: relative;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    
    .user-message .message-content {
      background-color: #2196F3;
      color: #ffffff;
    }
    
    .assistant-message .message-content {
      background-color: #f1f8e9;
    }
    
    .message-content::before {
      content: "";
      position: absolute;
      top: 12px;
      left: -8px;
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
    }
    
    .user-message .message-content::before {
      border-right: 8px solid #2196F3;
    }
    
    .assistant-message .message-content::before {
      border-right: 8px solid #f1f8e9;
    }
    
    .message-text {
      line-height: 1.5;
      white-space: pre-wrap;
    }
    
    .message-text p {
      margin-top: 0;
    }
    
    .message-text p:last-child {
      margin-bottom: 0;
    }
    
    .message-text pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      margin: 10px 0;
    }
    
    .message-text code {
      font-family: 'Courier New', Courier, monospace;
      background-color: rgba(0, 0, 0, 0.05);
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    .message-time {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
      text-align: right;
    }
    
    .user-message .message-time {
      color: #e0e0e0;
    }
    
    .input-container {
      display: flex;
      flex-direction: column;
    }
    
    .textarea-wrapper {
      position: relative;
      margin-bottom: 10px;
    }
    
    .message-input {
      width: 100%;
      height: 80px;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
      resize: none;
      font-family: 'Microsoft YaHei', sans-serif;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .message-input:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
    
    .controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .model-selector {
      display: flex;
      align-items: center;
    }
    
    .model-label {
      margin-right: 10px;
      font-size: 14px;
      color: #666;
    }
    
    .model-select {
      padding: 6px 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      background-color: white;
    }
    
    .send-btn {
      background-color: #3498db;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    .send-btn:hover {
      background-color: #2980b9;
    }
    
    .send-btn:disabled {
      background-color: #bdc3c7;
      cursor: not-allowed;
    }
    
    .loading-indicator {
      display: none;
      align-items: center;
      justify-content: center;
      margin-top: 10px;
      color: #666;
      font-size: 14px;
    }
    
    .loading-indicator.active {
      display: flex;
    }
    
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3498db;
      border-radius: 50%;
      margin-right: 8px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-message {
      color: #e74c3c;
      font-size: 14px;
      margin-top: 10px;
      display: none;
    }
    
    .error-message.active {
      display: block;
    }
    
    .info-message {
      color: #3498db;
      font-size: 14px;
      margin-top: 10px;
      display: none;
    }
    
    .info-message.active {
      display: block;
    }
    
    /* 文件上传按钮样式 */
    .file-upload {
      position: relative;
      display: inline-block;
      margin-right: 10px;
    }
    
    .file-upload-btn {
      background-color: #2ecc71;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 12px;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
    }
    
    .file-upload-btn:hover {
      background-color: #27ae60;
    }
    
    .file-upload-btn i {
      margin-right: 5px;
    }
    
    .file-upload input[type="file"] {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
    
    /* 图片预览区域样式 */
    .image-preview-container {
      margin-top: 10px;
      margin-bottom: 15px;
      display: none;
      position: relative;
    }
    
    .image-preview-container.active {
      display: block;
    }
    
    .image-preview {
      max-width: 300px;
      max-height: 200px;
      border-radius: 8px;
      border: 1px solid #ddd;
      object-fit: contain;
    }
    
    .pdf-preview {
      display: flex;
      align-items: center;
      padding: 12px;
      background-color: #f5f5f5;
      border-radius: 8px;
      border: 1px solid #ccc;
      max-width: 300px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .pdf-icon {
      color: #e74c3c;
      font-size: 28px;
      margin-right: 12px;
    }
    
    .pdf-filename {
      font-size: 15px;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 220px;
    }
    
    .remove-file-btn {
      position: absolute;
      top: -10px;
      right: -10px;
      background-color: #e74c3c;
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    .remove-file-btn:hover {
      background-color: #c0392b;
    }
    
    .left-controls {
      display: flex;
      align-items: center;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .main-container {
        flex-direction: column;
      }
      
      .chat-container {
        padding: 10px;
      }
      
      .message-input {
        height: 60px;
      }
      
      .controls {
        flex-direction: column;
        align-items: flex-start;
      }
      
      .model-selector {
        margin-bottom: 10px;
      }
      
      .file-upload {
        margin-bottom: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo-container">
      <img src="../../assets/icon.png" alt="金投AI Logo" class="logo">
      <h1 class="title">Kimi问答</h1>
    </div>
    <div class="actions">
      <button class="back-btn" onclick="goBack()">返回主页</button>
      <div class="user-info">
        <div class="user-avatar" id="user-avatar"></div>
        <span class="user-name" id="user-name"></span>
        <button class="logout-btn" onclick="logout()">退出登录</button>
      </div>
    </div>
  </div>
  
  <div class="main-container">
    <div class="chat-container">
      <div class="chat-messages" id="chat-messages">
        <div class="message assistant-message">
          <div class="message-avatar">K</div>
          <div class="message-content">
            <div class="message-text">
              <p>你好！我是Kimi，一个由Moonshot AI开发的大型语言模型。我可以帮助你回答问题、提供信息或进行对话。我现在支持图片和PDF分析，请告诉我你想了解什么？</p>
            </div>
            <div class="message-time">刚刚</div>
          </div>
        </div>
      </div>
      
      <div class="input-container">
        <!-- 图片预览区域 -->
        <div class="image-preview-container" id="image-preview-container">
          <!-- 图片或PDF预览将在这里显示 -->
          <button class="remove-file-btn" id="remove-file-btn">×</button>
        </div>
        
        <div class="textarea-wrapper">
          <textarea 
            id="message-input" 
            class="message-input" 
            placeholder="输入你的问题..." 
            rows="3"></textarea>
        </div>
        
        <div class="controls">
          <div class="left-controls">
            <!-- 文件上传按钮 -->
            <div class="file-upload">
              <button class="file-upload-btn">
                <span>上传图片/PDF</span>
              </button>
              <input type="file" id="file-upload" accept=".jpg,.jpeg,.png,.pdf">
            </div>
            
            <div class="model-selector">
              <span class="model-label">模型:</span>
              <select id="model-select" class="model-select">
                <option value="moonshot-v1-8k">Kimi (moonshot-v1-8k)</option>
                <option value="moonshot-v1-32k">Kimi (moonshot-v1-32k)</option>
                <option value="moonshot-v1-128k">Kimi (moonshot-v1-128k)</option>
                <option value="moonshot-v1-8k-vision-preview">Kimi (视觉模型)</option>
                <option value="qwen-vl-plus" selected>千问 (视觉模型)</option>
              </select>
            </div>
          </div>
          
          <button id="send-btn" class="send-btn">发送</button>
        </div>
        
        <div id="loading-indicator" class="loading-indicator">
          <div class="loading-spinner"></div>
          <span>正在生成回答...</span>
        </div>
        
        <div id="error-message" class="error-message"></div>
        <div id="info-message" class="info-message"></div>
      </div>
    </div>
  </div>
  
  <!-- 引入外部JavaScript文件 -->
  <script src="../js/kimi-chat.js"></script>
</body>
</html> 