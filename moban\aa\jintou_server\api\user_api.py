#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 用户API
"""

import os
import json
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

# 创建路由
router = APIRouter(
    prefix="/user",
    tags=["用户管理"],
    responses={404: {"description": "Not found"}},
)

# 数据模型
class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

# 用户数据存储路径
USERS_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "users.json")

# 确保用户数据目录存在
os.makedirs(os.path.dirname(USERS_FILE), exist_ok=True)

# 初始化用户数据文件
if not os.path.exists(USERS_FILE):
    with open(USERS_FILE, "w", encoding="utf-8") as f:
        json.dump({"users": []}, f, ensure_ascii=False, indent=4)

# 读取用户数据
def get_users():
    try:
        with open(USERS_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {"users": []}

# 保存用户数据
def save_users(users_data):
    with open(USERS_FILE, "w", encoding="utf-8") as f:
        json.dump(users_data, f, ensure_ascii=False, indent=4)

# API路由
@router.get("/", response_model=List[User])
async def read_users():
    """获取所有用户列表"""
    users_data = get_users()
    return users_data.get("users", [])

@router.get("/{username}", response_model=User)
async def read_user(username: str):
    """获取特定用户信息"""
    users_data = get_users()
    for user in users_data.get("users", []):
        if user.get("username") == username:
            return user
    raise HTTPException(status_code=404, detail="用户不存在")

@router.post("/", response_model=User)
async def create_user(user: User):
    """创建新用户"""
    users_data = get_users()
    for existing_user in users_data.get("users", []):
        if existing_user.get("username") == user.username:
            raise HTTPException(status_code=400, detail="用户名已存在")
    
    users_data["users"].append(user.dict())
    save_users(users_data)
    return user

@router.put("/{username}", response_model=User)
async def update_user(username: str, user: User):
    """更新用户信息"""
    users_data = get_users()
    for i, existing_user in enumerate(users_data.get("users", [])):
        if existing_user.get("username") == username:
            users_data["users"][i] = user.dict()
            save_users(users_data)
            return user
    raise HTTPException(status_code=404, detail="用户不存在")

@router.delete("/{username}")
async def delete_user(username: str):
    """删除用户"""
    users_data = get_users()
    for i, existing_user in enumerate(users_data.get("users", [])):
        if existing_user.get("username") == username:
            del users_data["users"][i]
            save_users(users_data)
            return {"message": f"用户 {username} 已删除"}
    raise HTTPException(status_code=404, detail="用户不存在") 