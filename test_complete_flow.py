#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整测试报表生成流程
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1/document"

def test_complete_flow():
    """
    测试完整的报表生成流程
    """
    print("🚀 开始测试完整的报表生成流程")
    print("=" * 60)
    
    # 步骤1：获取报表模板
    print("\n📋 步骤1：获取报表模板...")
    try:
        response = requests.get(f"{BASE_URL}/report-templates")
        if response.status_code != 200:
            print(f"❌ 获取模板失败: {response.text}")
            return False
        
        templates = response.json().get('templates', [])
        if not templates:
            print("❌ 没有找到可用模板")
            return False
        
        template = templates[0]
        print(f"✅ 找到模板: {template['display_name']}")
        
    except Exception as e:
        print(f"❌ 获取模板异常: {str(e)}")
        return False
    
    # 步骤2：准备测试数据
    print("\n📄 步骤2：准备测试数据...")
    mock_analysis_results = [
        {
            "filename": "测试财务报表.pdf",
            "content": """资产负债表
            
流动资产：
货币资金                    1,000,000
应收账款                      500,000
存货                          300,000
流动资产合计                1,800,000

非流动资产：
固定资产                    2,000,000
无形资产                      200,000
非流动资产合计              2,200,000

资产总计                    4,000,000

流动负债：
应付账款                      400,000
短期借款                      300,000
流动负债合计                  700,000

非流动负债：
长期借款                      800,000
非流动负债合计                800,000

负债合计                    1,500,000

所有者权益：
实收资本                    2,000,000
未分配利润                    500,000
所有者权益合计              2,500,000

负债和所有者权益总计        4,000,000""",
            "parsed_result": {
                "是否为报表": "是",
                "报表类型": "资产负债表",
                "报表主体": "测试公司",
                "报表时期": "2023年12月31日",
                "置信度": "95%"
            },
            "selected": True
        }
    ]
    
    request_data = {
        "selected_files": ["测试财务报表.pdf"],
        "template_name": template['name'],
        "analysis_results": mock_analysis_results
    }
    
    print(f"✅ 测试数据准备完成，文件数量: {len(request_data['selected_files'])}")
    
    # 步骤3：生成提示词
    print("\n📝 步骤3：生成提示词...")
    try:
        response = requests.post(f"{BASE_URL}/generate-prompt", json=request_data, timeout=30)
        if response.status_code != 200:
            print(f"❌ 生成提示词失败: {response.text}")
            return False
        
        prompt_data = response.json()
        prompt = prompt_data.get('prompt', '')
        
        if not prompt:
            print("❌ 提示词为空")
            return False
        
        print(f"✅ 提示词生成成功，长度: {len(prompt)} 字符")
        print(f"📄 提示词预览:\n{prompt[:300]}...")
        
    except Exception as e:
        print(f"❌ 生成提示词异常: {str(e)}")
        return False
    
    # 步骤4：调用千问API
    print("\n🤖 步骤4：调用千问API...")
    try:
        qwen_request = {
            "prompt": prompt,
            "template_name": template['name'],
            "selected_files": request_data['selected_files']
        }
        
        print("正在调用千问API，请稍候...")
        response = requests.post(f"{BASE_URL}/call-qwen-api", json=qwen_request, timeout=90)
        
        if response.status_code != 200:
            print(f"❌ 调用千问API失败: {response.text}")
            return False
        
        ai_data = response.json()
        ai_response = ai_data.get('ai_response', '')
        
        if not ai_response:
            print("❌ AI回答为空")
            return False
        
        print(f"✅ 千问API调用成功，回答长度: {len(ai_response)} 字符")
        print(f"🤖 AI回答预览:\n{ai_response[:500]}...")
        
    except Exception as e:
        print(f"❌ 调用千问API异常: {str(e)}")
        return False
    
    # 步骤5：测试一步完成的接口（兼容性测试）
    print("\n⚡ 步骤5：测试一步完成接口...")
    try:
        print("正在执行一步完成流程...")
        response = requests.post(f"{BASE_URL}/generate-report-with-template", json=request_data, timeout=120)
        
        if response.status_code != 200:
            print(f"❌ 一步完成失败: {response.text}")
            return False
        
        complete_data = response.json()
        complete_prompt = complete_data.get('prompt', '')
        complete_ai_response = complete_data.get('ai_response', '')
        
        print(f"✅ 一步完成成功")
        print(f"📝 提示词长度: {len(complete_prompt)} 字符")
        print(f"🤖 AI回答长度: {len(complete_ai_response)} 字符")
        
    except Exception as e:
        print(f"❌ 一步完成异常: {str(e)}")
        return False
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("🎉 测试结果汇总:")
    print("✅ 获取报表模板: 成功")
    print("✅ 生成提示词: 成功")
    print("✅ 调用千问API: 成功")
    print("✅ 一步完成接口: 成功")
    print("\n🎊 所有测试通过！报表生成功能完全可用！")
    
    # 保存测试结果
    test_result = {
        "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "template_used": template['name'],
        "prompt_length": len(prompt),
        "ai_response_length": len(ai_response),
        "test_status": "SUCCESS"
    }
    
    with open("test_result.json", "w", encoding="utf-8") as f:
        json.dump(test_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 测试结果已保存到 test_result.json")
    return True

if __name__ == "__main__":
    success = test_complete_flow()
    if success:
        print("\n🚀 测试完成，功能可以正常使用！")
    else:
        print("\n❌ 测试失败，请检查相关配置！")
