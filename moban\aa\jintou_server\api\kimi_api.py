#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - Kimi问答API服务
提供与Kimi大模型交互的API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks, File, UploadFile, Form
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
import time
import os
import json
import logging
import uuid
import asyncio
import aiohttp
import io
import base64
from datetime import datetime
import fitz  # PyMuPDF库，用于PDF处理
from PIL import Image

# 导入安全工具
from jintou_server.security.jwt_handler import get_current_user

# 创建路由
router = APIRouter(
    prefix="/api",
    tags=["Kimi问答服务"],
    responses={404: {"description": "Not found"}},
)

# 配置日志
logger = logging.getLogger("jintou_brain.kimi")

# Kimi API配置
KIMI_API_KEY = os.getenv("KIMI_API_KEY", "sk-wARbY1G4q4KgFAj6CBFTFi5LVIlxFrZ7cqp8tQG20s82R8YA")
KIMI_BASE_URL = os.getenv("KIMI_BASE_URL", "https://api.moonshot.cn/v1")
KIMI_MODEL = os.getenv("KIMI_MODEL", "moonshot-v1-32k")

# 千问API配置
QWEN_API_KEY = os.getenv("QWEN_API_KEY", "sk-798b2441b1d348dd9ba95d8f192ca9b8")
QWEN_BASE_URL = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
QWEN_MODEL = os.getenv("QWEN_MODEL", "qwen-vl-plus")
DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "qwen-vl-plus")  # 设置默认模型为千问视觉模型

# 定义可用的模型列表
AVAILABLE_MODELS = [
    "moonshot-v1-8k",   # 最大输出长度为8K - prompt_tokens
    "moonshot-v1-32k",  # 最大输出长度为32K - prompt_tokens
    "moonshot-v1-128k", # 最大输出长度为128K - prompt_tokens
    "moonshot-v1-8k-vision-preview",  # Kimi视觉模型，支持图片输入
    "qwen-vl-plus"  # 千问视觉大模型，支持图片输入
]

# 创建临时文件目录
TEMP_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "temp")
os.makedirs(TEMP_DIR, exist_ok=True)

# 数据模型
class MessageContent(BaseModel):
    type: str = Field(..., description="消息内容类型，例如'text'或'image_url'")
    text: Optional[str] = Field(None, description="文本内容，当type为'text'时使用")
    image_url: Optional[Dict[str, str]] = Field(None, description="图片URL，当type为'image_url'时使用")

class Message(BaseModel):
    role: str = Field(..., description="消息角色，例如'user'或'assistant'")
    content: Union[str, List[MessageContent]] = Field(..., description="消息内容，可以是字符串或内容列表")
    
class KimiRequest(BaseModel):
    messages: List[Message] = Field(..., description="对话历史消息列表")
    model: str = Field(KIMI_MODEL, description="使用的模型名称，支持的模型有：moonshot-v1-8k、moonshot-v1-32k、moonshot-v1-128k、moonshot-v1-8k-vision-preview")
    stream: bool = Field(False, description="是否使用流式响应")
    temperature: float = Field(0.7, description="温度参数，控制回答的随机性")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    
    # 验证模型是否在支持列表中
    def validate_model(self):
        if self.model not in AVAILABLE_MODELS:
            logger.warning(f"请求的模型 {self.model} 不在支持列表中，使用默认模型 {KIMI_MODEL}")
            self.model = KIMI_MODEL
        return self.model

class KimiResponse(BaseModel):
    id: str = Field(..., description="响应ID")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    message: Message = Field(..., description="助手回复的消息")

# 存储对话历史的简单内存缓存
kimi_history = {}

# 图像处理函数
def image_to_base64(image_bytes, image_type="jpeg"):
    """
    将图像字节数据转换为base64编码
    Args:
        image_bytes: 图像字节数据
        image_type: 图像类型
    Returns:
        base64编码的图像数据
    """
    try:
        # 编码为base64
        encoded_string = base64.b64decode(image_bytes) if isinstance(image_bytes, str) else image_bytes
        encoded_string = base64.b64encode(encoded_string).decode('utf-8')
        
        # 添加前缀
        return f"data:image/{image_type};base64,{encoded_string}"
    except Exception as e:
        logger.error(f"转换为base64时出错: {e}")
        return None

def pil_image_to_base64(image, format="PNG"):
    """
    将PIL图像转换为base64编码
    Args:
        image: PIL.Image对象
        format: 图像格式，默认PNG以保留更多细节
    Returns:
        base64编码的图像数据
    """
    try:
        # 创建内存缓冲区
        buffer = io.BytesIO()
        
        # 保存图像到缓冲区，使用高质量设置
        if format.upper() == "JPEG":
            image.save(buffer, format=format, quality=95)  # 高质量JPEG
        else:
            image.save(buffer, format=format)  # PNG已经是无损的
        
        # 获取字节数据
        img_bytes = buffer.getvalue()
        
        # 编码为base64
        encoded_string = base64.b64encode(img_bytes).decode('utf-8')
        
        # 添加前缀
        image_type = format.lower()
        if image_type == 'jpg':
            image_type = 'jpeg'
            
        return f"data:image/{image_type};base64,{encoded_string}"
    
    except Exception as e:
        logger.error(f"转换PIL图像为base64时出错: {e}")
        return None

def extract_pdf_data(image_base64):
    """
    从Base64编码的PDF数据中提取PDF二进制数据
    Args:
        image_base64: Base64编码的PDF数据
    Returns:
        PDF文件的二进制数据
    """
    try:
        # 移除数据URL前缀
        if "base64," in image_base64:
            pdf_data = image_base64.split('base64,')[1]
        else:
            pdf_data = image_base64
        
        # 解码Base64
        pdf_binary = base64.b64decode(pdf_data)
        return pdf_binary
    
    except Exception as e:
        logger.error(f"提取PDF数据失败: {e}")
        return None

def pdf_to_images(pdf_data):
    """
    将PDF数据转换为图片列表，最多处理前6页
    Args:
        pdf_data: PDF文件的字节数据
    Returns:
        图片列表，每个元素为PIL.Image对象
    """
    try:
        # 创建内存文件对象
        pdf_stream = io.BytesIO(pdf_data)
        
        # 打开PDF文件
        pdf_document = fitz.open(stream=pdf_stream, filetype="pdf")
        logger.info(f"成功打开PDF文件，共 {len(pdf_document)} 页")
        
        # 确定要处理的页数，最多6页
        max_pages = min(6, len(pdf_document))
        logger.info(f"将处理前 {max_pages} 页")
        
        images = []
        # 遍历每一页，最多处理6页
        for page_num in range(max_pages):
            page = pdf_document[page_num]
            
            # 将页面渲染为图像 - 使用4倍缩放以提高清晰度，特别是对表格
            pix = page.get_pixmap(matrix=fitz.Matrix(4, 4), alpha=False)
            
            # 转换为PIL图像
            img_data = pix.tobytes("png")  # 使用PNG格式保留更多细节
            img = Image.open(io.BytesIO(img_data))
            
            images.append(img)
            logger.info(f"成功转换第 {page_num + 1} 页，尺寸: {img.size}")
        
        pdf_document.close()
        return images
    
    except Exception as e:
        logger.error(f"PDF转图片失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None

# 调用Kimi API生成回复
async def call_kimi_api(messages: List[Message], model_name: str, temperature: float, max_tokens: Optional[int] = None) -> str:
    """
    调用Kimi API生成AI回复
    
    Args:
        messages: 对话历史
        model_name: 模型名称
        temperature: 温度参数
        max_tokens: 最大生成token数
        
    Returns:
        AI回复的文本
    """
    logger.info(f"调用Kimi API, 模型: {model_name}, 消息数量: {len(messages)}")
    
    # 转换消息格式
    kimi_messages = []
    for msg in messages:
        if isinstance(msg.content, str):
            kimi_messages.append({
                "role": msg.role if msg.role in ["user", "assistant", "system"] else "user", 
                "content": msg.content
            })
        elif isinstance(msg.content, list):
            # 处理多模态内容
            content_list = []
            for item in msg.content:
                if item.type == "text":
                    content_list.append({
                        "type": "text",
                        "text": item.text
                    })
                elif item.type == "image_url":
                    content_list.append({
                        "type": "image_url",
                        "image_url": item.image_url
                    })
            
            kimi_messages.append({
                "role": msg.role if msg.role in ["user", "assistant", "system"] else "user", 
                "content": content_list
            })
    
    # 准备请求数据
    request_data = {
        "model": model_name,
        "messages": kimi_messages,
        "temperature": temperature
    }
    
    # 如果提供了max_tokens参数，则添加到请求中
    if max_tokens is not None:
        request_data["max_tokens"] = max_tokens
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{KIMI_BASE_URL}/chat/completions",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {KIMI_API_KEY}"
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Kimi API错误: {response.status} - {error_text}")
                    raise HTTPException(status_code=500, detail=f"Kimi服务调用失败: {error_text}")
                
                data = await response.json()
                logger.info("成功获取Kimi API响应")
                
                # 从响应中提取内容
                return data["choices"][0]["message"]["content"]
    except aiohttp.ClientError as e:
        logger.error(f"调用Kimi API时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Kimi服务连接失败: {str(e)}")
    except Exception as e:
        logger.error(f"处理Kimi API响应时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理Kimi响应时出错: {str(e)}")

# 流式调用Kimi API
async def kimi_stream_chat(messages, model=KIMI_MODEL, temperature=0.7, max_tokens=4000):
    """
    流式调用Kimi API
    
    Args:
        messages: 消息列表
        model: 模型名称
        temperature: 温度参数
        max_tokens: 最大生成token数
        
    Returns:
        流式响应生成器
    """
    logger.info(f"开始流式调用Kimi API, 模型: {model}, 消息数量: {len(messages)}")
    
    # 准备请求数据
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "stream": True
    }
    
    if max_tokens:
        request_data["max_tokens"] = max_tokens
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{KIMI_BASE_URL}/chat/completions",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {KIMI_API_KEY}"
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Kimi API错误: {response.status} - {error_text}")
                    async for chunk in error_stream(f"Kimi服务调用失败: {error_text}"):
                        yield chunk
                    return
                
                # 处理流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line:
                        if line.startswith('data: '):
                            line = line[6:]  # 去掉 'data: ' 前缀
                        if line == '[DONE]':
                            break
                        try:
                            data = json.loads(line)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    yield f"data: {json.dumps({'content': content})}\n\n"
                        except json.JSONDecodeError as e:
                            logger.error(f"解析Kimi流式响应时出错: {str(e)}, 行: {line}")
                            continue
                        
    except aiohttp.ClientError as e:
        logger.error(f"流式调用Kimi API时出错: {str(e)}")
        async for chunk in error_stream(f"Kimi服务连接失败: {str(e)}"):
            yield chunk
    except Exception as e:
        logger.error(f"处理Kimi流式响应时出错: {str(e)}")
        async for chunk in error_stream(f"处理Kimi响应时出错: {str(e)}"):
            yield chunk

# 错误流
async def error_stream(error_message):
    """生成错误流响应"""
    yield f"data: {json.dumps({'error': error_message})}\n\n"

# Kimi问答API端点
@router.post("/kimi/completion", response_model=KimiResponse)
async def kimi_chat(request: KimiRequest, current_user: dict = Depends(get_current_user)):
    """
    Kimi问答API
    接收用户消息并返回Kimi回复
    """
    logger.info(f"收到Kimi问答请求，模型: {request.model}, 消息数量: {len(request.messages)}")
    
    # 验证模型
    model = request.validate_model()
    
    # 生成会话ID
    chat_id = str(uuid.uuid4())
    
    # 调用Kimi API获取回复
    ai_response = await call_kimi_api(
        request.messages, 
        model, 
        request.temperature,
        request.max_tokens
    )
    
    # 创建回复消息
    response_message = Message(
        role="assistant",
        content=ai_response
    )
    
    # 保存对话历史 (关联到用户)
    username = current_user.get("sub", "anonymous")
    if username not in kimi_history:
        kimi_history[username] = {}
    
    kimi_history[username][chat_id] = {
        "messages": request.messages + [response_message],
        "model": model,
        "timestamp": time.time()
    }
    
    # 返回响应
    return KimiResponse(
        id=f"kimichat-{chat_id}",
        created=int(time.time()),
        model=model,
        message=response_message
    )

# 流式Kimi问答API端点
@router.post("/kimi/stream")
async def kimi_chat_stream(
    request: Request,
    current_user: dict = Depends(get_current_user),
):
    """
    流式Kimi问答API
    接收用户消息并以流式方式返回Kimi回复
    """
    try:
        # 从请求体中获取JSON数据
        body = await request.json()
        message = body.get("message", "")
        history = body.get("history", [])
        model = body.get("model", DEFAULT_MODEL)  # 使用默认模型（千问视觉模型）
        temperature = body.get("temperature", 0.7)
        max_tokens = body.get("max_tokens", 4000)
        image_base64 = body.get("image", None)  # 获取图片数据
        
        # 验证必要参数
        if not message and not image_base64:
            raise HTTPException(status_code=400, detail="缺少必要参数：message或image")
        
        # 验证模型是否支持
        if model not in AVAILABLE_MODELS:
            logger.warning(f"请求的模型 {model} 不在支持列表中，使用默认模型 {DEFAULT_MODEL}")
            model = DEFAULT_MODEL
            
        # 如果有图片但使用的不是视觉模型，则切换到视觉模型
        if image_base64:
            if "vision" not in model and model != "qwen-vl-plus":
                if "qwen" in model:
                    logger.info(f"检测到图片输入，切换到千问视觉模型 qwen-vl-plus")
                    model = "qwen-vl-plus"
                else:
                    logger.info(f"检测到图片输入，切换到视觉模型 moonshot-v1-8k-vision-preview")
                    model = "moonshot-v1-8k-vision-preview"
        
        # 过滤历史记录，确保没有空消息
        filtered_history = []
        for msg in history:
            if isinstance(msg, dict) and msg.get("role") and msg.get("content") and msg.get("content").strip():
                # 确保角色是有效的
                role = msg["role"]
                if role not in ["user", "assistant", "system"]:
                    role = "user"  # 默认为用户角色
                
                filtered_history.append({
                    "role": role,
                    "content": msg["content"]
                })
        
        # 处理图片输入
        if image_base64:
            # 检查是否是PDF文件
            is_pdf = False
            if "data:application/pdf" in image_base64:
                logger.info("检测到PDF文件，转换为图像")
                is_pdf = True
                
                # 提取PDF数据
                pdf_data = extract_pdf_data(image_base64)
                if not pdf_data:
                    raise HTTPException(status_code=400, detail="无法提取PDF数据")
                
                # 将PDF转换为图像
                images = pdf_to_images(pdf_data)
                if not images or len(images) == 0:
                    raise HTTPException(status_code=400, detail="PDF转图像失败")
                
                # 获取当前处理的页码
                current_page = body.get("page", 0)  # 默认处理第一页
                if current_page >= len(images):
                    raise HTTPException(status_code=400, detail=f"页码超出范围，PDF共{len(images)}页")
                
                # 使用当前页作为图像
                current_image = images[current_page]
                current_image_base64 = pil_image_to_base64(current_image)
                
                # 准备用户消息
                if model == "qwen-vl-plus":
                    # 千问模型的消息格式
                    user_message = {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": current_image_base64
                                }
                            }
                        ]
                    }
                    
                    # 确保文本内容不为空
                    if not message:
                        message = "请详细分析这个PDF文档内容，特别注意识别和解析其中的表格。"
                    
                    # 添加文本消息，包含PDF页数信息
                    total_pages = len(images)
                    
                    pdf_info = f"这是一个PDF文件的第{current_page + 1}页，共{total_pages}页。请特别注意识别其中的表格内容，完整提取表格中的所有文字和数据，并保持表格的结构。"
                    if message:
                        pdf_info += " " + message
                    
                    user_message["content"].append({
                        "type": "text",
                        "text": pdf_info
                    })
                else:
                    # Kimi模型的消息格式
                    user_message = {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": current_image_base64
                                }
                            }
                        ]
                    }
                    
                    # 添加文本消息，包含PDF页数信息
                    total_pages = len(images)
                    
                    pdf_info = f"这是一个PDF文件的第{current_page + 1}页，共{total_pages}页。请特别注意识别其中的表格内容，完整提取表格中的所有文字和数据，并保持表格的结构。"
                    if message:
                        pdf_info += " " + message
                    
                    user_message["content"].append({
                        "type": "text",
                        "text": pdf_info
                    })
            else:
                # 普通图片处理
                if model == "qwen-vl-plus":
                    # 千问模型的消息格式
                    user_message = {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_base64
                                }
                            },
                            {
                                "type": "text",
                                "text": message or "请描述这张图片。"  # 确保文本内容不为空
                            }
                        ]
                    }
                else:
                    # Kimi模型的消息格式
                    user_message = {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_base64
                                }
                            },
                            {
                                "type": "text",
                                "text": message or "请描述这张图片。"  # 确保文本内容不为空
                            }
                        ]
                    }
        else:
            # 纯文本消息
            user_message = {
                "role": "user",
                "content": message
            }
        
        # 准备发送给模型的消息
        messages = filtered_history + [user_message]
        
        # 根据模型类型选择不同的API
        if model == "qwen-vl-plus" or model.startswith("qwen"):
            # 使用千问API
            return StreamingResponse(
                qwen_stream_chat(messages, model, temperature, max_tokens),
                media_type="text/event-stream"
            )
        else:
            # 使用Kimi API
            return StreamingResponse(
                kimi_stream_chat(messages, model, temperature, max_tokens),
                media_type="text/event-stream"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理流式问答请求时出错: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

# 文件上传端点
@router.post("/kimi/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user)
):
    """
    上传文件并转换为Base64编码
    支持图片和PDF文件
    """
    try:
        # 验证文件格式
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in ['.jpg', '.jpeg', '.png', '.pdf']:
            raise HTTPException(
                status_code=400,
                detail="只支持JPG、PNG和PDF格式"
            )
        
        # 读取文件内容
        content = await file.read()
        
        # 获取文件类型
        file_type = file_ext.replace('.', '')
        if file_type == 'jpg':
            file_type = 'jpeg'
        
        # 转换为base64
        if file_ext == '.pdf':
            image_base64 = f"data:application/pdf;base64,{base64.b64encode(content).decode('utf-8')}"
        else:
            image_base64 = f"data:image/{file_type};base64,{base64.b64encode(content).decode('utf-8')}"
        
        return {
            "success": True,
            "filename": file.filename,
            "file_type": file_type,
            "image_base64": image_base64
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理文件上传时出错: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

# 获取模型信息
@router.get("/kimi/info")
async def get_kimi_info():
    """获取API信息"""
    return {
        "name": "智能问答API",
        "version": "1.1",
        "models": AVAILABLE_MODELS,
        "default_model": DEFAULT_MODEL,
        "kimi_base_url": KIMI_BASE_URL,
        "qwen_base_url": QWEN_BASE_URL,
    }

# 调用千问API生成回复
async def call_qwen_api(messages, model_name="qwen-vl-plus", temperature=0.7, max_tokens=None) -> str:
    """
    调用千问API生成AI回复
    
    Args:
        messages: 消息列表
        model_name: 模型名称
        temperature: 温度参数
        max_tokens: 最大生成token数
        
    Returns:
        AI回复的文本
    """
    logger.info(f"调用千问API, 模型: {model_name}, 消息数量: {len(messages)}")
    
    # 准备请求数据
    request_data = {
        "model": model_name,
        "messages": messages,
        "temperature": temperature
    }
    
    # 如果提供了max_tokens参数，则添加到请求中
    if max_tokens is not None:
        request_data["max_tokens"] = max_tokens
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{QWEN_BASE_URL}/chat/completions",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {QWEN_API_KEY}"
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"千问API错误: {response.status} - {error_text}")
                    raise HTTPException(status_code=500, detail=f"千问服务调用失败: {error_text}")
                
                data = await response.json()
                logger.info("成功获取千问API响应")
                
                # 从响应中提取内容
                return data["choices"][0]["message"]["content"]
    except aiohttp.ClientError as e:
        logger.error(f"调用千问API时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"千问服务连接失败: {str(e)}")
    except Exception as e:
        logger.error(f"处理千问API响应时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理千问响应时出错: {str(e)}")

# 流式调用千问API
async def qwen_stream_chat(messages, model="qwen-vl-plus", temperature=0.7, max_tokens=4000):
    """
    流式调用千问API
    
    Args:
        messages: 消息列表
        model: 模型名称
        temperature: 温度参数
        max_tokens: 最大生成token数
        
    Returns:
        流式响应生成器
    """
    logger.info(f"开始流式调用千问API, 模型: {model}, 消息数量: {len(messages)}")
    
    # 准备请求数据
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "stream": True
    }
    
    if max_tokens:
        request_data["max_tokens"] = max_tokens
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{QWEN_BASE_URL}/chat/completions",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {QWEN_API_KEY}"
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"千问API错误: {response.status} - {error_text}")
                    async for chunk in error_stream(f"千问服务调用失败: {error_text}"):
                        yield chunk
                    return
                
                # 处理流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line:
                        if line.startswith('data: '):
                            line = line[6:]  # 去掉 'data: ' 前缀
                        if line == '[DONE]':
                            break
                        try:
                            data = json.loads(line)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    yield f"data: {json.dumps({'content': content})}\n\n"
                        except json.JSONDecodeError as e:
                            logger.error(f"解析千问流式响应时出错: {str(e)}, 行: {line}")
                            continue
                        
    except aiohttp.ClientError as e:
        logger.error(f"流式调用千问API时出错: {str(e)}")
        async for chunk in error_stream(f"千问服务连接失败: {str(e)}"):
            yield chunk
    except Exception as e:
        logger.error(f"处理千问流式响应时出错: {str(e)}")
        async for chunk in error_stream(f"处理千问响应时出错: {str(e)}"):
            yield chunk 