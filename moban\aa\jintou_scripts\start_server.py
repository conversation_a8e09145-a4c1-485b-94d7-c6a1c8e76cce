#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 服务器启动脚本
一键启动API服务
"""

import os
import sys
import subprocess
import time
import signal
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("jintou_starter")

# 获取当前脚本所在目录的上一级目录作为项目根目录
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import fastapi
        import uvicorn
        import jose
        import passlib
        logger.info("所有依赖已安装")
        return True
    except ImportError as e:
        logger.error(f"缺少依赖: {e}")
        logger.info("正在安装依赖...")
        try:
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "fastapi", "uvicorn", "python-jose", "passlib", "python-multipart", "bcrypt"],
                check=True
            )
            logger.info("依赖安装完成")
            return True
        except subprocess.CalledProcessError:
            logger.error("依赖安装失败")
            return False

def start_server():
    """启动API服务器"""
    logger.info("正在启动金投大脑API服务...")
    
    # 切换到项目根目录
    os.chdir(ROOT_DIR)
    
    # 确保数据目录存在
    os.makedirs("jintou_data", exist_ok=True)
    
    # 启动服务
    server_process = subprocess.Popen(
        [sys.executable, "-m", "uvicorn", "jintou_server.main_app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    
    # 注册信号处理函数，确保程序退出时关闭服务器进程
    def signal_handler(sig, frame):
        logger.info("接收到终止信号，正在关闭服务...")
        server_process.terminate()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 实时显示服务器日志
    logger.info("服务启动中，请稍候...")
    try:
        for line in server_process.stdout:
            print(line, end='')
    except KeyboardInterrupt:
        logger.info("接收到终止信号，正在关闭服务...")
        server_process.terminate()
    
    return server_process

if __name__ == "__main__":
    logger.info("金投大脑服务启动器")
    
    # 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败，无法启动服务")
        sys.exit(1)
    
    # 启动服务器
    server_process = start_server()
    
    # 等待服务器进程结束
    try:
        server_process.wait()
    except KeyboardInterrupt:
        logger.info("接收到终止信号，正在关闭服务...")
        server_process.terminate()
    
    logger.info("服务已关闭") 