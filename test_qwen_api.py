#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试千问API是否可用
"""

import dashscope
from http import HTTPStatus
import json

# API配置
DASHSCOPE_API_KEY = "sk-798b2441b1d348dd9ba95d8f192ca9b8"
DASHSCOPE_MODEL = "qwen-plus"

def test_qwen_api():
    """
    测试千问API基本功能
    """
    try:
        print("开始测试千问API...")
        print(f"API Key: {DASHSCOPE_API_KEY}")
        print(f"Model: {DASHSCOPE_MODEL}")
        
        # 测试消息
        messages = [
            {"role": "system", "content": "你是一个专业的财务报表分析师。"},
            {"role": "user", "content": "请简单介绍一下资产负债表的主要组成部分。"}
        ]
        
        print("\n发送的消息:")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        
        # 调用API
        print("\n调用API中...")
        response = dashscope.Generation.call(
            model=DASHSCOPE_MODEL,
            api_key=DASHSCOPE_API_KEY,
            messages=messages,
            stream=False,
            result_format='message',
            top_p=0.8,
            temperature=0.3,
            max_tokens=1000,
            enable_search=False
        )
        
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == HTTPStatus.OK:
            if hasattr(response, 'output') and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
                choice = response.output.choices[0]
                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    content = choice.message.content
                    print(f"\n✅ API调用成功!")
                    print(f"回答内容:\n{content}")
                    return True
                else:
                    print("❌ 响应格式错误: 无法获取content")
                    print(f"Response: {response}")
            else:
                print("❌ 响应格式错误: 无法获取choices")
                print(f"Response: {response}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            if hasattr(response, 'message'):
                print(f"错误信息: {response.message}")
            if hasattr(response, 'code'):
                print(f"错误代码: {response.code}")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_qwen_stream_api():
    """
    测试千问流式API
    """
    try:
        print("\n" + "="*50)
        print("开始测试千问流式API...")
        
        # 测试消息
        messages = [
            {"role": "system", "content": "你是一个专业的财务报表分析师。"},
            {"role": "user", "content": "请生成一个简单的资产负债表示例，包含主要科目。"}
        ]
        
        print("\n发送的消息:")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        
        # 调用流式API
        print("\n调用流式API中...")
        responses = dashscope.Generation.call(
            model=DASHSCOPE_MODEL,
            api_key=DASHSCOPE_API_KEY,
            messages=messages,
            stream=True,
            result_format='message',
            top_p=0.8,
            temperature=0.3,
            max_tokens=1000,
            enable_search=False
        )
        
        print("\n✅ 流式响应:")
        full_content = ""
        
        for response in responses:
            if response.status_code == HTTPStatus.OK:
                if hasattr(response, 'output') and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
                    choice = response.output.choices[0]
                    if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                        content = choice.message.content
                        if content:
                            print(content, end='', flush=True)
                            full_content += content
            else:
                print(f"\n❌ 流式响应错误: {response.status_code}")
                if hasattr(response, 'message'):
                    print(f"错误信息: {response.message}")
                return False
        
        print(f"\n\n✅ 流式API调用成功!")
        print(f"完整回答长度: {len(full_content)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ 流式测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("千问API测试脚本")
    print("="*50)
    
    # 测试基本API
    success1 = test_qwen_api()
    
    # 测试流式API
    success2 = test_qwen_stream_api()
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"基本API: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"流式API: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过，千问API可以正常使用!")
    else:
        print("\n⚠️ 部分测试失败，请检查API配置")
