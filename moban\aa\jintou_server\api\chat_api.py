#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
金投大脑 - 聊天API服务
提供AI智能问答功能的API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks, Query
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import time
import os
import json
import logging
import uuid
import asyncio
import aiohttp
from datetime import datetime
import hashlib
import httpx

# 导入安全工具
from jintou_server.security.jwt_handler import get_current_user
# 导入工具函数
from jintou_server.utils.helpers import trim_messages

# 创建路由
router = APIRouter(
    prefix="/api",
    tags=["聊天服务"],
    responses={404: {"description": "Not found"}},
)

# 配置日志
logger = logging.getLogger("jintou_brain.chat")

# Deepseek API配置
DEEPSEEK_API_URL = os.getenv("DEEPSEEK_API_URL", "http://**********:8000/v1/chat/completions")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

# Qwen API配置
QWEN_API_URL = os.getenv("QWEN_API_URL", "http://**********:8001/v1/chat/completions")
QWEN_API_KEY = os.getenv("QWEN_API_KEY", "szjf@2025")
QWEN_MODEL = os.getenv("QWEN_MODEL", "qwen-32b")

# 数据模型
class Message(BaseModel):
    role: str = Field(..., description="消息角色，例如'user'或'assistant'")
    content: str = Field(..., description="消息内容")
    
class ChatRequest(BaseModel):
    messages: List[Message] = Field(..., description="对话历史消息列表")
    model: str = Field("gpt-3.5-turbo", description="使用的模型名称")
    stream: bool = Field(False, description="是否使用流式响应")
    temperature: float = Field(0.7, description="温度参数，控制回答的随机性")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")

class LoginRequest(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    
class ChatResponse(BaseModel):
    id: str = Field(..., description="响应ID")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="使用的模型")
    message: Message = Field(..., description="助手回复的消息")

class SessionCreate(BaseModel):
    title: str = Field(..., description="会话标题")

class SessionUpdate(BaseModel):
    title: str = Field(None, description="会话标题")
    
# 存储对话历史的简单内存缓存
chat_history = {}

# 存储会话数据的简单内存缓存
sessions = {}

# 调用Deepseek API生成回复
async def call_deepseek_api(messages: List[Message], model_name: str, temperature: float, max_tokens: Optional[int] = None) -> str:
    """
    调用Deepseek API生成AI回复
    
    Args:
        messages: 对话历史
        model_name: 模型名称
        temperature: 温度参数
        max_tokens: 最大生成token数
        
    Returns:
        AI回复的文本
    """
    logger.info(f"调用Deepseek API, 模型: {model_name}, 消息数量: {len(messages)}")
    
    # 转换消息格式
    deepseek_messages = [
        {"role": msg.role if msg.role in ["user", "assistant"] else "user", "content": msg.content} 
        for msg in messages
    ]
    
    # 准备请求数据
    request_data = {
        "model": DEEPSEEK_MODEL,  # 使用完整的模型路径
        "messages": deepseek_messages,
        "temperature": temperature
    }
    
    # 如果提供了max_tokens参数，则添加到请求中
    if max_tokens is not None:
        request_data["max_tokens"] = max_tokens
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                DEEPSEEK_API_URL,
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                }
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Deepseek API错误: {response.status} - {error_text}")
                    raise HTTPException(status_code=500, detail=f"大模型服务调用失败: {error_text}")
                
                data = await response.json()
                logger.info("成功获取Deepseek API响应")
                
                # 从响应中提取内容
                return data["choices"][0]["message"]["content"]
    except aiohttp.ClientError as e:
        logger.error(f"调用Deepseek API时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"大模型服务连接失败: {str(e)}")
    except Exception as e:
        logger.error(f"处理Deepseek API响应时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理大模型响应时出错: {str(e)}")

# 聊天API端点
@router.post("/chat/completion", response_model=ChatResponse)
async def chat(request: ChatRequest, current_user: dict = Depends(get_current_user)):
    """
    智能问答API
    接收用户消息并返回AI回复
    """
    logger.info(f"收到聊天请求，模型: {request.model}, 消息数量: {len(request.messages)}")
    
    # 生成会话ID
    chat_id = str(uuid.uuid4())
    
    # 转换消息格式
    raw_messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
    
    # 使用trim_messages处理消息列表，确保不超出token限制
    trimmed_messages = trim_messages(
        messages=raw_messages,
        max_tokens=30000,  # 降低到30000，给模型回复预留空间
        keep_system_message=True,
        preserve_latest_messages=2  # 降低保留消息数量，节省token
    )
    
    # 如果有系统消息，确保它在列表开头
    system_messages = [msg for msg in trimmed_messages if msg.get("role") == "system"]
    if not system_messages and system_prompt:
        trimmed_messages.insert(0, {"role": "system", "content": system_prompt})
    
    # 记录消息裁剪信息
    logger.info(f"原始消息数量: {len(raw_messages)}, 裁剪后消息数量: {len(trimmed_messages)}")
    
    # 转回pydantic模型
    trimmed_request_messages = [Message(role=msg["role"], content=msg["content"]) for msg in trimmed_messages]
    
    # 调用Deepseek API获取回复
    ai_response = await call_deepseek_api(
        trimmed_request_messages, 
        request.model, 
        request.temperature,
        request.max_tokens
    )
    
    # 创建回复消息
    response_message = Message(
        role="assistant",
        content=ai_response
    )
    
    # 保存对话历史 (关联到用户)
    username = current_user.get("sub", "anonymous")
    if username not in chat_history:
        chat_history[username] = {}
    
    chat_history[username][chat_id] = {
        "messages": request.messages + [response_message],
        "model": request.model,
        "timestamp": time.time()
    }
    
    # 返回响应
    return ChatResponse(
        id=f"chatcmpl-{chat_id}",
        created=int(time.time()),
        model=request.model,
        message=response_message
    )

# 流式聊天API端点
@router.post("/chat/stream")
async def chat_stream(
    request: Request,
    current_user: dict = Depends(get_current_user),
):
    """
    流式智能问答API
    接收用户消息并以流式方式返回AI回复
    """
    try:
        # 从请求体中获取JSON数据
        body = await request.json()
        message = body.get("message", "")
        session_id = body.get("session_id", "")
        model = body.get("model", "deepseek-chat")
        temperature = body.get("temperature", 0.7)
        max_tokens = body.get("max_tokens", 20000)  # 降低默认值到20000
        system_prompt = body.get("system_prompt", "你是一个智能助手，由金投大脑提供支持。请用简洁、专业的方式回答问题。")
        
        # 验证必要参数
        if not message or not session_id:
            raise HTTPException(status_code=400, detail="缺少必要参数：message或session_id")
        
        # 获取用户名
        username = current_user.get("sub", "anonymous")
        
        # 获取会话
        if username not in sessions or session_id not in sessions[username]:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        # 获取会话消息
        session = sessions[username][session_id]
        messages = session["messages"]
        
        # 添加用户消息
        user_message = Message(role="user", content=message)
        messages.append(user_message.dict())
        
        # 准备发送给模型的消息
        model_messages = [{"role": "system", "content": system_prompt}]
        raw_messages = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
        
        # 使用trim_messages处理消息列表，确保不超出token限制
        # 保留最近2条消息和系统消息，最大token数为30000
        trimmed_messages = trim_messages(
            messages=raw_messages,
            max_tokens=30000,
            keep_system_message=True,
            preserve_latest_messages=2
        )
        
        # 如果有系统消息，确保它在列表开头
        system_messages = [msg for msg in trimmed_messages if msg.get("role") == "system"]
        if not system_messages and system_prompt:
            trimmed_messages.insert(0, {"role": "system", "content": system_prompt})
        
        # 记录消息裁剪信息
        logger.info(f"原始消息数量: {len(raw_messages)}, 裁剪后消息数量: {len(trimmed_messages)}")
        
        # 根据模型选择不同的API调用方式
        if model.startswith("deepseek"):
            # 调用Deepseek API
            return StreamingResponse(
                deepseek_stream_chat(trimmed_messages, temperature=temperature, max_tokens=max_tokens),
                media_type="text/event-stream"
            )
        elif model.startswith("qwen"):
            # 调用Qwen API
            return StreamingResponse(
                qwen_stream_chat(trimmed_messages, temperature=temperature, max_tokens=max_tokens),
                media_type="text/event-stream"
            )
        elif model.startswith("gpt"):
            # 调用OpenAI API
            return StreamingResponse(
                openai_stream_chat(trimmed_messages, model=model, temperature=temperature, max_tokens=max_tokens),
                media_type="text/event-stream"
            )
        else:
            # 默认使用Deepseek
            return StreamingResponse(
                deepseek_stream_chat(trimmed_messages, temperature=temperature, max_tokens=max_tokens),
                media_type="text/event-stream"
            )
            
    except Exception as e:
        logger.error(f"Stream chat error: {str(e)}")
        return StreamingResponse(
            error_stream(str(e)),
            media_type="text/event-stream"
        )

async def deepseek_stream_chat(messages, temperature=0.7, max_tokens=32000):
    """
    调用Deepseek API进行流式聊天
    """
    try:
        logger.info(f"开始调用Deepseek流式API，max_tokens={max_tokens}")
        
        # 强制限制max_tokens，确保不超过API限制
        if max_tokens > 20000:
            max_tokens = 20000
            logger.info(f"max_tokens过大，已调整为{max_tokens}")
        
        # 构建请求体
        request_body = {
            "model": DEEPSEEK_MODEL,  # 使用完整的模型路径
            "messages": messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        # 发送请求
        async with httpx.AsyncClient(timeout=300.0) as client:
            async with client.stream(
                "POST",
                DEEPSEEK_API_URL,  # 使用环境变量中配置的API地址
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
                }
            ) as response:
                if response.status_code != 200:
                    error_msg = await response.aread()
                    logger.error(f"Deepseek API错误: {response.status_code} - {error_msg}")
                    yield f"data: {json.dumps({'error': f'API错误: {error_msg}'})}\n\n"
                    return
                
                # 流式处理响应
                buffer = ""
                assistant_message = {"role": "assistant", "content": ""}
                total_tokens = 0
                
                async for chunk in response.aiter_bytes():
                    buffer += chunk.decode("utf-8")
                    
                    # 处理可能的多行数据
                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)
                        if line.startswith("data: "):
                            data = line[6:]  # 去掉 "data: " 前缀
                            if data == "[DONE]":
                                logger.info(f"Deepseek流式响应完成，总计tokens: {total_tokens}")
                                continue
                                
                            try:
                                json_data = json.loads(data)
                                if "choices" in json_data and len(json_data["choices"]) > 0:
                                    delta = json_data["choices"][0].get("delta", {})
                                    if "content" in delta and delta["content"]:
                                        content = delta["content"]
                                        assistant_message["content"] += content
                                        total_tokens += 1
                                        # 发送小块数据，改进流式体验
                                        yield f"data: {json.dumps({'content': content})}\n\n"
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析错误: {e}, 数据: {data[:100]}...")
                                continue
                
                # 确保最后的内容也被发送
                if buffer and buffer.startswith("data: "):
                    try:
                        data = buffer[6:]  # 去掉 "data: " 前缀
                        if data != "[DONE]":
                            json_data = json.loads(data)
                            if "choices" in json_data and len(json_data["choices"]) > 0:
                                delta = json_data["choices"][0].get("delta", {})
                                if "content" in delta and delta["content"]:
                                    content = delta["content"]
                                    yield f"data: {json.dumps({'content': content})}\n\n"
                    except Exception as e:
                        logger.warning(f"处理最后的缓冲区时出错: {e}")
                
                # 发送结束标记
                yield f"data: [DONE]\n\n"
    
    except Exception as e:
        logger.error(f"Deepseek API error: {str(e)}")
        yield f"data: {json.dumps({'error': str(e)})}\n\n"

async def qwen_stream_chat(messages, temperature=0.7, max_tokens=32000):
    """
    调用Qwen API进行流式聊天
    """
    try:
        # 构建请求体
        request_body = {
            "model": QWEN_MODEL,
            "messages": messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        # 发送请求 (示例，实际应根据Qwen API调整)
        async with httpx.AsyncClient(timeout=300.0) as client:
            async with client.stream(
                "POST",
                QWEN_API_URL,  # 使用环境变量中配置的API地址
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {QWEN_API_KEY}"
                }
            ) as response:
                if response.status_code != 200:
                    error_msg = await response.aread()
                    yield f"data: {json.dumps({'error': f'API错误: {error_msg}'})}\n\n"
                    return
                
                # 流式处理响应
                buffer = ""
                
                async for chunk in response.aiter_bytes():
                    buffer += chunk.decode("utf-8")
                    
                    # 处理可能的多行数据
                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)
                        if line.startswith("data: "):
                            data = line[6:]  # 去掉 "data: " 前缀
                            if data == "[DONE]":
                                continue
                                
                            try:
                                json_data = json.loads(data)
                                if "choices" in json_data and len(json_data["choices"]) > 0:
                                    delta = json_data["choices"][0].get("delta", {})
                                    if "content" in delta and delta["content"]:
                                        content = delta["content"]
                                        # 发送小块数据
                                        yield f"data: {json.dumps({'content': content})}\n\n"
                            except json.JSONDecodeError:
                                continue
                
    except Exception as e:
        logger.error(f"Qwen API error: {str(e)}")
        yield f"data: {json.dumps({'error': str(e)})}\n\n"

async def openai_stream_chat(messages, model="gpt-3.5-turbo", temperature=0.7, max_tokens=32000):
    """
    调用OpenAI API进行流式聊天
    """
    try:
        # 构建请求体
        request_body = {
            "model": model,
            "messages": messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        # 发送请求 (示例，实际应根据OpenAI API调整)
        async with httpx.AsyncClient(timeout=300.0) as client:
            async with client.stream(
                "POST",
                "https://api.openai.com/v1/chat/completions",
                json=request_body,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {get_openai_api_key()}"
                }
            ) as response:
                if response.status_code != 200:
                    error_msg = await response.aread()
                    yield f"data: {json.dumps({'error': f'API错误: {error_msg}'})}\n\n"
                    return
                
                # 流式处理响应
                buffer = ""
                
                async for chunk in response.aiter_bytes():
                    buffer += chunk.decode("utf-8")
                    
                    # 处理可能的多行数据
                    while "\n\n" in buffer:
                        line, buffer = buffer.split("\n\n", 1)
                        if line.startswith("data: "):
                            data = line[6:]  # 去掉 "data: " 前缀
                            if data == "[DONE]":
                                continue
                                
                            try:
                                json_data = json.loads(data)
                                if "choices" in json_data and len(json_data["choices"]) > 0:
                                    delta = json_data["choices"][0].get("delta", {})
                                    if "content" in delta and delta["content"]:
                                        content = delta["content"]
                                        # 发送小块数据
                                        yield f"data: {json.dumps({'content': content})}\n\n"
                            except json.JSONDecodeError:
                                continue
                
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        yield f"data: {json.dumps({'error': str(e)})}\n\n"

def get_openai_api_key():
    """
    获取OpenAI API密钥
    """
    # 实际应用中应从环境变量或配置文件中获取
    return "your_openai_api_key"

async def error_stream(error_message):
    """
    生成错误流
    """
    yield f"data: {json.dumps({'error': error_message})}\n\n"

# 获取聊天历史
@router.get("/chat/history")
async def get_chat_history(current_user: dict = Depends(get_current_user)):
    """
    获取当前用户的聊天历史
    """
    username = current_user.get("sub", "anonymous")
    
    # 如果用户没有聊天历史，返回空列表
    if username not in chat_history:
        return {"messages": []}
    
    # 获取最近的一个聊天历史
    if chat_history[username]:
        latest_chat_id = max(chat_history[username].keys(), key=lambda k: chat_history[username][k]["timestamp"])
        return {"messages": chat_history[username][latest_chat_id]["messages"]}
    else:
        return {"messages": []}

@router.delete("/chat/history/{chat_id}")
async def delete_chat_history(chat_id: str, current_user: dict = Depends(get_current_user)):
    """
    删除指定的聊天历史
    """
    username = current_user.get("sub", "anonymous")
    
    # 检查用户是否有聊天历史
    if username not in chat_history or chat_id not in chat_history[username]:
        raise HTTPException(status_code=404, detail="聊天历史不存在")
    
    # 删除聊天历史
    del chat_history[username][chat_id]
    
    return {"success": True, "message": "聊天历史已删除"}

# 会话管理API

@router.get("/chat/sessions")
async def get_sessions(current_user: dict = Depends(get_current_user)):
    """
    获取当前用户的所有会话
    """
    username = current_user.get("sub", "anonymous")
    
    # 如果用户没有会话，初始化一个空字典
    if username not in sessions:
        sessions[username] = {}
    
    # 将会话转换为列表并按更新时间排序
    user_sessions = []
    for session_id, session in sessions[username].items():
        user_sessions.append({
            "id": session_id,
            "title": session["title"],
            "preview": session["preview"],
            "created_at": session["created_at"],
            "updated_at": session["updated_at"]
        })
    
    # 按更新时间降序排序
    user_sessions.sort(key=lambda s: s["updated_at"], reverse=True)
    
    return {"sessions": user_sessions}

@router.post("/chat/sessions")
async def create_session(session: SessionCreate, current_user: dict = Depends(get_current_user)):
    """
    创建新会话
    """
    username = current_user.get("sub", "anonymous")
    
    # 如果用户没有会话，初始化一个空字典
    if username not in sessions:
        sessions[username] = {}
    
    # 生成会话ID
    session_id = str(uuid.uuid4())
    
    # 创建会话
    now = time.time()
    sessions[username][session_id] = {
        "title": session.title,
        "preview": "暂无内容",
        "messages": [],
        "created_at": now,
        "updated_at": now
    }
    
    # 返回新会话信息
    return {
        "success": True,
        "session": {
            "id": session_id,
            "title": session.title,
            "preview": "暂无内容",
            "created_at": now,
            "updated_at": now
        }
    }

@router.get("/chat/sessions/{session_id}")
async def get_session(session_id: str, current_user: dict = Depends(get_current_user)):
    """
    获取指定会话的详细信息
    """
    username = current_user.get("sub", "anonymous")
    
    # 检查会话是否存在
    if username not in sessions or session_id not in sessions[username]:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 获取会话信息
    session = sessions[username][session_id]
    
    return {
        "id": session_id,
        "title": session["title"],
        "preview": session["preview"],
        "messages": session["messages"],
        "created_at": session["created_at"],
        "updated_at": session["updated_at"]
    }

@router.delete("/chat/sessions/{session_id}")
async def delete_session(session_id: str, current_user: dict = Depends(get_current_user)):
    """
    删除指定会话
    """
    username = current_user.get("sub", "anonymous")
    
    # 检查会话是否存在
    if username not in sessions or session_id not in sessions[username]:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 删除会话
    del sessions[username][session_id]
    
    return {"success": True, "message": "会话已删除"}

@router.post("/chat/sessions/{session_id}/clear")
async def clear_session(session_id: str, current_user: dict = Depends(get_current_user)):
    """
    清空指定会话的所有消息
    """
    username = current_user.get("sub", "anonymous")
    
    # 检查会话是否存在
    if username not in sessions or session_id not in sessions[username]:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 清空会话消息
    sessions[username][session_id]["messages"] = []
    sessions[username][session_id]["preview"] = "暂无内容"
    sessions[username][session_id]["updated_at"] = time.time()
    
    return {"success": True, "message": "会话已清空"}

@router.put("/chat/sessions/{session_id}")
async def update_session(session_id: str, session: SessionUpdate, current_user: dict = Depends(get_current_user)):
    """
    更新指定会话的信息
    """
    username = current_user.get("sub", "anonymous")
    
    # 检查会话是否存在
    if username not in sessions or session_id not in sessions[username]:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 更新会话标题
    if session.title is not None:
        sessions[username][session_id]["title"] = session.title
    
    # 更新时间戳
    sessions[username][session_id]["updated_at"] = time.time()
    
    return {"success": True, "message": "会话已更新"}

@router.get("/auth/verify")
async def verify_token(current_user: dict = Depends(get_current_user)):
    """
    验证访问令牌
    """
    return {"valid": True, "user": current_user.get("sub", "anonymous")}

@router.get("/chat/info")
async def get_api_info():
    """
    获取API信息
    """
    return {
        "name": "金投大脑聊天API",
        "version": "1.0.0",
        "model": DEEPSEEK_MODEL,
        "api_url": DEEPSEEK_API_URL,
    }

@router.get("/status")
async def check_status():
    """
    检查API状态
    """
    try:
        # 简单的健康检查
        return {
            "status": "ok",
            "timestamp": time.time(),
            "version": "1.0.0",
            "services": {
                "api": "running",
                "deepseek": "connected"
            }
        }
    except Exception as e:
        logger.error(f"状态检查失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": time.time()
        }

@router.post("/login")
async def login(request: LoginRequest):
    """
    用户登录API
    """
    # 简单的用户验证逻辑，实际应用中应使用数据库和安全的密码存储
    valid_users = {
        "admin": "admin123",
        "test": "test123"
    }
    
    if request.username not in valid_users or valid_users[request.username] != request.password:
        raise HTTPException(status_code=401, detail="用户名或密码错误")
    
    # 生成令牌
    token = hashlib.sha256(f"{request.username}:{int(time.time())}".encode()).hexdigest()
    
    # 返回登录成功响应
    return {
        "success": True,
        "token": token,
        "username": request.username,
        "message": "登录成功"
    } 