<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>调试测试页面</h1>
    
    <div>
        <h2>1. 测试报表模板API</h2>
        <button onclick="testTemplates()">测试获取模板</button>
        <div id="templates-result"></div>
    </div>
    
    <div>
        <h2>2. 测试生成提示词API</h2>
        <button onclick="testGeneratePrompt()">测试生成提示词</button>
        <div id="prompt-result"></div>
    </div>
    
    <div>
        <h2>3. 测试千问API</h2>
        <button onclick="testQwenAPI()">测试千问API</button>
        <div id="qwen-result"></div>
    </div>

    <script>
        async function testTemplates() {
            const resultDiv = document.getElementById('templates-result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                console.log('开始测试模板API...');
                const response = await axios.get('http://localhost:8000/api/v1/document/report-templates');
                console.log('模板API响应:', response);
                
                resultDiv.innerHTML = `
                    <h3>✅ 成功</h3>
                    <p>状态码: ${response.status}</p>
                    <p>响应数据: <pre>${JSON.stringify(response.data, null, 2)}</pre></p>
                `;
            } catch (error) {
                console.error('模板API错误:', error);
                resultDiv.innerHTML = `
                    <h3>❌ 失败</h3>
                    <p>错误: ${error.message}</p>
                    <p>详情: <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre></p>
                `;
            }
        }
        
        async function testGeneratePrompt() {
            const resultDiv = document.getElementById('prompt-result');
            resultDiv.innerHTML = '正在测试...';
            
            const testData = {
                "selected_files": ["测试文件.pdf"],
                "template_name": "一般企业 (2).xls",
                "analysis_results": [
                    {
                        "filename": "测试文件.pdf",
                        "content": "这是测试内容",
                        "parsed_result": {
                            "是否为报表": "是",
                            "报表类型": "资产负债表",
                            "报表主体": "测试公司",
                            "报表时期": "2023年12月31日"
                        },
                        "selected": true
                    }
                ]
            };
            
            try {
                console.log('开始测试生成提示词API...');
                const response = await axios.post('http://localhost:8000/api/v1/document/generate-prompt', testData);
                console.log('生成提示词API响应:', response);
                
                resultDiv.innerHTML = `
                    <h3>✅ 成功</h3>
                    <p>状态码: ${response.status}</p>
                    <p>提示词长度: ${response.data.prompt?.length || 0} 字符</p>
                    <p>提示词预览: <pre>${(response.data.prompt || '').substring(0, 500)}...</pre></p>
                `;
            } catch (error) {
                console.error('生成提示词API错误:', error);
                resultDiv.innerHTML = `
                    <h3>❌ 失败</h3>
                    <p>错误: ${error.message}</p>
                    <p>详情: <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre></p>
                `;
            }
        }
        
        async function testQwenAPI() {
            const resultDiv = document.getElementById('qwen-result');
            resultDiv.innerHTML = '正在测试...';
            
            const testData = {
                "prompt": "请生成一个简单的资产负债表示例。",
                "template_name": "一般企业 (2).xls",
                "selected_files": ["测试文件.pdf"]
            };
            
            try {
                console.log('开始测试千问API...');
                const response = await axios.post('http://localhost:8000/api/v1/document/call-qwen-api', testData);
                console.log('千问API响应:', response);
                
                resultDiv.innerHTML = `
                    <h3>✅ 成功</h3>
                    <p>状态码: ${response.status}</p>
                    <p>AI回答长度: ${response.data.ai_response?.length || 0} 字符</p>
                    <p>AI回答预览: <pre>${(response.data.ai_response || '').substring(0, 500)}...</pre></p>
                `;
            } catch (error) {
                console.error('千问API错误:', error);
                resultDiv.innerHTML = `
                    <h3>❌ 失败</h3>
                    <p>错误: ${error.message}</p>
                    <p>详情: <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre></p>
                `;
            }
        }
        
        // 页面加载时自动测试模板
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testTemplates();
        };
    </script>
</body>
</html>
